<?php
require_once '../includes/auth.php';
require_once '../db_connection.php';
require_once '../includes/report_query_builder_semantic.php';

// --- Configuration & Setup ---
$pdo = db_connect();
require_page_access('dashboard_reports', 'view');


/**
 * Provides a centralized list of all system-defined, pre-built reports.
 * These reports use the new semantic format and are designed for management insights.
 *
 * @return array
 */
function get_prebuilt_reports(): array
{
    // This is the single source of truth for all system reports.
    // All definitions are in the new semantic format.
    return [
        // --- New Analytical & Trend Reports ---
        [
            'id' => 'downtime_by_device_this_year',
            'name' => 'ساعات توقف دستگاه‌ها (در سال جاری)',
            'description' => 'شناسایی دستگاه‌هایی که بیشترین زمان از کارافتادگی را داشته‌اند.',
            'definition_json' => json_encode([
                'rows' => ['device_name'],
                'columns' => ['downtime_hours'],
                'filters' => ['work_order_request_date' => ['period' => 'this_year']],
                'orderBy' => ['key' => 'downtime_hours', 'dir' => 'DESC'],
                'chartType' => 'bar'
            ]),
            'is_system' => true
        ],
        [
            'id' => 'root_cause_analysis_this_year',
            'name' => 'تحلیل دلایل اصلی خرابی (در سال جاری)',
            'description' => 'فراوانی دلایل ثبت‌شده برای خرابی‌ها جهت شناسایی مشکلات ریشه‌ای.',
            'definition_json' => json_encode([
                'rows' => ['problem_description'],
                'columns' => ['breakdown_count'],
                'filters' => ['breakdown_date' => ['period' => 'this_year']],
                'orderBy' => ['key' => 'breakdown_count', 'dir' => 'DESC'],
                'chartType' => 'pie'
            ]),
            'is_system' => true
        ],
        [
            'id' => 'planned_vs_unplanned_work',
            'name' => 'نسبت کارهای برنامه‌ریزی شده به اضطراری',
            'description' => 'مقایسه تعداد دستورکارهای پیشگیرانه، اصلاحی و اضطراری.',
            'definition_json' => json_encode([
                'rows' => ['work_order_type'],
                'columns' => ['work_order_count'],
                'filters' => ['work_order_request_date' => ['period' => 'this_year']],
                'chartType' => 'bar'
            ]),
            'is_system' => true
        ],
        [
            'id' => 'monthly_pm_completion_trend',
            'name' => 'روند انجام نت پیشگیرانه (PM)',
            'description' => 'تعداد دستورکارهای نت پیشگیرانه تکمیل‌شده در ماه‌های مختلف سال جاری.',
            'definition_json' => json_encode([
                'rows' => ['wo_jalali_month'],
                'columns' => ['work_order_count'],
                'filters' => [
                    'work_order_type' => 'پیشگیرانه',
                    'work_order_status' => 'تکمیل شده',
                    'work_order_request_date' => ['period' => 'this_year']
                ],
                'orderBy' => ['key' => 'wo_jalali_month', 'dir' => 'ASC'],
                'chartType' => 'line'
            ]),
            'is_system' => true
        ],
        [
            'id' => 'mtbf_mttr_trend',
            'name' => 'روند ماهانه MTBF و MTTR',
            'description' => 'مقایسه شاخص‌های قابلیت اطمینان و تعمیرپذیری در طول زمان.',
            'definition_json' => json_encode([
                'rows' => ['br_jalali_month'],
                'columns' => ['mtbf_hours', 'mttr_minutes'],
                'filters' => ['breakdown_date' => ['period' => 'this_year']],
                'orderBy' => ['key' => 'br_jalali_month', 'dir' => 'ASC'],
                'chartType' => 'line'
            ]),
            'is_system' => true
        ],

        // --- Original reports (with default time filters) ---
        [
            'id' => 'breakdown_count_by_device_last_90',
            'name' => 'تعداد خرابی دستگاه‌ها (۹۰ روز اخیر)',
            'description' => 'شناسایی دستگاه‌های پرریسک بر اساس تعداد خرابی ثبت‌شده.',
            'definition_json' => json_encode([
                'rows' => ['device_name'],
                'columns' => ['breakdown_count'],
                'filters' => ['breakdown_date' => ['period' => 'last_90_days']],
                'orderBy' => ['key' => 'breakdown_count', 'dir' => 'DESC'],
                'chartType' => 'bar'
            ]),
            'is_system' => true
        ],
        [
            'id' => 'work_order_count_by_status',
            'name' => 'تعداد دستورکار به تفکیک وضعیت',
            'description' => 'تعداد دستورکارهای صادرشده، در حال انجام و تکمیل‌شده.',
            'definition_json' => json_encode([
                'rows' => ['work_order_status'],
                'columns' => ['work_order_count'],
                'filters' => [],
                'chartType' => 'bar'
            ]),
            'is_system' => true
        ],
        [
            'id' => 'technician_labor_hours_this_month',
            'name' => 'ساعات کار تکنسین‌ها (ماه جاری)',
            'description' => 'سهم هر تکنسین در انجام فعالیت‌ها و ساعات کارکرد.',
            'definition_json' => json_encode([
                'rows' => ['technician_name'],
                'columns' => ['technician_labor_hours'],
                'filters' => ['work_order_request_date' => ['period' => 'this_month']],
                'orderBy' => ['key' => 'technician_labor_hours', 'dir' => 'DESC'],
                'chartType' => 'bar'
            ]),
            'is_system' => true
        ],
        
        // --- KPI Reports (unchanged) ---
        [
            'id' => 'mttr_overall',
            'name' => 'MTTR (میانگین زمان تعمیر) کل',
            'description' => 'ارزیابی سرعت رفع مشکل در کل سیستم (دقیقه).',
            'definition_json' => json_encode([
                'rows' => [],
                'columns' => ['mttr_minutes'],
                'filters' => ['work_order_request_date' => ['period' => 'this_year']],
                'chartType' => 'kpi'
            ]),
            'is_system' => true
        ],
        [
            'id' => 'breakdown_with_line_stop_percent',
            'name' => 'درصد خرابی با توقف خط',
            'description' => 'درصد خرابی‌هایی که منجر به توقف خط تولید شده‌اند.',
            'definition_json' => json_encode([
                'rows' => [],
                'columns' => ['breakdown_with_line_stop_percent'],
                'filters' => ['breakdown_date' => ['period' => 'this_year']],
                'chartType' => 'kpi'
            ]),
            'is_system' => true
        ],

        // --- گزارش‌های مدیریتی جدید ---
        [
            'id' => 'repair_reports_pending_approval',
            'name' => 'گزارش‌های تعمیر منتظر تایید',
            'description' => 'لیست دستورکارهایی که در وضعیت "منتظر تایید" قرار دارند و نیاز به تایید مدیریت دارند.',
            'definition_json' => json_encode([
                'rows' => ['workorder_id', 'work_order_title', 'device_name', 'work_order_request_date_shamsi', 'work_order_verifier_name', 'work_order_verifier_id', 'work_order_status'],
                'columns' => [],
                'filters' => ['work_order_status' => ['منتظر تایید']],
                'orderBy' => ['key' => 'work_order_request_date_shamsi', 'dir' => 'DESC'],
                'chartType' => 'approval_list'
            ]),
            'is_system' => true
        ],
        [
            'id' => 'open_work_orders',
            'name' => 'دستورکارهای باز',
            'description' => 'لیست دستورکارهایی که هنوز در حال انجام هستند و بسته نشده‌اند.',
            'definition_json' => json_encode([
                'rows' => ['workorder_id', 'work_order_title', 'device_name', 'work_order_request_date_shamsi', 'work_order_status', 'work_order_priority'],
                'columns' => [],
                'filters' => ['work_order_status' => ['دستورکار صادر شد', 'در حال انجام', 'پایان تعمیر', 'برون سپاری شد', 'پایان برون سپاری', 'برگشت جهت اصلاح']],
                'orderBy' => ['key' => 'work_order_request_date_shamsi', 'dir' => 'DESC'],
                'chartType' => 'clickable_list'
            ]),
            'is_system' => true
        ],
        [
            'id' => 'breakdown_reports_last_week',
            'name' => 'گزارش‌های خرابی یک هفته اخیر',
            'description' => 'لیست گزارش‌های خرابی ثبت شده در یک هفته گذشته با نمایش وضعیت فعلی.',
            'definition_json' => json_encode([
                'rows' => ['device_name', 'breakdown_problem_description', 'breakdown_date_shamsi', 'breakdown_status', 'breakdown_urgency'],
                'columns' => [],
                'filters' => ['breakdown_date' => ['period' => 'last_7_days']],
                'orderBy' => ['key' => 'breakdown_date_shamsi', 'dir' => 'DESC'],
                'chartType' => 'clickable_list'
            ]),
            'is_system' => true
        ],
    ];
}

// --- AJAX Endpoints ---
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    header('Content-Type: application/json; charset=utf-8');
    $user_id = current_user_id();
    $builder = new SemanticReportQueryBuilder();

    switch ($_POST['action']) {
        case 'get_report_data':
            $definition_json = $_POST['definition'] ?? '{}';
            $definition = json_decode($definition_json, true);

            if (json_last_error() !== JSON_ERROR_NONE || empty($definition)) {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'تعریف گزارش نامعتبر است.']);
                exit;
            }

            try {
                $result = $builder->execute($pdo, $definition);
                echo json_encode([
                    'success' => true,
                    'data' => $result,
                    'chartType' => $definition['chartType'] ?? 'table'
                ]);
            } catch (Throwable $e) {
                http_response_code(500);
                error_log("Report Execution Error: " . $e->getMessage() . "\nSQL: " . ($builder->buildSql($definition)['sql'] ?? 'N/A'));
                echo json_encode(['success' => false, 'message' => 'خطا در اجرای گزارش: ' . $e->getMessage()]);
            }
            break;

        case 'add':
            require_page_access('dashboard_reports', 'manage');
            $report_id = $_POST['report_id'] ?? 0;
            $is_system = isset($_POST['is_system']) && $_POST['is_system'] === 'true';
            $definition_json = $_POST['definition'] ?? '{}';
            $definition = json_decode($definition_json, true);
            $display_options = json_encode(['type' => $definition['chartType'] ?? 'table']);

            try {
                if ($is_system) {
                    $report_name = $_POST['name'] ?? 'گزارش سیستمی';
                    $report_desc = $_POST['description'] ?? '';
                    $stmt = $pdo->prepare('INSERT INTO report_definitions (name, description, owner_id, is_public, definition_json) VALUES (?,?,?,?,?)');
                    $stmt->execute([$report_name, $report_desc, $user_id, 0, $definition_json]);
                    $db_report_id = $pdo->lastInsertId();
                } else {
                    $db_report_id = (int)$report_id;
                }

                $stmt = $pdo->prepare('INSERT INTO dashboard_report_widgets (user_id, report_id, position, display_options) VALUES (?,?,?,?)');
                $stmt->execute([$user_id, $db_report_id, (int)($_POST['position'] ?? 99), $display_options]);
                $widget_id = $pdo->lastInsertId();

                echo json_encode(['success' => true, 'message' => 'ویجت به داشبورد اضافه شد.', 'widget_id' => $widget_id, 'db_report_id' => $db_report_id], JSON_UNESCAPED_UNICODE);
            } catch (Throwable $e) {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => $e->getMessage()], JSON_UNESCAPED_UNICODE);
            }
            break;

        case 'remove':
            require_page_access('dashboard_reports', 'manage');
            $widget_id = (int)($_POST['widget_id'] ?? 0);
            try {
                $stmt = $pdo->prepare('DELETE FROM dashboard_report_widgets WHERE id = ? AND user_id = ?');
                $stmt->execute([$widget_id, $user_id]);
                echo json_encode(['success' => true, 'message' => 'ویجت از داشبورد حذف شد.'], JSON_UNESCAPED_UNICODE);
            } catch (Throwable $e) {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => $e->getMessage()], JSON_UNESCAPED_UNICODE);
            }
            break;

        case 'reorder':
            require_page_access('dashboard_reports', 'manage');
            $positions = json_decode($_POST['positions'] ?? '[]', true) ?: [];
            try {
                $pdo->beginTransaction();
                $stmt = $pdo->prepare('UPDATE dashboard_report_widgets SET position = ? WHERE id = ? AND user_id = ?');
                foreach ($positions as $pos_data) {
                    $stmt->execute([(int)$pos_data['position'], (int)$pos_data['id'], $user_id]);
                }
                $pdo->commit();
                echo json_encode(['success' => true, 'message' => 'ترتیب ویجت‌ها ذخیره شد.'], JSON_UNESCAPED_UNICODE);
            } catch (Throwable $e) {
                $pdo->rollBack();
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => $e->getMessage()], JSON_UNESCAPED_UNICODE);
            }
            break;

        case 'update_options':
            require_page_access('dashboard_reports', 'manage');
            $widget_id = (int)($_POST['widget_id'] ?? 0);
            $display_options = $_POST['display_options'] ?? '{}';
            try {
                $stmt = $pdo->prepare('UPDATE dashboard_report_widgets SET display_options = ? WHERE id = ? AND user_id = ?');
                $stmt->execute([$display_options, $widget_id, $user_id]);
                echo json_encode(['success' => true, 'message' => 'نوع نمایش ذخیره شد.'], JSON_UNESCAPED_UNICODE);
            } catch (Throwable $e) {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => $e->getMessage()], JSON_UNESCAPED_UNICODE);
            }
            break;
			
        case 'delete_report':
            require_page_access('dashboard_reports', 'manage');
            $report_id = (int)($_POST['report_id'] ?? 0);
            $user_id = current_user_id();
            try {
                $pdo->beginTransaction();
                // First, delete any widgets using this report
                $stmt = $pdo->prepare('DELETE FROM dashboard_report_widgets WHERE report_id = ?');
                $stmt->execute([$report_id]);

                // Then, delete the report itself, ensuring it's owned by the current user
                $stmt = $pdo->prepare('DELETE FROM report_definitions WHERE id = ? AND owner_id = ?');
                $stmt->execute([$report_id, $user_id]);
                
                $pdo->commit();
                echo json_encode(['success' => true, 'message' => 'گزارش با موفقیت حذف شد.'], JSON_UNESCAPED_UNICODE);
            } catch (Throwable $e) {
                $pdo->rollBack();
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => $e->getMessage()], JSON_UNESCAPED_UNICODE);
            }
            break;		
    }
    exit;
}

// --- Page Data Fetching ---
include '../includes/header.php';

// مرحله ۱: گزارش‌های پیش‌فرض (سیستمی) را از تابع دریافت کنید
$system_reports = get_prebuilt_reports();

// مرحله ۲: لیستی از نام‌های گزارش‌های سیستمی برای استفاده در فیلتر ایجاد کنید
$system_report_names = array_column($system_reports, 'name');

// مرحله ۳: گزارش‌های ثبت‌شده در دیتابیس را واکشی کنید
$userReportsStmt = $pdo->prepare("SELECT id, name, description, definition_json FROM report_definitions WHERE owner_id = ? OR is_public = 1 ORDER BY name ASC");
$userReportsStmt->execute([current_user_id()]);
$db_reports = $userReportsStmt->fetchAll(PDO::FETCH_ASSOC);

// مرحله ۴: گزارش‌های دیتابیس را فیلتر کنید تا کپی‌های گزارش‌های سیستمی حذف شوند
// فقط گزارش‌هایی باقی می‌مانند که نامشان در لیست گزارش‌های سیستمی وجود ندارد
$user_only_reports = array_filter($db_reports, function($report) use ($system_report_names) {
    return !in_array($report['name'], $system_report_names);
});

// مرحله ۵: لیست نهایی را از ادغام گزارش‌های سیستمی و گزارش‌های خالص کاربری ایجاد کنید
$all_available_reports = array_merge($system_reports, $user_only_reports);

$widgetsStmt = $pdo->prepare("SELECT w.id, w.position, w.display_options, r.id as report_id, r.name, r.description 
                             FROM dashboard_report_widgets w 
                             JOIN report_definitions r ON r.id = w.report_id 
                             WHERE w.user_id = ? 
                             ORDER BY w.position ASC, w.id ASC");
$widgetsStmt->execute([current_user_id()]);
$widgets = $widgetsStmt->fetchAll(PDO::FETCH_ASSOC);

?>
<!DOCTYPE html>
<html lang="fa" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>سفارشی‌سازی داشبورد</title>
	<script src="../assets/js/chart.min.js"></script>
    <link rel="stylesheet" href="../assets/css/all.min.css">
    <style>
        
        /* --- General Styles --- */
        body { 
            font-family: 'Vazirmatn', sans-serif; 
            background-color: #f1f5f9;
            color: #333;
            margin: 0;
        }
        .container {
            max-width: 1280px;
            margin: 0 auto;
            padding: 1.5rem;
        }

        /* --- Layout Grid --- */
        .layout-grid {
            display: grid;
            gap: 1.5rem;
            grid-template-columns: 1fr;
        }
        @media (min-width: 1024px) {
            .layout-grid {
                grid-template-columns: 1fr 2fr;
            }
        }

        /* --- Card Styles --- */
        .card {
            background-color: white;
            padding: 1.5rem;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.08);
			display: block;
        }
		.card:hover {
    transform: none;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}
        .card-header {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            font-size: 1.25rem;
            font-weight: bold;
            color: #1f2937;
            margin-bottom: 1.5rem;
        }
        .card-header i {
            color: #4f46e5;
        }
        
        /* --- Available Reports List --- */
        #available-reports-list {
            max-height: 70vh;
            overflow-y: auto;
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }
        .report-item {
            padding: 0.75rem;
            border-radius: 8px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        .report-item:hover {
            background-color: #f4f4f5;
        }
        .report-item h4 {
            font-weight: 600;
            color: #1f2937;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        .report-item p {
            font-size: 0.85rem;
            color: #6b7280;
            padding-right: 0.25rem;
        }
        .report-item .fa-star {
            color: #facc15;
        }
        .report-item .fa-eye {
            color: #6366f1;
            font-size: 1.25rem;
        }

        /* --- Widgets List --- */
        #widgets-list {
            display: flex;
            flex-direction: column;
            gap: 0.75rem;
            min-height: 200px;
        }
        .widget-item {
            padding: 0.75rem;
            border-radius: 8px;
            background-color: white;
            border: 1px solid #e5e7eb;
            box-shadow: 0 1px 3px rgba(0,0,0,0.05);
        }
        .widget-item.dragging { 
            opacity: 0.5; 
            background-color: #e0e7ff; 
            border-style: dashed; 
        }
        .widget-header {
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }
        .drag-handle {
            color: #9ca3af;
            cursor: move;
        }
        .widget-info {
            flex-grow: 1;
        }
        .widget-info h4 {
            font-weight: bold;
            color: #1f2937;
        }
        .widget-info p {
            font-size: 0.85rem;
            color: #6b7280;
        }
        .remove-btn {
            color: #ef4444;
            background: none;
            border: none;
            cursor: pointer;
            font-size: 1rem;
        }
        .remove-btn:hover {
            color: #b91c1c;
        }
        .widget-options {
            margin-top: 0.75rem;
            padding-top: 0.75rem;
            border-top: 1px solid #f3f4f6;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        .widget-options-label {
            font-size: 0.75rem;
            font-weight: 600;
            color: #6b7280;
        }
        .display-options-group {
            display: flex;
            align-items: center;
            gap: 0.25rem;
            background-color: #252525;
            border-radius: 9999px;
            padding: 0.25rem;
        }
        .display-option {
            height: 2rem;
            width: 2rem;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            background: none;
            border: none;
            cursor: pointer;
            transition: background-color 0.2s, color 0.2s;
        }
        .display-option:hover {
            background-color: #777777;
        }
        .display-option.selected {
            background-color: #4f46e5;
            color: white;
        }
        #widgets-empty-state {
            text-align: center;
            color: #6b7280;
            padding: 2rem;
            background-color: #f9fafb;
            border-radius: 8px;
            min-height: 200px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }
        #widgets-empty-state i {
            font-size: 3rem;
            margin-bottom: 1rem;
            color: #d1d5db;
        }
        #widgets-empty-state p:first-of-type {
            font-weight: 600;
            font-size: 1.1rem;
        }

        /* --- Preview Modal --- */
        #preview-modal {
            position: fixed;
            inset: 0;
            background-color: rgba(17, 24, 39, 0.75);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 50;
            padding: 1rem;
        }
        #preview-modal.hidden {
            display: none;
        }
        .modal-content {
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 20px 25px rgba(0,0,0,0.1);
            padding: 1.5rem;
            width: 100%;
            max-width: 56rem; /* max-w-3xl */
            display: flex;
            flex-direction: column;
        }
        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }
        .modal-header h3 {
            font-size: 1.25rem;
            font-weight: bold;
        }
        .modal-close-btn {
            font-size: 1.5rem;
            color: #6b7280;
            background: none;
            border: none;
            cursor: pointer;
        }
        .modal-close-btn:hover {
            color: #1f2937;
        }
        #preview-content {
            min-height: 400px;
            position: relative;
            flex-grow: 1;
        }
        .modal-footer {
            margin-top: 1.5rem;
            display: flex;
            justify-content: flex-end;
            gap: 0.75rem;
        }
        .btn {
            padding: 0.6rem 1.25rem;
            border-radius: 8px;
            border: none;
            cursor: pointer;
            font-weight: 600;
        }
        .btn-secondary {
            background-color: #e5e7eb;
            color: #1f2937;
        }
        .btn-secondary:hover {
            background-color: #d1d5db;
        }
        .btn-primary {
            background-color: #4f46e5;
            color: white;
        }
        .btn-primary:hover {
            background-color: #4338ca;
        }
        .btn-primary i {
            margin-right: 0.5rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="layout-grid">
            <aside>
                <div class="card">
                    <h3 class="card-header"><i class="fas fa-plus-circle"></i> گزارش‌های موجود</h3>
                    <div id="available-reports-list">
    <?php foreach ($all_available_reports as $r): ?>
        <div class="report-item" 
             data-report-id="<?= htmlspecialchars($r['id'] ?? '') ?>"
             data-report-name="<?= htmlspecialchars($r['name']) ?>"
             data-report-desc="<?= htmlspecialchars($r['description']) ?>"
             data-report-definition='<?= htmlspecialchars($r['definition_json']) ?>'
             data-is-system="<?= isset($r['is_system']) && $r['is_system'] ? 'true' : 'false' ?>">
            
            <div onclick="showPreview(this.closest('.report-item'))" style="flex-grow: 1; cursor: pointer;">
                <h4>
                    <?php if(isset($r['is_system']) && $r['is_system']): ?><i class="fas fa-star" title="گزارش سیستمی"></i><?php endif; ?>
                    <?= htmlspecialchars($r['name']) ?>
                </h4>
                <?php if ($r['description']): ?><p><?= htmlspecialchars($r['description']) ?></p><?php endif; ?>
            </div>
            
            <div style="display: flex; align-items: center; gap: 1rem;">
                <i class="fas fa-eye" title="مشاهده پیش‌نمایش" onclick="showPreview(this.closest('.report-item'))" style="cursor: pointer;"></i>
                <?php if(!isset($r['is_system']) || !$r['is_system']): ?>
                    <button class="remove-btn" title="حذف گزارش" onclick="confirmDeleteReport(this.closest('.report-item'))">
                        <i class="fas fa-trash-alt"></i>
                    </button>
                <?php endif; ?>
            </div>
        </div>
    <?php endforeach;  ?>
</div>
                </div>
            </aside>
            <main>
                <div class="card">
                    <h3 class="card-header"><i class="fas fa-tachometer-alt"></i> ویجت‌های داشبورد</h3>
                    <div id="widgets-list">
                        <?php if (empty($widgets)): ?>
                            <div id="widgets-empty-state">
                                <i class="fas fa-grip-horizontal"></i>
                                <p>داشبورد شما خالی است</p>
                                <p>برای افزودن، از لیست گزارش‌های موجود در سمت راست، روی یک گزارش کلیک کنید</p>
                            </div>
                        <?php else: ?>
                            <?php foreach ($widgets as $w):
                                $opts = json_decode($w['display_options'] ?? '{}', true);
                                $type = $opts['type'] ?? 'table';
                            ?>
                                <div class="widget-item" draggable="true" data-widget-id="<?= $w['id'] ?>" data-report-id="<?= $w['report_id'] ?>">
                                    <div class="widget-header">
                                        <i class="drag-handle fas fa-grip-vertical"></i>
                                        <div class="widget-info">
                                            <h4><?= htmlspecialchars($w['name']) ?></h4>
                                            <?php if ($w['description']): ?><p><?= htmlspecialchars($w['description']) ?></p><?php endif; ?>
                                        </div>
                                        <button class="remove-btn" onclick="confirmRemove(this)"><i class="fas fa-trash-alt"></i></button>
                                    </div>
                                    <div class="widget-options">
                                        <span class="widget-options-label">نوع نمایش:</span>
                                        <div class="display-options-group">
                                            <button onclick="updateWidgetOptions(this, 'bar')" class="display-option <?= $type === 'bar' ? 'selected' : '' ?>" title="نمودار ستونی"><i class="fas fa-chart-bar"></i></button>
                                            <button onclick="updateWidgetOptions(this, 'pie')" class="display-option <?= $type === 'pie' ? 'selected' : '' ?>" title="نمودار دایره‌ای"><i class="fas fa-chart-pie"></i></button>
                                            <button onclick="updateWidgetOptions(this, 'line')" class="display-option <?= $type === 'line' ? 'selected' : '' ?>" title="نمودار خطی"><i class="fas fa-chart-line"></i></button>
                                            <button onclick="updateWidgetOptions(this, 'kpi')" class="display-option <?= $type === 'kpi' ? 'selected' : '' ?>" title="کارت KPI"><i class="fas fa-tachometer-alt-fast"></i></button>
                                            <button onclick="updateWidgetOptions(this, 'table')" class="display-option <?= $type === 'table' ? 'selected' : '' ?>" title="جدول"><i class="fas fa-table"></i></button>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <div id="preview-modal" class="hidden">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="preview-title">پیش‌نمایش گزارش</h3>
                <button onclick="closePreviewModal()" class="modal-close-btn">&times;</button>
            </div>
            <div id="preview-content"></div>
            <div class="modal-footer">
                <button onclick="closePreviewModal()" class="btn btn-secondary">بستن</button>
                <button id="add-from-preview-btn" class="btn btn-primary"><i class="fas fa-plus"></i>افزودن به داشبورد</button>
            </div>
        </div>
    </div>

<script>
// The Javascript logic remains the same as it does not depend on Tailwind.
// ... (All previous Javascript code is here)
document.addEventListener('DOMContentLoaded', () => {
    const previewModal = document.getElementById('preview-modal');
    const previewContent = document.getElementById('preview-content');
    const previewTitle = document.getElementById('preview-title');
    const addFromPreviewBtn = document.getElementById('add-from-preview-btn');
    let currentReportElement = null;
    let chartInstance = null;

    window.showPreview = async (reportElement) => {
        currentReportElement = reportElement;
        previewTitle.innerText = `پیش‌نمایش: ${reportElement.dataset.reportName}`;
        previewModal.classList.remove('hidden');
        previewContent.innerHTML = '<div style="display:flex; align-items:center; justify-content:center; height:100%;"><i class="fas fa-spinner fa-spin" style="font-size: 3rem; color: #4f46e5;"></i><p style="margin-right: 1rem;">در حال بارگذاری داده‌ها...</p></div>';

        if (chartInstance) chartInstance.destroy();

        const formData = new FormData();
        formData.append('action', 'get_report_data');
        formData.append('definition', reportElement.dataset.reportDefinition);

        const response = await apiFetch(formData);
        if (response?.success) {
            renderPreview(response);
        } else {
            previewContent.innerHTML = `<div style="text-align:center; padding: 2.5rem; color: #ef4444;"><p style="font-weight:bold;">خطا در بارگذاری پیش‌نمایش</p><p style="font-size:0.9rem; margin-top:0.5rem;">${response?.message || 'خطای نامشخص'}</p></div>`;
        }
    };

    window.closePreviewModal = () => {
        previewModal.classList.add('hidden');
        if (chartInstance) {
            chartInstance.destroy();
            chartInstance = null;
        }
        currentReportElement = null;
    };

    addFromPreviewBtn.onclick = () => {
        if (currentReportElement) {
            addToDashboard(currentReportElement);
            closePreviewModal();
        }
    };

    function renderPreview(response) {
        const { data, chartType } = response;
        const { columns, rows, warning } = data;
        previewContent.innerHTML = '';

        if (warning) {
            const warningDiv = document.createElement('div');
            warningDiv.style.cssText = 'padding: 0.5rem; margin-bottom: 0.5rem; font-size: 0.85rem; color: #92400e; background-color: #fef3c7; border-radius: 8px;';
            warningDiv.innerText = warning;
            previewContent.appendChild(warningDiv);
        }

        if (!rows || rows.length === 0) {
            previewContent.innerHTML += '<p style="text-align:center; padding: 2.5rem; color: #6b7280;">داده‌ای برای نمایش در این بازه زمانی وجود ندارد.</p>';
            return;
        }

        if (chartType === 'kpi') {
            let kpiHtml = '<div style="display:flex; flex-wrap:wrap; justify-content:space-around; align-items:center; height:100%; gap:1rem;">';
            columns.forEach(col => {
                const value = rows[0][col.key] ?? 'N/A';
                 kpiHtml += `
                    <div style="text-align:center; padding:1rem;">
                        <h4 style="font-size:1.1rem; color:#6b7280;">${col.label}</h4>
                        <p style="font-size:3rem; font-weight:bold; color:#4f46e5;">${Number(value).toLocaleString()}</p>
                    </div>
                `;
            });
            kpiHtml += '</div>';
            previewContent.innerHTML += kpiHtml;
            return;
        }

        if (chartType === 'table') {
             let tableHtml = '<div style="overflow:auto; height:100%;"><table style="width:100%; font-size:0.9rem; text-align:left; color:#6b7280; border-collapse:collapse;"><thead><tr style="font-size:0.75rem; color:#374151; text-transform:uppercase; background-color:#f9fafb;">';
             columns.forEach(col => tableHtml += `<th scope="col" style="padding:0.75rem 1.5rem;">${col.label}</th>`);
             tableHtml += '</tr></thead><tbody>';
             rows.forEach(row => {
                 tableHtml += '<tr style="background-color:white; border-bottom:1px solid #f3f4f6;">';
                 columns.forEach(col => tableHtml += `<td style="padding:1rem 1.5rem;">${row[col.key] ?? ''}</td>`);
                 tableHtml += '</tr>';
             });
             tableHtml += '</tbody></table></div>';
             previewContent.innerHTML += tableHtml;
             return;
        }
        
        const canvasContainer = document.createElement('div');
        canvasContainer.style.cssText = "position:relative; height:400px;";
        const canvas = document.createElement('canvas');
        canvasContainer.appendChild(canvas);
        previewContent.appendChild(canvasContainer);

        // --- Start of Revised Chart Logic ---

        const labels = rows.map(row => row[columns[0].key]);
        const datasets = [];
        
        // Use a consistent, pleasant color palette
        const baseColors = [
            'rgba(79, 70, 229, 0.7)', 'rgba(52, 211, 153, 0.7)', 'rgba(245, 158, 11, 0.7)', 
            'rgba(239, 68, 68, 0.7)', 'rgba(99, 102, 241, 0.7)', 'rgba(168, 85, 247, 0.7)',
            'rgba(236, 72, 153, 0.7)', 'rgba(16, 185, 129, 0.7)'
        ];
        const borderColors = baseColors.map(c => c.replace('0.7', '1'));

        const numberOfMeasures = columns.length - 1;

        // Logic for coloring based on chart type and number of measures
        if ((chartType === 'bar' && numberOfMeasures === 1) || chartType === 'pie' || chartType === 'doughnut') {
            // Case 1: Pie/Doughnut charts, or a Bar chart with a single measure.
            // We want each slice/bar to have a different color.
            datasets.push({
                label: columns[1].label,
                data: rows.map(row => row[columns[1].key]),
                backgroundColor: labels.map((_, i) => baseColors[i % baseColors.length]), // Array of colors
                borderColor: labels.map((_, i) => borderColors[i % borderColors.length]),
                borderWidth: 1
            });
        } else {
            // Case 2: Line chart or grouped Bar chart (multiple measures).
            // We want each measure (dataset) to have its own single color.
            for (let i = 1; i < columns.length; i++) {
                datasets.push({
                    label: columns[i].label,
                    data: rows.map(row => row[columns[i].key]),
                    backgroundColor: baseColors[(i - 1) % baseColors.length], // Single color
                    borderColor: borderColors[(i - 1) % borderColors.length],
                    borderWidth: chartType === 'line' ? 2 : 1,
                    fill: chartType === 'line',
                    tension: 0.1
                });
            }
        }

        chartInstance = new Chart(canvas, {
            type: ['bar', 'pie', 'doughnut', 'line'].includes(chartType) ? chartType : 'bar',
            data: { labels, datasets },
            options: { 
                responsive: true, 
                maintainAspectRatio: false,
                scales: (chartType === 'bar' || chartType === 'line') ? { y: { beginAtZero: true } } : {},
                plugins: { 
                    legend: { 
                        // Show legend if it's a pie chart or if there are multiple datasets
                        display: chartType === 'pie' || chartType === 'doughnut' || datasets.length > 1 
                    } 
                }
            }
        });
        // --- End of Revised Chart Logic ---
    }
    
    const DOMElements = {
        widgetsList: document.getElementById('widgets-list'),
        widgetsEmptyState: document.getElementById('widgets-empty-state'),
    };

    const apiFetch = async (formData) => {
        try {
            const response = await fetch('dashboard_reports.php', { method: 'POST', body: formData });
            const data = await response.json();
            if (!response.ok) throw new Error(data.message || 'خطای ناشناخته در سرور');
            return data;
        } catch (error) {
            console.error("API Fetch Error:", error);
            alert(`خطا: ${error.message}`);
            return null;
        }
    };

    window.addToDashboard = async (reportElement) => {
        const formData = new FormData();
        formData.append('action', 'add');
        formData.append('report_id', reportElement.dataset.reportId);
        formData.append('name', reportElement.dataset.reportName);
        formData.append('description', reportElement.dataset.reportDesc);
        formData.append('definition', reportElement.dataset.reportDefinition);
        formData.append('is_system', reportElement.dataset.isSystem);
        formData.append('position', DOMElements.widgetsList.children.length);
        
        const data = await apiFetch(formData);
        if (data?.success) {
            const def = JSON.parse(reportElement.dataset.reportDefinition);
            const newWidget = createWidgetElement({
                id: data.widget_id,
                report_id: data.db_report_id,
                name: reportElement.dataset.reportName,
                description: reportElement.dataset.reportDesc,
                display_options: JSON.stringify({ type: def.chartType || 'table' })
            });
            DOMElements.widgetsEmptyState?.remove();
            DOMElements.widgetsList.appendChild(newWidget);
            alert('گزارش با موفقیت به داشبورد اضافه شد.');
        }
    };
    window.confirmDeleteReport = async (reportElement) => {
        const reportName = reportElement.dataset.reportName;
        if (!confirm(`آیا از حذف گزارش "${reportName}" مطمئن هستید؟ این عمل غیرقابل بازگشت است.`)) return;

        const formData = new FormData();
        formData.append('action', 'delete_report');
        formData.append('report_id', reportElement.dataset.reportId);
        
        const data = await apiFetch(formData);
        if (data?.success) {
            reportElement.remove();
            alert(data.message);
        }
    };

    window.confirmRemove = async (buttonElement) => {
        if (!confirm('آیا از حذف این ویجت مطمئن هستید؟')) return;
        const widgetElement = buttonElement.closest('.widget-item');
        const formData = new FormData();
        formData.append('action', 'remove');
        formData.append('widget_id', widgetElement.dataset.widgetId);
        
        const data = await apiFetch(formData);
        if (data?.success) {
            widgetElement.remove();
            if (DOMElements.widgetsList.children.length === 0 && !document.getElementById('widgets-empty-state')) {
                DOMElements.widgetsList.innerHTML = `<div id="widgets-empty-state">
                    <i class="fas fa-grip-horizontal"></i>
                    <p>داشبورد شما خالی است</p>
                    <p>برای افزودن، از لیست گزارش‌های موجود کلیک کنید</p>
                </div>`;
            }
        }
    };

    window.updateWidgetOptions = async (buttonElement, type) => {
        const widgetElement = buttonElement.closest('.widget-item');
        const formData = new FormData();
        formData.append('action', 'update_options');
        formData.append('widget_id', widgetElement.dataset.widgetId);
        formData.append('display_options', JSON.stringify({ type: type }));
        
        const data = await apiFetch(formData);
        if (data?.success) {
            const parent = buttonElement.parentElement;
            parent.querySelectorAll('.display-option').forEach(btn => btn.classList.remove('selected'));
            buttonElement.classList.add('selected');
        }
    };

    const createWidgetElement = (widgetData) => {
        const widget = document.createElement('div');
        widget.className = 'widget-item';
        widget.draggable = true;
        widget.dataset.widgetId = widgetData.id;
        widget.dataset.reportId = widgetData.report_id;
        const opts = JSON.parse(widgetData.display_options ?? '{}');
        const type = opts.type ?? 'table';
        widget.innerHTML = `
            <div class="widget-header">
                <i class="drag-handle fas fa-grip-vertical"></i>
                <div class="widget-info">
                    <h4>${widgetData.name}</h4>
                    <p>${widgetData.description || ''}</p>
                </div>
                <button class="remove-btn" onclick="confirmRemove(this)"><i class="fas fa-trash-alt"></i></button>
            </div>
            <div class="widget-options">
                <span class="widget-options-label">نوع نمایش:</span>
                <div class="display-options-group">
                    <button onclick="updateWidgetOptions(this, 'bar')" class="display-option ${type === 'bar' ? 'selected' : ''}" title="نمودار ستونی"><i class="fas fa-chart-bar"></i></button>
                    <button onclick="updateWidgetOptions(this, 'pie')" class="display-option ${type === 'pie' ? 'selected' : ''}" title="نمودار دایره‌ای"><i class="fas fa-chart-pie"></i></button>
                    <button onclick="updateWidgetOptions(this, 'line')" class="display-option ${type === 'line' ? 'selected' : ''}" title="نمودار خطی"><i class="fas fa-chart-line"></i></button>
                    <button onclick="updateWidgetOptions(this, 'kpi')" class="display-option ${type === 'kpi' ? 'selected' : ''}" title="کارت KPI"><i class="fas fa-tachometer-alt-fast"></i></button>
                    <button onclick="updateWidgetOptions(this, 'table')" class="display-option ${type === 'table' ? 'selected' : ''}" title="جدول"><i class="fas fa-table"></i></button>
                </div>
            </div>
        `;
        return widget;
    };
    
    let draggedElement = null;
    DOMElements.widgetsList.addEventListener('dragstart', e => {
        if (e.target.classList.contains('widget-item')) {
            draggedElement = e.target;
            setTimeout(() => draggedElement.classList.add('dragging'), 0);
        }
    });
    DOMElements.widgetsList.addEventListener('dragend', async e => {
        if (draggedElement) {
            draggedElement.classList.remove('dragging');
            draggedElement = null;
            const positions = [];
            DOMElements.widgetsList.querySelectorAll('.widget-item').forEach((item, index) => {
                positions.push({ id: item.dataset.widgetId, position: index });
            });
            const formData = new FormData();
            formData.append('action', 'reorder');
            formData.append('positions', JSON.stringify(positions));
            await apiFetch(formData);
        }
    });
    DOMElements.widgetsList.addEventListener('dragover', e => {
        e.preventDefault();
        const afterElement = getDragAfterElement(DOMElements.widgetsList, e.clientY);
        if (draggedElement) {
            if (afterElement == null) {
                DOMElements.widgetsList.appendChild(draggedElement);
            } else {
                DOMElements.widgetsList.insertBefore(draggedElement, afterElement);
            }
        }
    });
    function getDragAfterElement(container, y) {
        const draggableElements = [...container.querySelectorAll('.widget-item:not(.dragging)')];
        return draggableElements.reduce((closest, child) => {
            const box = child.getBoundingClientRect();
            const offset = y - box.top - box.height / 2;
            if (offset < 0 && offset > closest.offset) {
                return { offset: offset, element: child };
            } else {
                return closest;
            }
        }, { offset: Number.NEGATIVE_INFINITY }).element;
    }
});
</script>
</body>
</html>
