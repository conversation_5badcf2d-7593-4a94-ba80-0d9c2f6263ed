<?php
// includes/date_helper.php

require_once 'jdf.php';

/**
 * تبدیل اعداد فارسی و عربی به انگلیسی
 * @param string $string
 * @return string
 */
function convert_digits($string) {
    $persianDigits = ['۰', '۱', '۲', '۳', '۴', '۵', '۶', '۷', '۸', '۹'];
    $arabicDigits  = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
    $englishDigits = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];
    
    $string = str_replace($persianDigits, $englishDigits, $string);
    $string = str_replace($arabicDigits, $englishDigits, $string);
    
    return $string;
}

/**
 * اعتبارسنجی تاریخ شمسی
 * @param int $year
 * @param int $month
 * @param int $day
 * @return bool
 */
function validate_shamsi_date($year, $month, $day) {
    // اعتبارسنجی ماه
    if ($month < 1 || $month > 12) {
        return false;
    }
    
    // تعریف تعداد روزهای هر ماه
    $daysInMonth = [
        1 => 31,   // فروردین
        2 => 31,   // اردیبهشت
        3 => 31,   // خرداد
        4 => 31,   // تیر
        5 => 31,   // مرداد
        6 => 31,   // شهریور
        7 => 30,   // مهر
        8 => 30,   // آبان
        9 => 30,   // آذر
        10 => 30,  // دی
        11 => 30,  // بهمن
        12 => ($year % 4 === 3) ? 30 : 29 // اسفند
    ];
    
    // اعتبارسنجی روز
    return ($day >= 1 && $day <= $daysInMonth[$month]);
}

/**
 * تبدیل تاریخ میلادی به شمسی
 * @param string|null $gregorian_date تاریخ میلادی (Y-m-d or Y-m-d H:i:s)
 * @param string $format فرمت خروجی برای jdate
 * @return string
 */
function to_shamsi($gregorian_date, $format = 'Y/m/d') {
    // بررسی مقادیر نامعتبر
    if (empty($gregorian_date) || 
        $gregorian_date === '0000-00-00' || 
        substr($gregorian_date, 0, 10) === '0000-00-00') {
        return '-';
    }
    
    // تبدیل کل تاریخ و زمان به timestamp
    $timestamp = strtotime($gregorian_date);
    if ($timestamp === false) {
        return '-'; // در صورت نامعتبر بودن تاریخ ورودی
    }
    
    return jdate($format, $timestamp);
}

/**
 * تبدیل تاریخ شمسی به میلادی (بدون زمان)
 * @param string|null $shamsi_date تاریخ شمسی (فرمت: Y/m/d)
 * @return string|null
 */
function to_miladi($shamsi_date) {
    // بررسی مقادیر خالی
    if (empty($shamsi_date) || trim($shamsi_date) === '') {
        return null;
    }
    
    // تبدیل اعداد و پاکسازی
    $cleanDate = convert_digits($shamsi_date);
    $cleanDate = preg_replace('/[^0-9\/]/', '', $cleanDate);
    $cleanDate = str_replace('-', '/', $cleanDate);
    
    // استخراج اجزای تاریخ
    $parts = explode('/', $cleanDate);
    
    // اعتبارسنجی فرمت
    if (count($parts) !== 3) {
        error_log("[Date Helper] Invalid date format: $shamsi_date");
        return null;
    }
    
    list($year, $month, $day) = array_map('intval', $parts);
    
    // اعتبارسنجی محدوده سال
    if ($year < 1300 || $year > 1500) {
        error_log("[Date Helper] Year out of range: $year");
        return null;
    }
    
    // اعتبارسنجی پیشرفته تاریخ
    if (!validate_shamsi_date($year, $month, $day)) {
        error_log("[Date Helper] Invalid Shamsi date: $year/$month/$day");
        return null;
    }
    
    // تبدیل تاریخ شمسی به میلادی
    $miladi_array = jalali_to_gregorian($year, $month, $day);
    
    if (count($miladi_array) !== 3) {
        error_log("[Date Helper] Conversion failed for: $year/$month/$day");
        return null;
    }
    
    // فرمت‌دهی خروجی
    return sprintf('%04d-%02d-%02d', 
        $miladi_array[0], 
        $miladi_array[1], 
        $miladi_array[2]
    );
}

if (!function_exists('format_shamsi_datetime')) {
    /**
     * فرمت تاریخ و ساعت شمسی با اعداد فارسی (مانند reports_list.php)
     * @param string|null $datetime_string
     * @return string
     */
    function format_shamsi_datetime($datetime_string) {
        if (empty($datetime_string) || (is_string($datetime_string) && (str_starts_with($datetime_string, '0000-00-00') || $datetime_string === '0000-00-00 00:00:00'))) {
            return '-';
        }
        try {
            $date_obj = new DateTime($datetime_string);
            // تاریخ شمسی از کتابخانه دریافت می‌شود
            $shamsi_date = to_shamsi($datetime_string);
            // زمان با استفاده از کلاس داخلی PHP استخراج می‌شود
            $time = $date_obj->format('H:i');
            // تبدیل اعداد به فارسی
            $persian_digits = ['۰', '۱', '۲', '۳', '۴', '۵', '۶', '۷', '۸', '۹'];
            $english_digits = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];
            $time_fa = str_replace($english_digits, $persian_digits, $time);
            return $shamsi_date . '، ساعت ' . $time_fa;
        } catch (Exception $e) {
            return to_shamsi($datetime_string);
        }
    }
}