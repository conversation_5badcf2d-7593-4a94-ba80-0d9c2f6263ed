-- ایجاد پایگاه داده با تنظیمات فارسی
CREATE DATABASE IF NOT EXISTS net_dombaz CHARACTER SET utf8mb4 COLLATE utf8mb4_persian_ci;
USE net_dombaz;

-- جدول نقش‌های کاربری
CREATE TABLE IF NOT EXISTS roles (
  id INT AUTO_INCREMENT PRIMARY KEY,
  name VARCHAR(50) NOT NULL UNIQUE,
  display_name VARCHAR(100) NOT NULL,
  description TEXT NULL,
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_persian_ci;

-- درج نقش‌های پیش‌فرض
INSERT INTO roles (name, display_name, description) VALUES
('admin', 'مدیر سیستم', 'دسترسی کامل به تمام بخش‌های سیستم'),
('user', 'کاربر عادی', 'دسترسی محدود به عملیات پایه'),
('outsource', 'برون سپاری', 'نقش ویژه برای تعمیرات برون‌سپاری شده')
ON DUPLICATE KEY UPDATE display_name = VALUES(display_name), description = VALUES(description);

-- جدول صفحات سیستم
CREATE TABLE IF NOT EXISTS system_pages (
  id INT AUTO_INCREMENT PRIMARY KEY,
  page_name VARCHAR(50) NOT NULL UNIQUE,
  display_name VARCHAR(100) NOT NULL,
  description TEXT NULL,
  file_path VARCHAR(255) NOT NULL,
  icon VARCHAR(50) NULL,
  sort_order INT DEFAULT 0,
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_persian_ci;

-- درج صفحات سیستم
INSERT INTO system_pages (page_name, display_name, description, file_path, icon, sort_order) VALUES
('dashboard', 'داشبورد', 'صفحه اصلی و نمای کلی سیستم', 'dashboard.php', 'fas fa-tachometer-alt', 1),
('devices', 'مدیریت دستگاه‌ها', 'مشاهده و مدیریت دستگاه‌ها', 'devices.php', 'fas fa-cogs', 2),
('activities', 'مدیریت فعالیت‌ها', 'تعریف و مدیریت فعالیت‌های نگهداری', 'activities.php', 'fas fa-tasks', 3),
('work_order', 'دستور کار', 'ایجاد و مدیریت دستورات کار', 'work_order.php', 'fas fa-clipboard-list', 4),
('reports_list', 'گزارش‌های خرابی', 'مشاهده و مدیریت گزارش‌های خرابی', 'reports_list.php', 'fas fa-exclamation-triangle', 5),
('my_tasks', 'وظایف من', 'مشاهده وظایف اختصاص یافته', 'my_tasks.php', 'fas fa-user-check', 6),
('profile', 'پروفایل کاربری', 'مشاهده و ویرایش پروفایل شخصی', 'profile.php', 'fas fa-user-circle', 7),
('user_management', 'مدیریت کاربران', 'مدیریت کاربران، نقش‌ها و مجوزها', 'user_management.php', 'fas fa-users-cog', 8)
ON DUPLICATE KEY UPDATE display_name = VALUES(display_name), description = VALUES(description), file_path = VALUES(file_path), icon = VALUES(icon), sort_order = VALUES(sort_order);

-- جدول مجوزها
CREATE TABLE IF NOT EXISTS permissions (
  id INT AUTO_INCREMENT PRIMARY KEY,
  page_id INT NOT NULL,
  permission_name VARCHAR(50) NOT NULL,
  display_name VARCHAR(100) NOT NULL,
  description TEXT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (page_id) REFERENCES system_pages(id) ON DELETE CASCADE,
  UNIQUE KEY unique_page_permission (page_id, permission_name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_persian_ci;

-- جدول تخصیص مجوزها به نقش‌ها
CREATE TABLE IF NOT EXISTS role_permissions (
  id INT AUTO_INCREMENT PRIMARY KEY,
  role_id INT NOT NULL,
  permission_id INT NOT NULL,
  granted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE,
  FOREIGN KEY (permission_id) REFERENCES permissions(id) ON DELETE CASCADE,
  UNIQUE KEY unique_role_permission (role_id, permission_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_persian_ci;

-- جدول کاربران (به‌روزرسانی شده)
CREATE TABLE IF NOT EXISTS users (
  id INT AUTO_INCREMENT PRIMARY KEY,
  username VARCHAR(50) NOT NULL UNIQUE,
  password VARCHAR(255) NOT NULL,
  name VARCHAR(100) NULL,
  email VARCHAR(100) NULL,
  phone VARCHAR(20) NULL,
  avatar VARCHAR(255) NULL,
  role_id INT NULL,
  role VARCHAR(50) DEFAULT 'user', 
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_persian_ci;

-- درج مجوزهای پیش‌فرض برای هر صفحه
INSERT INTO permissions (page_id, permission_name, display_name, description) VALUES
-- مجوزهای داشبورد
((SELECT id FROM system_pages WHERE page_name = 'dashboard'), 'view', 'مشاهده داشبورد', 'دسترسی به صفحه داشبورد'),

-- مجوزهای دستگاه‌ها
((SELECT id FROM system_pages WHERE page_name = 'devices'), 'view', 'مشاهده دستگاه‌ها', 'مشاهده لیست دستگاه‌ها'),
((SELECT id FROM system_pages WHERE page_name = 'devices'), 'create', 'ایجاد دستگاه', 'افزودن دستگاه جدید'),
((SELECT id FROM system_pages WHERE page_name = 'devices'), 'edit', 'ویرایش دستگاه', 'ویرایش اطلاعات دستگاه‌ها'),
((SELECT id FROM system_pages WHERE page_name = 'devices'), 'delete', 'حذف دستگاه', 'حذف دستگاه‌ها'),

-- مجوزهای فعالیت‌ها
((SELECT id FROM system_pages WHERE page_name = 'activities'), 'view', 'مشاهده فعالیت‌ها', 'مشاهده لیست فعالیت‌ها'),
((SELECT id FROM system_pages WHERE page_name = 'activities'), 'create', 'ایجاد فعالیت', 'تعریف فعالیت جدید'),
((SELECT id FROM system_pages WHERE page_name = 'activities'), 'edit', 'ویرایش فعالیت', 'ویرایش فعالیت‌ها'),
((SELECT id FROM system_pages WHERE page_name = 'activities'), 'delete', 'حذف فعالیت', 'حذف فعالیت‌ها'),

-- مجوزهای دستور کار
((SELECT id FROM system_pages WHERE page_name = 'work_order'), 'view', 'مشاهده دستور کار', 'مشاهده دستورات کار'),
((SELECT id FROM system_pages WHERE page_name = 'work_order'), 'create', 'ایجاد دستور کار', 'ایجاد دستور کار جدید'),
((SELECT id FROM system_pages WHERE page_name = 'work_order'), 'edit', 'ویرایش دستور کار', 'ویرایش دستورات کار'),
((SELECT id FROM system_pages WHERE page_name = 'work_order'), 'delete', 'حذف دستور کار', 'حذف دستورات کار'),
((SELECT id FROM system_pages WHERE page_name = 'work_order'), 'approve', 'تایید دستور کار', 'تایید و بستن دستورات کار'),

-- مجوزهای گزارش‌های خرابی
((SELECT id FROM system_pages WHERE page_name = 'reports_list'), 'view', 'مشاهده گزارش‌ها', 'مشاهده گزارش‌های خرابی'),
((SELECT id FROM system_pages WHERE page_name = 'reports_list'), 'create', 'ایجاد گزارش', 'ثبت گزارش خرابی جدید'),
((SELECT id FROM system_pages WHERE page_name = 'reports_list'), 'edit', 'ویرایش گزارش', 'ویرایش گزارش‌های خرابی'),
((SELECT id FROM system_pages WHERE page_name = 'reports_list'), 'delete', 'حذف گزارش', 'حذف گزارش‌های خرابی'),

-- مجوزهای وظایف من
((SELECT id FROM system_pages WHERE page_name = 'my_tasks'), 'view', 'مشاهده وظایف', 'مشاهده وظایف اختصاص یافته'),
((SELECT id FROM system_pages WHERE page_name = 'my_tasks'), 'update', 'به‌روزرسانی وظایف', 'به‌روزرسانی وضعیت وظایف'),

-- مجوزهای پروفایل کاربری
((SELECT id FROM system_pages WHERE page_name = 'profile'), 'view', 'مشاهده پروفایل', 'مشاهده اطلاعات شخصی'),
((SELECT id FROM system_pages WHERE page_name = 'profile'), 'edit_own_profile', 'ویرایش پروفایل خود', 'ویرایش اطلاعات شخصی خود'),
((SELECT id FROM system_pages WHERE page_name = 'profile'), 'view_all_profiles', 'مشاهده همه پروفایل‌ها', 'مشاهده اطلاعات تمام کاربران'),
((SELECT id FROM system_pages WHERE page_name = 'profile'), 'edit_all_profiles', 'ویرایش همه پروفایل‌ها', 'ویرایش اطلاعات تمام کاربران'),

-- مجوزهای مدیریت کاربران
((SELECT id FROM system_pages WHERE page_name = 'user_management'), 'view', 'مشاهده کاربران', 'مشاهده لیست کاربران'),
((SELECT id FROM system_pages WHERE page_name = 'user_management'), 'create', 'ایجاد کاربر', 'افزودن کاربر جدید'),
((SELECT id FROM system_pages WHERE page_name = 'user_management'), 'edit', 'ویرایش کاربر', 'ویرایش اطلاعات کاربران'),
((SELECT id FROM system_pages WHERE page_name = 'user_management'), 'delete', 'حذف کاربر', 'حذف کاربران'),
((SELECT id FROM system_pages WHERE page_name = 'user_management'), 'manage_roles', 'مدیریت نقش‌ها', 'مدیریت نقش‌های کاربری'),
((SELECT id FROM system_pages WHERE page_name = 'user_management'), 'manage_permissions', 'مدیریت مجوزها', 'تخصیص مجوزها به نقش‌ها')
ON DUPLICATE KEY UPDATE display_name = VALUES(display_name), description = VALUES(description);

-- تخصیص مجوزهای کامل به نقش مدیر
INSERT INTO role_permissions (role_id, permission_id)
SELECT r.id, p.id
FROM roles r
CROSS JOIN permissions p
WHERE r.name = 'admin'
ON DUPLICATE KEY UPDATE granted_at = VALUES(granted_at);

-- تخصیص مجوزهای محدود به نقش کاربر عادی
INSERT INTO role_permissions (role_id, permission_id)
SELECT r.id, p.id
FROM roles r
JOIN permissions p ON p.permission_name IN ('view', 'create', 'update')
JOIN system_pages sp ON p.page_id = sp.id
WHERE r.name = 'user' AND sp.page_name IN ('dashboard', 'devices', 'activities', 'work_order', 'reports_list', 'my_tasks')
ON DUPLICATE KEY UPDATE granted_at = VALUES(granted_at);

-- تخصیص مجوزهای پروفایل به همه کاربران
INSERT INTO role_permissions (role_id, permission_id)
SELECT r.id, p.id
FROM roles r
JOIN permissions p ON p.permission_name IN ('view', 'edit_own_profile')
JOIN system_pages sp ON p.page_id = sp.id
WHERE sp.page_name = 'profile'
ON DUPLICATE KEY UPDATE granted_at = VALUES(granted_at);

-- درج کاربر پیش‌فرض (اگر وجود ندارد)
INSERT INTO users (username, password, name, role_id, role)
SELECT 'admin', 'admin123', 'مدیر سیستم',
       (SELECT id FROM roles WHERE name = 'admin'), 'admin'
WHERE NOT EXISTS (SELECT 1 FROM users WHERE username = 'admin');

-- درج کاربر ویژه برون سپاری اگر وجود ندارد
INSERT INTO users (id, username, password, name, role_id, role)
SELECT 99999, 'outsource', '', 'برون سپاری',
       (SELECT id FROM roles WHERE name = 'outsource'), 'outsource'
WHERE NOT EXISTS (SELECT 1 FROM users WHERE id = 99999 OR username = 'outsource');


-- جدول دستگاه‌ها
CREATE TABLE IF NOT EXISTS devices (
  id INT AUTO_INCREMENT PRIMARY KEY,
  name VARCHAR(100) NOT NULL,
  serial_number VARCHAR(100) NULL,
  description TEXT NULL,
  images VARCHAR(255) NULL,
  purchase_date DATE NULL,
  manufacture_year INT NULL,
  installation_date DATE NULL,
  operation_date DATE NULL,
  vendor_name VARCHAR(150) NULL,
  vendor_phone VARCHAR(30) NULL,
  location INT NULL,
  status ENUM('فعال', 'غیرفعال', 'در حال تعمیر') NOT NULL DEFAULT 'فعال',
  notes TEXT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_persian_ci;

-- جدول دسته‌بندی‌ها
CREATE TABLE IF NOT EXISTS categories (
  id INT AUTO_INCREMENT PRIMARY KEY,
  name VARCHAR(255) NOT NULL UNIQUE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_persian_ci;

-- درج دسته‌بندی‌های اولیه
INSERT INTO categories (name)
SELECT 'پیشگیرانه (PM)' WHERE NOT EXISTS (SELECT 1 FROM categories WHERE name = 'پیشگیرانه (PM)');
INSERT INTO categories (name)
SELECT 'اصلاحی (CM)' WHERE NOT EXISTS (SELECT 1 FROM categories WHERE name = 'اصلاحی (CM)');
INSERT INTO categories (name)
SELECT 'کالیبراسیون' WHERE NOT EXISTS (SELECT 1 FROM categories WHERE name = 'کالیبراسیون');
INSERT INTO categories (name)
SELECT 'بازرسی' WHERE NOT EXISTS (SELECT 1 FROM categories WHERE name = 'بازرسی');

-- جدول فعالیت‌ها
CREATE TABLE IF NOT EXISTS activities (
    id INT AUTO_INCREMENT PRIMARY KEY,
    device_id INT NOT NULL,
    activity_name VARCHAR(255) NOT NULL,
    service_interval_days INT NOT NULL,
    category_id INT NULL,
    last_activity_time DATE NULL,
	next_service_date DATE NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (device_id) REFERENCES devices(id) ON DELETE CASCADE,
    FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_persian_ci;

-- جدول گزارش‌ها
CREATE TABLE IF NOT EXISTS logs (
  id INT AUTO_INCREMENT PRIMARY KEY,
  device_id INT NOT NULL,
  activity_id INT NOT NULL,
  activity_date DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  description TEXT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (device_id) REFERENCES devices(id) ON DELETE CASCADE,
  FOREIGN KEY (activity_id) REFERENCES activities(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_persian_ci;

-- جدول قطعات مصرفی
CREATE TABLE IF NOT EXISTS device_parts (
  id INT AUTO_INCREMENT PRIMARY KEY,
  device_id INT NOT NULL,
  part_name VARCHAR(255) NOT NULL,
  quantity INT NOT NULL DEFAULT 1,
  description TEXT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (device_id) REFERENCES devices(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_persian_ci;

-- جدول مشخصات الکتروموتورها
CREATE TABLE IF NOT EXISTS device_motors (
  id INT AUTO_INCREMENT PRIMARY KEY,
  device_id INT NOT NULL,
  motor_name VARCHAR(255) NOT NULL,
  voltage VARCHAR(50) NULL,
  power VARCHAR(50) NULL,
  current VARCHAR(50) NULL,
  rpm VARCHAR(50) NULL,
  description TEXT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (device_id) REFERENCES devices(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_persian_ci;

-- جدول مسئولین فعالیت‌ها
CREATE TABLE IF NOT EXISTS activity_assignees (
  activity_id INT NOT NULL,
  user_id INT NOT NULL,
  PRIMARY KEY (activity_id, user_id),
  FOREIGN KEY (activity_id) REFERENCES activities(id) ON DELETE CASCADE,
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_persian_ci;

-- جدول پیوست‌ها
CREATE TABLE IF NOT EXISTS attachments (
  id INT AUTO_INCREMENT PRIMARY KEY,
  activity_id INT NOT NULL,
  filename VARCHAR(255) NOT NULL,
  filepath VARCHAR(512) NOT NULL,
  uploaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (activity_id) REFERENCES activities(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_persian_ci;

CREATE TABLE IF NOT EXISTS activity_attachments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    activity_id INT NOT NULL,
    filename VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (activity_id) REFERENCES activities(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_persian_ci;

-- جدول مکانها
CREATE TABLE IF NOT EXISTS locations (
  id INT AUTO_INCREMENT PRIMARY KEY,
  location_name VARCHAR(50) NOT NULL UNIQUE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_persian_ci;

-- جدول دستور کارها 
CREATE TABLE IF NOT EXISTS work_orders (
  id INT AUTO_INCREMENT PRIMARY KEY,
  workorder_id VARCHAR(20) NOT NULL UNIQUE, -- شماره خودکار دستور کار
  title VARCHAR(255) NOT NULL,
  type ENUM('پیشگیرانه', 'اصلاحی', 'اضطراری') NOT NULL DEFAULT 'پیشگیرانه',
  device_id INT NOT NULL,
  scheduled_activity_id INT NULL,
  description TEXT NOT NULL,
  requester_id INT NOT NULL, -- درخواست کننده
  priority ENUM('پایین', 'متوسط', 'بالا', 'بحرانی') NOT NULL DEFAULT 'متوسط',
  due_date DATE NULL,
  request_date DATE NOT NULL, -- تاریخ درخواست
  status VARCHAR(100) NOT NULL DEFAULT 'دستورکار صادر شد',
  verifier_id INT(11) NULL DEFAULT NULL,
  line_stopped BOOLEAN DEFAULT 0, -- خط متوقف شده؟
  stop_datetime DATETIME NULL, -- تاریخ و زمان توقف خط
  restart_datetime DATETIME NULL, --
  attachment_path VARCHAR(255) NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (device_id) REFERENCES devices(id) ON DELETE CASCADE,
  FOREIGN KEY (scheduled_activity_id) REFERENCES activities(id) ON DELETE SET NULL,
  FOREIGN KEY (requester_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_persian_ci;


-- جدول ارتباط دستور کار با مسئولین (کارشناسان/تکنسین‌ها)
CREATE TABLE IF NOT EXISTS work_order_assignees (
  id INT AUTO_INCREMENT PRIMARY KEY,
  work_order_id INT NOT NULL,
  user_id INT NOT NULL,
  assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (work_order_id) REFERENCES work_orders(id) ON DELETE CASCADE,
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_persian_ci;

CREATE TABLE IF NOT EXISTS breakdown_reports (
    id INT AUTO_INCREMENT PRIMARY KEY,
    device_id INT NOT NULL,
    reported_by_id INT NOT NULL,
    problem_description TEXT NOT NULL,
    urgency ENUM('فوری', 'عادی') NOT NULL DEFAULT 'عادی',
    report_datetime DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
	breakdown_datetime DATETIME  NULL,
	line_stoppage_datetime DATETIME  NULL,
    status VARCHAR(100) NOT NULL DEFAULT 'گزارش شده',
    converted_to_wo_id INT NULL,
    FOREIGN KEY (device_id) REFERENCES devices(id),
    FOREIGN KEY (reported_by_id) REFERENCES users(id),
    FOREIGN KEY (converted_to_wo_id) REFERENCES work_orders(id)
)ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_persian_ci;

CREATE TABLE IF NOT EXISTS breakdown_report_images (
    id INT AUTO_INCREMENT PRIMARY KEY,
    report_id INT NOT NULL,
    image_path VARCHAR(255) NOT NULL,
    uploaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,	
    FOREIGN KEY (report_id) REFERENCES breakdown_reports(id) ON DELETE CASCADE
)ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_persian_ci;

CREATE TABLE IF NOT EXISTS work_order_attachments (
  id INT AUTO_INCREMENT PRIMARY KEY,
  work_order_id INT NOT NULL,
  file_path VARCHAR(255) NOT NULL,
  uploaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (work_order_id) REFERENCES work_orders(id) ON DELETE CASCADE
)ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_persian_ci;

CREATE TABLE IF NOT EXISTS work_order_execution (
  id INT AUTO_INCREMENT PRIMARY KEY,
  work_order_id INT NOT NULL UNIQUE, /* کلید خارجی یکتا برای رابطه یک-به-یک */
  actual_start_datetime DATETIME NULL,
  actual_end_datetime DATETIME NULL,
  completed_by_id INT NULL,
  actual_labor_hours DECIMAL(5, 2) NULL,
  completion_notes TEXT NULL,
  delay_reason TEXT NULL,
  exit_date DATE NULL,
  exit_where VARCHAR(255) NULL,
  exit_desc TEXT NULL,
  back_date DATE NULL,
  back_desc TEXT NULL,
  back_pay INT NULL,
  status VARCHAR(50) NULL,
  FOREIGN KEY (work_order_id) REFERENCES work_orders(id) ON DELETE CASCADE
)ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_persian_ci;

CREATE TABLE IF NOT EXISTS work_order_parts (
  id INT AUTO_INCREMENT PRIMARY KEY,
  work_order_id INT NOT NULL,
  part_name VARCHAR(255) NOT NULL,
  quantity_used INT NOT NULL DEFAULT 1,
  unit VARCHAR(50) NOT NULL,
  FOREIGN KEY (work_order_id) REFERENCES work_orders(id) ON DELETE CASCADE
)ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_persian_ci;

CREATE TABLE IF NOT EXISTS work_order_labor (
  id INT AUTO_INCREMENT PRIMARY KEY,
  work_order_id INT NOT NULL,
  user_id INT NOT NULL,
  hours_worked DECIMAL(5, 2) NOT NULL,
  FOREIGN KEY (work_order_id) REFERENCES work_orders(id) ON DELETE CASCADE,
  FOREIGN KEY (user_id) REFERENCES users(`id`) ON DELETE CASCADE
)ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_persian_ci;

-- جدول یادآوری‌های poke برای گزارش‌های خرابی
CREATE TABLE IF NOT EXISTS breakdown_report_pokes (
  id INT AUTO_INCREMENT PRIMARY KEY,
  report_id INT NOT NULL,
  sender_id INT NOT NULL,
  recipient_id INT NOT NULL,
  message TEXT NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (report_id) REFERENCES breakdown_reports(id) ON DELETE CASCADE,
  FOREIGN KEY (sender_id) REFERENCES users(id) ON DELETE CASCADE,
  FOREIGN KEY (recipient_id) REFERENCES users(id) ON DELETE CASCADE
)ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_persian_ci;
-- ایجاد تریگر

DELIMITER $$
CREATE TRIGGER update_activity_time
AFTER INSERT ON logs
FOR EACH ROW
BEGIN
    UPDATE activities 
    SET last_activity_time = NEW.activity_date
    WHERE id = NEW.activity_id;
END$$
DELIMITER ;


-- #############################################
-- گزارش‌ساز و ویجت‌های داشبورد (افزوده شده)
-- #############################################

-- جداول جدید برای گزارش‌ساز و ویجت‌های داشبورد
CREATE TABLE IF NOT EXISTS report_definitions (
  id INT AUTO_INCREMENT PRIMARY KEY,
  name VARCHAR(150) NOT NULL,
  description TEXT NULL,
  owner_id INT NOT NULL,
  is_public BOOLEAN DEFAULT 0,
  definition_json JSON NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (owner_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_persian_ci;

CREATE TABLE IF NOT EXISTS dashboard_report_widgets (
  id INT AUTO_INCREMENT PRIMARY KEY,
  user_id INT NOT NULL,
  report_id INT NOT NULL,
  position INT NOT NULL DEFAULT 0,
  display_options JSON NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
  FOREIGN KEY (report_id) REFERENCES report_definitions(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_persian_ci;

-- ثبت صفحات جدید
INSERT INTO system_pages (page_name, display_name, description, file_path, icon, sort_order)
VALUES
  ('report_builder', 'سازنده گزارش', 'ساخت و ذخیره گزارش‌های سفارشی', 'report_builder.php', 'fas fa-chart-bar', 9),
  ('dashboard_reports', 'سفارشی‌سازی داشبورد', 'انتخاب گزارش‌ها جهت نمایش در داشبورد', 'dashboard_reports.php', 'fas fa-th-large', 10)
ON DUPLICATE KEY UPDATE display_name = VALUES(display_name), description = VALUES(description), file_path = VALUES(file_path), icon = VALUES(icon), sort_order = VALUES(sort_order);

-- ثبت مجوزهای صفحات جدید
INSERT INTO permissions (page_id, permission_name, display_name, description)
VALUES
  ((SELECT id FROM system_pages WHERE page_name = 'report_builder'), 'view', 'مشاهده سازنده گزارش', NULL),
  ((SELECT id FROM system_pages WHERE page_name = 'report_builder'), 'create', 'ایجاد گزارش', NULL),
  ((SELECT id FROM system_pages WHERE page_name = 'report_builder'), 'edit', 'ویرایش گزارش', NULL),
  ((SELECT id FROM system_pages WHERE page_name = 'report_builder'), 'delete', 'حذف گزارش', NULL),
  ((SELECT id FROM system_pages WHERE page_name = 'report_builder'), 'execute', 'اجرای گزارش', NULL),
  ((SELECT id FROM system_pages WHERE page_name = 'dashboard_reports'), 'view', 'مشاهده مدیریت داشبورد', NULL),
  ((SELECT id FROM system_pages WHERE page_name = 'dashboard_reports'), 'manage', 'مدیریت ویجت‌های داشبورد', NULL)
ON DUPLICATE KEY UPDATE display_name = VALUES(display_name), description = VALUES(description);

-- اطمینان از تخصیص مجوزهای جدید به نقش مدیر سیستم
INSERT INTO role_permissions (role_id, permission_id)
SELECT r.id, p.id
FROM roles r
CROSS JOIN permissions p
WHERE r.name = 'admin'
ON DUPLICATE KEY UPDATE granted_at = VALUES(granted_at);

-- Table for predefined messages
CREATE TABLE IF NOT EXISTS predefined_messages (
    id INT AUTO_INCREMENT PRIMARY KEY,
    message TEXT NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_persian_ci;
