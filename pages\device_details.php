<?php
require_once '../includes/auth.php';
require_once '../db_connection.php';
require_once '../includes/date_helper.php';

// بررسی مجوز مشاهده جزئیات دستگاه
require_page_access('devices', 'view');

header('Content-Type: application/json; charset=utf-8');

if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    echo json_encode(['success' => false, 'message' => 'شناسه دستگاه نامعتبر است']);
    exit;
}

$id = (int)$_GET['id'];
$pdo = db_connect();

// **اصلاح**: یک کوئری بهینه برای دریافت تمام اطلاعات لازم
$stmt = $pdo->prepare("
    SELECT d.*, l.location_name 
    FROM devices d
    LEFT JOIN locations l ON d.location = l.id
    WHERE d.id = ?
");
$stmt->execute([$id]);
$device = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$device) {
    echo json_encode(['success' => false, 'message' => 'دستگاه یافت نشد']);
    exit;
}

// دریافت لیست قطعات مصرفی
$partStmt = $pdo->prepare("SELECT part_name, quantity, unit, description FROM device_parts WHERE device_id = ?");
$partStmt->execute([$id]);
$parts = $partStmt->fetchAll(PDO::FETCH_ASSOC);

// دریافت لیست موتورها
$motorStmt = $pdo->prepare("SELECT motor_name, voltage, power, current, rpm, description FROM device_motors WHERE device_id = ?");
$motorStmt->execute([$id]);
$motors = $motorStmt->fetchAll(PDO::FETCH_ASSOC);

// تبدیل تاریخ‌ها به شمسی
$device['purchase_date'] = !empty($device['purchase_date']) ? to_shamsi($device['purchase_date'], 'Y/m/d') : '-';
$device['installation_date'] = !empty($device['installation_date']) ? to_shamsi($device['installation_date'], 'Y/m/d') : '-';
$device['operation_date'] = !empty($device['operation_date']) ? to_shamsi($device['operation_date'], 'Y/m/d') : '-';
$device['updated_at'] = !empty($device['updated_at']) ? to_shamsi($device['updated_at'], 'Y/m/d H:i') : '-';

// افزودن قطعات و موتورها به آرایه دستگاه
$device['parts'] = $parts;
$device['motors'] = $motors;

// **توجه**: ستون images از قبل در کوئری اصلی خوانده شده است.
// نیازی به کار اضافی نیست، فقط به صورت خودکار در پاسخ JSON قرار می‌گیرد.

echo json_encode(['success' => true, 'device' => $device]);
