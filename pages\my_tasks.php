<?php
require_once '../includes/auth.php';
require_once '../db_connection.php';
require_once '../includes/date_helper.php';

// بررسی دسترسی به صفحه وظایف من
require_page_access('my_tasks', 'view');

date_default_timezone_set('Asia/Tehran');

$pdo = db_connect();

function to_persian_numerals($string)
{
    $persian_digits = ['۰', '۱', '۲', '۳', '۴', '۵', '۶', '۷', '۸', '۹'];
    $english_digits = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];
    return str_replace($english_digits, $persian_digits, $string);
}

function format_shamsi_datetime($datetime_string)
{
    if (empty($datetime_string) || str_starts_with($datetime_string, '0000-00-00')) {
        return '-';
    }
    try {
        $date_obj = new DateTime($datetime_string);
        $shamsi_date = to_shamsi($datetime_string, 'Y/m/d');

        if (strpos($datetime_string, ' ') !== false && $date_obj->format('H:i:s') !== '00:00:00') {
            $time = $date_obj->format('H:i');
            return $shamsi_date . '، ساعت ' . to_persian_numerals($time);
        } else {
            return $shamsi_date;
        }
    } catch (Exception $e) {
        return to_shamsi($datetime_string, 'Y/m/d');
    }
}

function json_response($data, $statusCode = 200)
{
    if (ob_get_level() > 0) {
        ob_clean();
    }
    http_response_code($statusCode);
    header('Content-Type: application/json; charset=utf-8');
    echo json_encode($data, JSON_UNESCAPED_UNICODE);
    exit;
}

if (isset($_POST['action']) || isset($_GET['action'])) {
    ob_start();
    ini_set('display_errors', 0);
    error_reporting(E_ALL);

    try {
        $db = db_connect();
        $user_id = current_user_id();
        $action = $_POST['action'] ?? $_GET['action'];

        if ($action === 'get_work_order_details') {
            $work_order_id = $_GET['work_order_id'] ?? 0;
            if (empty($work_order_id)) {
                json_response(['success' => false, 'message' => 'شناسه نامعتبر است.'], 400);
            }

            $stmt_wo = $db->prepare("
                SELECT 
                    wo.*, 
                    d.name as device_name,
                    d.serial_number,
                    loc.location_name,
                    u.name as requester_name, 
                    sa.activity_name as scheduled_activity_name
                FROM work_orders wo
                LEFT JOIN devices d ON wo.device_id = d.id
                LEFT JOIN locations loc ON d.location = loc.id
                LEFT JOIN users u ON wo.requester_id = u.id
                LEFT JOIN activities sa ON wo.scheduled_activity_id = sa.id
                WHERE wo.id = ?
            ");
            $stmt_wo->execute([$work_order_id]);
            $work_order_data = $stmt_wo->fetch(PDO::FETCH_ASSOC);

            if (!$work_order_data) {
                json_response(['success' => false, 'message' => 'دستور کار یافت نشد.'], 404);
            }

            $stmt_source_report = $db->prepare("SELECT id FROM breakdown_reports WHERE converted_to_wo_id = ?");
            $stmt_source_report->execute([$work_order_id]);
            $source_report_info = $stmt_source_report->fetch(PDO::FETCH_ASSOC);
            $source_report_details = null;

            if ($source_report_info) {
                $source_id = $source_report_info['id'];
                $stmt_full_report = $db->prepare("
                    SELECT br.*, d.name AS device_name, u.name AS reporter_name,
                           (SELECT GROUP_CONCAT(image_path) FROM breakdown_report_images WHERE report_id = br.id) AS images
                    FROM breakdown_reports br
                    JOIN devices d ON br.device_id = d.id
                    JOIN users u ON br.reported_by_id = u.id
                    WHERE br.id = ?
                ");
                $stmt_full_report->execute([$source_id]);
                $source_report_details = $stmt_full_report->fetch(PDO::FETCH_ASSOC);

                if ($source_report_details) {
                    $source_report_details['breakdown_datetime_shamsi'] = format_shamsi_datetime($source_report_details['breakdown_datetime']);
                    $source_report_details['report_datetime_shamsi'] = format_shamsi_datetime($source_report_details['report_datetime']);
                    $source_report_details['line_stoppage_datetime_shamsi'] = $source_report_details['line_stoppage_datetime'] ? format_shamsi_datetime($source_report_details['line_stoppage_datetime']) : null;
                    $source_report_details['images'] = !empty($source_report_details['images']) ? explode(',', $source_report_details['images']) : [];
                }
            }
            $work_order_data['source_report_details'] = $source_report_details;

            $work_order_data['request_date_shamsi'] = format_shamsi_datetime($work_order_data['request_date']);
            $work_order_data['due_date_shamsi'] = format_shamsi_datetime($work_order_data['due_date']);
            $work_order_data['stop_datetime_shamsi'] = format_shamsi_datetime($work_order_data['stop_datetime']);

            $stmt_assignees = $db->prepare("SELECT u.name FROM work_order_assignees woa JOIN users u ON woa.user_id = u.id WHERE woa.work_order_id = ?");
            $stmt_assignees->execute([$work_order_id]);
            $assignees_data = $stmt_assignees->fetchAll(PDO::FETCH_COLUMN);

            $stmt_attachments = $db->prepare("SELECT file_path FROM work_order_attachments WHERE work_order_id = ?");
            $stmt_attachments->execute([$work_order_id]);
            $attachments_data = $stmt_attachments->fetchAll(PDO::FETCH_COLUMN);

            json_response([
                'success' => true,
                'work_order' => $work_order_data,
                'assignees' => $assignees_data,
                'attachments' => $attachments_data
            ]);
        }

        if ($action === 'get_history_list') {
            $search_term = $_GET['search'] ?? '';
            $from_date = !empty($_GET['from_date']) ? to_miladi($_GET['from_date']) : null;
            $to_date = !empty($_GET['to_date']) ? to_miladi($_GET['to_date']) : null;
            $status = $_GET['status'] ?? 'all';

            $sql = "SELECT
                        wo.id, wo.title, wo.status,
                        d.name as device_name,
                        exec.actual_start_datetime,
                        exec.actual_end_datetime,
                        exec.completion_notes,
                        exec.delay_reason,
                        u_verifier.name as verifier_name,
                        (SELECT GROUP_CONCAT(u.name) FROM work_order_labor wol JOIN users u ON wol.user_id = u.id WHERE wol.work_order_id = wo.id) as labor_users,
                        (SELECT GROUP_CONCAT(wop.part_name, ' (', wop.quantity_used, ' ', wop.unit, ')') FROM work_order_parts wop WHERE wop.work_order_id = wo.id) as parts_used
                    FROM work_orders wo
                    JOIN devices d ON wo.device_id = d.id
                    LEFT JOIN work_order_execution exec ON wo.id = exec.work_order_id
                    LEFT JOIN users u_verifier ON wo.verifier_id = u_verifier.id
                    WHERE
                        exec.completed_by_id = ?";

            $params = [$user_id];

            if ($from_date) {
                $sql .= " AND DATE(exec.actual_end_datetime) >= ?";
                $params[] = $from_date;
            }
            if ($to_date) {
                $sql .= " AND DATE(exec.actual_end_datetime) <= ?";
                $params[] = $to_date;
            }
            if (!empty($search_term)) {
                $sql .= " AND (wo.title LIKE ? OR d.name LIKE ?)";
                $params[] = "%$search_term%";
                $params[] = "%$search_term%";
            }
            if ($status !== 'all') {
                $sql .= " AND wo.status = ?";
                $params[] = $status;
            } else {
                $sql .= " AND wo.status NOT IN ('دستورکار صادر شد', 'پایان تعمیر', 'برگشت جهت اصلاح', 'در حال انجام')";
            }

            $sql .= " ORDER BY exec.actual_end_datetime DESC LIMIT 100";

            $stmt = $db->prepare($sql);
            $stmt->execute($params);
            $history = $stmt->fetchAll(PDO::FETCH_ASSOC);

            foreach ($history as &$task) {
                $task['actual_start_datetime_shamsi'] = format_shamsi_datetime($task['actual_start_datetime']);
                $task['actual_end_datetime_shamsi'] = format_shamsi_datetime($task['actual_end_datetime']);
            }

            json_response(['success' => true, 'history' => $history]);
        }

        if ($action === 'start' || $action === 'stop') {
            $work_order_id = $_POST['work_order_id'] ?? 0;
            if (empty($work_order_id)) {
                json_response(['success' => false, 'message' => 'شناسه نامعتبر است.'], 400);
            }

            $db->beginTransaction();
            try {
                if ($action === 'start') {
                    $stmt = $db->prepare("INSERT INTO work_order_execution (work_order_id, actual_start_datetime, completed_by_id) VALUES (?, NOW(), ?) ON DUPLICATE KEY UPDATE actual_start_datetime = NOW(), completed_by_id = ?");
                    $stmt->execute([$work_order_id, $user_id, $user_id]);
                    $status_stmt = $db->prepare("UPDATE work_orders SET status = 'در حال انجام' WHERE id = ?");
                    $status_stmt->execute([$work_order_id]);
                    
                    // به‌روزرسانی وضعیت گزارش خرابی مرتبط
                    $report_status_stmt = $db->prepare("UPDATE breakdown_reports SET status = 'در حال انجام' WHERE converted_to_wo_id = ?");
                    $report_status_stmt->execute([$work_order_id]);
                    
                    $message = 'کار با موفقیت شروع شد.';
                } else {
                    // استفاده از INSERT ... ON DUPLICATE KEY UPDATE مثل دکمه شروع کار
                    $stmt = $db->prepare("INSERT INTO work_order_execution (work_order_id, actual_end_datetime, completed_by_id) VALUES (?, NOW(), ?) ON DUPLICATE KEY UPDATE actual_end_datetime = NOW(), completed_by_id = ?");
                    $stmt->execute([$work_order_id, $user_id, $user_id]);

                    $status_stmt = $db->prepare("UPDATE work_orders SET status = 'پایان تعمیر' WHERE id = ?");
                    $status_stmt->execute([$work_order_id]);

                    // به‌روزرسانی وضعیت گزارش خرابی مرتبط
                    $report_status_stmt = $db->prepare("UPDATE breakdown_reports SET status = 'پایان تعمیر' WHERE converted_to_wo_id = ?");
                    $report_status_stmt->execute([$work_order_id]);

                    $message = 'وضعیت با موفقیت به "پایان تعمیر" تغییر یافت.';
                }
                $db->commit();
                json_response(['success' => true, 'message' => $message]);
            } catch (Exception $e) {
                if ($db->inTransaction()) $db->rollBack();
                json_response(['success' => false, 'message' => 'خطا در پایگاه داده: ' . $e->getMessage()], 500);
            }
        }

        if (isset($_POST['action']) && $_POST['action'] === 'outsource_exit') {
            $wo_id = intval($_POST['work_order_id']);
            $exit_date = $_POST['exit_date'] ?? null;
            $exit_where = $_POST['exit_where'] ?? null;
            $exit_desc = $_POST['exit_desc'] ?? null;
            if (!$wo_id || !$exit_date || !$exit_where) {
                json_response(['success'=>false,'message'=>'اطلاعات ناقص است.']);
            }
            // تبدیل تاریخ شمسی به میلادی
            require_once '../includes/date_helper.php';
            $exit_date_miladi = to_miladi($exit_date);
            $pdo->beginTransaction();
            try {
                // ابتدا بررسی وجود رکورد
                $stmt = $pdo->prepare("SELECT COUNT(*) FROM work_order_execution WHERE work_order_id=?");
                $stmt->execute([$wo_id]);
                $exists = $stmt->fetchColumn();
                if ($exists) {
                    $stmt = $pdo->prepare("UPDATE work_order_execution SET exit_date=?, exit_where=?, exit_desc=? WHERE work_order_id=?");
                    $stmt->execute([$exit_date_miladi, $exit_where, $exit_desc, $wo_id]);
                } else {
                    $stmt = $pdo->prepare("INSERT INTO work_order_execution (work_order_id, exit_date, exit_where, exit_desc) VALUES (?, ?, ?, ?)");
                    $stmt->execute([$wo_id, $exit_date_miladi, $exit_where, $exit_desc]);
                }
                // تغییر وضعیت دستورکار
                $pdo->prepare("UPDATE work_orders SET status='برون سپاری شد' WHERE id=?")->execute([$wo_id]);
                // تغییر وضعیت گزارش خرابی (در صورت وجود)
                $pdo->prepare("UPDATE breakdown_reports SET status='برون سپاری شد' WHERE converted_to_wo_id=?")->execute([$wo_id]);
                $pdo->commit();
                json_response(['success'=>true]);
            } catch(Exception $e) {
                $pdo->rollBack();
                json_response(['success'=>false,'message'=>'خطا در ثبت اطلاعات: '.$e->getMessage()]);
            }
        }

        if (isset($_POST['action']) && $_POST['action'] === 'back_to_org') {
            $wo_id = intval($_POST['work_order_id']);
            $back_date = $_POST['back_date'] ?? null;
            $back_desc = $_POST['back_desc'] ?? null;
            $back_pay = $_POST['back_pay'] ?? null;
            if (!$wo_id || !$back_date) {
                json_response(['success'=>false,'message'=>'اطلاعات ناقص است.']);
            }
            require_once '../includes/date_helper.php';
            $back_date_miladi = to_miladi($back_date);
            $pdo->beginTransaction();
            try {
                // ابتدا بررسی وجود رکورد
                $stmt = $pdo->prepare("SELECT COUNT(*) FROM work_order_execution WHERE work_order_id=?");
                $stmt->execute([$wo_id]);
                $exists = $stmt->fetchColumn();
                if ($exists) {
                    $stmt = $pdo->prepare("UPDATE work_order_execution SET back_date=?, back_desc=?, back_pay=? WHERE work_order_id=?");
                    $stmt->execute([$back_date_miladi, $back_desc, $back_pay, $wo_id]);
                } else {
                    $stmt = $pdo->prepare("INSERT INTO work_order_execution (work_order_id, back_date, back_desc, back_pay) VALUES (?, ?, ?, ?)");
                    $stmt->execute([$wo_id, $back_date_miladi, $back_desc, $back_pay]);
                }
                $pdo->prepare("UPDATE work_orders SET status='پایان برون سپاری' WHERE id=?")->execute([$wo_id]);
                $pdo->prepare("UPDATE breakdown_reports SET status='پایان برون سپاری' WHERE converted_to_wo_id=?")->execute([$wo_id]);
                $pdo->commit();
                json_response(['success'=>true]);
            } catch(Exception $e) {
                $pdo->rollBack();
                json_response(['success'=>false,'message'=>'خطا در ثبت اطلاعات: '.$e->getMessage()]);
            }
        }

    } catch (Throwable $e) {
        json_response([
            'success' => false,
            'message' => 'خطای داخلی سرور رخ داد.',
            'error' => $e->getMessage(),
            'file' => $e->getFile(),
            'line' => $e->getLine()
        ], 500);
    }
}

require_once '../includes/user_helper.php';

$pdo = db_connect();
$current_user_id = current_user_id();
$users = getUsersForSelect($pdo);

$stmt = $pdo->prepare("
    SELECT 
        wo.id, wo.workorder_id, wo.title, wo.description, wo.priority, wo.status,
        wo.request_date, wo.due_date, wo.line_stopped, wo.stop_datetime,
        d.name as device_name,
        d.serial_number,
        loc.location_name,
        exec.actual_start_datetime,
        reporter_user.name AS reporter_name,
        br.line_stoppage_datetime
    FROM work_orders wo
    JOIN work_order_assignees woa ON wo.id = woa.work_order_id
    JOIN devices d ON wo.device_id = d.id
    LEFT JOIN locations loc ON d.location = loc.id
    LEFT JOIN work_order_execution exec ON wo.id = exec.work_order_id
    LEFT JOIN breakdown_reports br ON br.converted_to_wo_id = wo.id
    LEFT JOIN users reporter_user ON br.reported_by_id = reporter_user.id
    WHERE 
        woa.user_id = ? AND wo.status IN ('دستورکار صادر شد', 'پایان تعمیر', 'برگشت جهت اصلاح', 'در حال انجام', 'برون سپاری شد', 'پایان برون سپاری')
    GROUP BY wo.id
    ORDER BY FIELD(wo.priority, 'بحرانی', 'بالا', 'متوسط', 'پایین'), wo.request_date DESC
");
$stmt->execute([$current_user_id]);
$tasks = $stmt->fetchAll(PDO::FETCH_ASSOC);

$priority_class_map = [
    'بحرانی' => 'bahrani',
    'بالا' => 'bala',
    'متوسط' => 'motavaset',
    'پایین' => 'paeen'
];

$toast_message = null;
$toast_type = null;
if (isset($_SESSION['toast_message'])) {
    $toast_message = $_SESSION['toast_message'];
    $toast_type = $_SESSION['toast_type'] ?? 'info';
    unset($_SESSION['toast_message']);
    unset($_SESSION['toast_type']);
}

include '../includes/header.php';

// inject meta tags for toast
if ($toast_message) {
    echo "<meta name=\"toast-message\" content=\"" . htmlspecialchars($toast_message, ENT_QUOTES, 'UTF-8') . "\">\n";
    echo "<meta name=\"toast-type\" content=\"" . htmlspecialchars($toast_type, ENT_QUOTES, 'UTF-8') . "\">\n";
}
?>
<!DOCTYPE html>
<html lang="fa" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>وظایف من</title>
    <style>
        * {
            box-sizing: border-box;
        }
        html, body {
            overflow-x: hidden;
        }
        :root { 
            --main-color: #d94307; 
            --background-color: #f4f7f6; 
        }
        
        #page-my-tasks {
            background-color: var(--background-color);
            margin: 0;
            line-height: 1.6;
        }

        #page-my-tasks .container { max-width: 1200px; margin: 0 auto; padding: 2rem 1rem; }
        #page-my-tasks .page-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 2.5rem; }
        
        #page-my-tasks .task-cards-container { display: grid; grid-template-columns: repeat(auto-fill, minmax(340px, 1fr)); gap: 1.5rem; }
        #page-my-tasks .task-card { background-color: #fff; border-radius: 12px; box-shadow: 0 4px 15px rgba(0,0,0,0.07); display: flex; flex-direction: column; border: 1px solid #e9ecef; }
        #page-my-tasks .task-card__header { padding: 1rem 1.25rem; border-bottom: 1px solid #e9ecef; display: flex; justify-content: space-between; align-items: center; gap: 1rem; }
        #page-my-tasks .task-card__header-main { flex-grow: 1; }
        #page-my-tasks .task-card__title { font-size: var(--fs-lg); font-weight: 700; color: #333; margin: 0; }
        #page-my-tasks .task-card__problem-section {
            margin-bottom: 1rem;
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 1rem;
        }
        
        #page-my-tasks .task-card__info-section {
            margin-bottom: 1rem;
        }
        
        #page-my-tasks .task-card__info-item {
            margin: 0.5rem 0;
            font-size: var(--fs-sm);
            color: #555;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        #page-my-tasks .task-card__info-item i {
            width: 16px;
            text-align: center;
        }
        
        #page-my-tasks .task-card__line-stop {
            color: #dc3545;
            font-weight: 600;
        }
        #page-my-tasks .task-card__body { padding: 1.25rem 1.25rem 0 1.25rem; flex-grow: 1; }
        #page-my-tasks .task-card__description { color: #495057; }
        #page-my-tasks .task-card__footer { padding: 1rem 1.25rem; background-color: #f8f9fa; border-top: 1px solid #e9ecef; display: flex; justify-content: space-between; align-items: center; border-radius: 0 0 12px 12px; flex-wrap: wrap; gap: 0.5rem; }
        #page-my-tasks .btn { padding: 0.6rem 1.2rem; border-radius: 6px; text-decoration: none; font-weight: 600; border: none; cursor: pointer; transition: background-color 0.2s; width: auto; display: inline-block; text-align: center; }
        #page-my-tasks .btn-task-action { display: inline-flex; align-items: center; gap: 8px; padding: 0.6rem 1.2rem;
    border-radius: 6px; }
        #page-my-tasks .btn-task-action:disabled { background-color: #28a745; color: white; cursor: not-allowed; opacity: 0.8; padding: 0.5rem 1.2rem; }
        #page-my-tasks .btn-start { background-color: #007bff; color: white; }
        #page-my-tasks .btn-stop { background-color: #dc3545; color: white; }
        #page-my-tasks .btn-report {   background-color: #2da124;
    color: white;
    border: 1px solid #dee2e6; }
        #page-my-tasks .btn-details {   background-color: #e9ecef;
    color: #495057;
    border: 1px solid #dee2e6; }
        #page-my-tasks .no-tasks { text-align: center; padding: 3rem; font-size: var(--fs-xl); color: #6c757d; }
        #page-my-tasks .task-status-icon { font-size: var(--fs-md); }
        #page-my-tasks .in-progress .fa-clock { animation: spin 2s linear infinite; }
        @keyframes spin { 100% { transform: rotate(360deg); } }

        .priority-badge { padding: 0.25em 0.6em;  font-weight: 700; border-radius: 0.25rem; color: #fff; white-space: nowrap; flex-shrink: 0; }
        .priority-badge.bahrani { background-color: #bf2836; }
        .priority-badge.bala { background-color: #e53e3e; }
        .priority-badge.motavaset { background-color: #ffc107; color: #000; }
        .priority-badge.paeen { background-color: #17a2b8; }
        .btn-secondary{
            background-color: #2563eb;
            color: white;
        }
        .status-badge { padding: 0.2em 0.6em; border-radius: 0.25rem; font-size: var(--fs-xs); font-weight: bold; color: white; flex-shrink: 0; }
        .status-rejected { background-color: #dc3545 !important; color: #fff !important; }

        .modal { display: none; position: fixed; z-index: 1000; left: 0; top: 0; width: 100%; height: 100%; overflow: auto; background-color: rgba(0,0,0,0.5); }
        .modal .modal-content { background-color: #fefefe; margin: 5% auto; padding: 0; border: 1px solid #ddd; width: 90%; max-width: 800px; border-radius: 8px; box-shadow: 0 5px 15px rgba(0,0,0,0.15); display: flex; flex-direction: column; }
        .modal .modal-content.modal-lg { max-width: 900px; }
        .modal .modal-header { padding: 1rem 1.5rem; border-bottom: 1px solid #e9ecef; display: flex; justify-content: space-between; align-items: center; flex-shrink: 0; }
        .modal .modal-title { margin: 0; font-size: var(--fs-md); color: #333; }
        .modal .close-modal-btn { color: #aaa; font-size: var(--fs-3xl); font-weight: bold; cursor: pointer; line-height: 1; background: transparent; border: 0; padding: 0; margin: 0; }
       .modal .modal-body { padding: 1.5rem; overflow-y: auto; flex-grow: 1; }
        
        #history-modal .modal-header { padding: 1rem 1.5rem; border-bottom: 1px solid #e9ecef; display: flex; flex-direction: column; align-items: stretch; gap: 1rem; flex-shrink: 0; }
        #history-modal .modal-header-top { display: flex; justify-content: space-between; align-items: center; }
        #history-modal .modal-body { padding: 0.5rem 1.5rem 1.5rem 1.5rem; }
        #history-modal .history-filters { display: flex; flex-direction: column; gap: 1rem; }
        #history-modal .filter-actions { display: flex; justify-content: space-between; align-items: center; gap: 1rem; }
        #history-modal .advanced-filters { display: none; flex-direction: column; gap: 1rem; padding: 1rem; border-radius: 6px; background-color: #f8f9fa; border: 1px solid #e9ecef; margin-top: 0.5rem; }
        #history-modal .filter-row { display: flex; flex-direction: column; gap: 0.5rem; }
        #history-modal .filter-row input, #history-modal .filter-row select { width: 100%; padding: 0.5rem; border: 1px solid #ccc; border-radius: 4px; }
        #history-modal #toggle-filters-btn { background: none; border: 1px dashed #aaa; color: #555; padding: 0.5rem 1rem; border-radius: 6px; flex-grow: 1; text-align: right; cursor: pointer; }
        #history-modal #toggle-filters-btn .icon { float: left; transition: transform 0.3s; }
        #history-modal #toggle-filters-btn.open .icon { transform: rotate(180deg); }
        #history-modal #clear-filters-btn { background: none; border: none; color: #dc3545; font-size: var(--fs-2xl); cursor: pointer; padding: 0 0.5rem; }
        #history-modal .history-cards-container { max-height: 55vh; overflow-y: auto; padding: 5px; }
        #history-modal .history-card { background: #fff; border-radius: 8px; border-left: 5px solid #6c757d; margin-bottom: 1rem; box-shadow: 0 2px 8px rgba(0,0,0,0.08); padding: 0; }
        #history-modal .history-card-main { cursor: pointer; padding: 1rem; position: relative; }
        #history-modal .history-card-summary { flex-grow: 1; padding-left: 2rem; }
        #history-modal .history-card-summary p { margin: 0.25rem 0; color: #555; }
        #history-modal .history-card-title-line { display: flex; justify-content: space-between; align-items: center; gap: 1rem; margin-bottom: 0.5rem; }
        #history-modal .history-card-title-line strong { color: #333; flex-grow: 1; }
        #history-modal .history-card-toggle { position: absolute; bottom: 0.5rem; left: 0.5rem; background: none; border: none; font-size: var(--fs-sm); color: #aaaaaa; cursor: pointer; padding: 0.2rem; transition: transform 0.3s ease; }
        #history-modal .history-card.active .history-card-toggle { transform: rotate(180deg); }
        #history-modal .history-card-collapsible-content { max-height: 0; overflow: hidden; transition: max-height 0.4s ease-out; }
        #history-modal .history-card.active .history-card-collapsible-content { max-height: 500px; transition: max-height 0.5s ease-in; }
        #history-modal .history-card-details { padding: 0 1rem 1rem 1rem; }
        #history-modal .history-card-details p { font-size: var(--fs-sm); color: #555; }
        #history-modal .history-card-details p strong { color: #333; }
        #history-modal .status-badge, .priority-badge { padding: 0.2em 0.6em; border-radius: 0.25rem; font-size: var(--fs-xs); font-weight: bold; color: white; flex-shrink: 0; }
        #history-modal .status-انجام-شده { background-color: #28a745; }
        #history-modal .status-منتظر-تایید { background-color: #ffc107; color: #333; }
        #history-modal .status-تایید-شده { background-color: #17a2b8; }
        #history-modal .status-بسته-شده { background-color: #6c757d; }
        #history-modal .status-done-confirmed { background-color: #28a745 !important; color: #fff !important; }
        #history-modal .status-rejected { background-color: #dc3545 !important; color: #fff !important; }
        #history-modal .history-card-divider { border: 0; border-top: 1px solid #eee; margin: 0 1rem; }
        
        #details-modal .details-grid, #source-report-modal .details-grid { display: grid; grid-template-columns: 1fr; gap: 1rem; margin-bottom: 1.5rem; }
        #details-modal .detail-item, #source-report-modal .detail-item { background-color: #f8f9fa; padding: 0.75rem; border-radius: 6px; border: 1px solid #e9ecef; }
        #details-modal .detail-item strong, #source-report-modal .detail-item strong { display: block; margin-bottom: 0.25rem; color: #495057; }
        #details-modal .detail-full-width, #source-report-modal .detail-full-width { grid-column: 1 / -1; }
        #details-modal .priority-badge.bahrani { background-color: #bf2836; }
        #details-modal .priority-badge.bala { background-color: #e53e3e; }
        #details-modal .priority-badge.motavaset { background-color: #ffc107; color: #000; }
        #details-modal .priority-badge.paeen { background-color: #17a2b8; }
        
        #details-modal .attachments-list, #source-report-modal .attachments-list {
            display: block;
            margin-top: 10px;
            padding-right: 0;
            list-style: none;
        }
        #details-modal .attachment-item, #source-report-modal .attachment-item {
            width: 100%;
            height: auto;
            border: 1px solid #ddd;
            border-radius: 8px;
            overflow: hidden;
            margin-bottom: 1rem;
        }
        #details-modal .thumbnail-image, #source-report-modal .thumbnail-image {
            width: 100%;
            height: auto;
            display: block;
            object-fit: contain;
            cursor: pointer;
            background-color: #f0f0f0;
            max-width: 100% !important;
            max-height: fit-content !important;
        }

        #details-modal .detail-item a, #source-report-modal .detail-item a { color: #007bff; text-decoration: none; }
        #details-modal .detail-item a:hover, #source-report-modal .detail-item a:hover { text-decoration: underline; }

        .image-viewer-modal {
            display: none;
            position: fixed;
            z-index: 2000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;
            background-color: rgba(0,0,0,0.9);
            align-items: center;
            justify-content: center;
        }
        .image-viewer-modal .modal-content-image {
            max-width: 90vw;
            max-height: 90vh;
            object-fit: contain;
        }
        .image-viewer-modal .close-viewer {
            position: absolute;
            top: 15px;
            right: 35px;
            color: #f1f1f1;
            font-size: var(--fs-4xl);
            font-weight: bold;
            transition: 0.3s;
            cursor: pointer;
        }
        @media (min-width: 769px) {
            #details-modal .details-grid, #source-report-modal .details-grid {
                grid-template-columns: repeat(3, 1fr);
            }
            #history-modal .filter-row { 
                flex-direction: row; 
                gap: 1rem; 
                align-items: center; 
            }
        }
    </style>
</head>
<body id="page-my-tasks">
<div id="toast-container" class="toast-container"></div>
    <div class="container">
        <div class="page-header">
            <h2 class="page-title"><i class="fas fa-tasks"></i> وظایف من</h2>
            <button id="history-btn" class="btn btn-secondary"><i class="fas fa-history"></i> مشاهده سوابق</button>
        </div>
        
        <div class="task-cards-container">
            <?php if (empty($tasks)): ?>
                <p class="no-tasks">در حال حاضر هیچ وظیفه‌ای به شما محول نشده است.</p>
            <?php else: ?>
                <?php foreach ($tasks as $task): 
                    $is_in_progress = ($task['status'] === 'در حال انجام');
                    $is_finished = ($task['status'] === 'پایان تعمیر');
                    $priority_class = $priority_class_map[$task['priority']] ?? 'motavaset';
                ?>
                    <div class="task-card">
                        <div class="task-card__header">
                            <div class="task-card__header-main">
                                <h3 class="task-card__title"><?= htmlspecialchars($task['title']) ?></h3>
                            </div>
                            <div style="display: flex; gap: 0.5rem; align-items: center;">
                                <?php if ($task['status'] === 'برگشت جهت اصلاح'): ?>
                                    <span class="status-badge status-rejected">رد شده</span>
                                <?php endif; ?>
                            <span class="priority-badge <?= $priority_class ?>"><?= htmlspecialchars($task['priority']) ?></span>
                            </div>
                        </div>
                        <div class="task-card__body">
                            <?php if ($task['status'] === 'برگشت جهت اصلاح'): ?>
                                <div style="background-color: #fff3cd; border: 1px solid #ffeaa7; color: #856404; padding: 0.75rem; border-radius: 6px; margin-bottom: 1rem;">
                                    <i class="fas fa-exclamation-triangle"></i> <strong>این وظیفه رد شده است و نیاز به اصلاح دارد.</strong>
                                </div>
                            <?php endif; ?>
                            
                            <div class="task-card__problem-section">
                            <p class="task-card__description"><?= htmlspecialchars(mb_substr($task['description'], 0, 150)) ?>...</p>
                            </div>
                            
                            <div class="task-card__info-section">
                                <div class="task-card__info-item">
                                    <i class="fas fa-cogs"></i>
                                    <span><strong>دستگاه:</strong> <?= htmlspecialchars($task['device_name']) ?></span>
                                </div>
                                
                                <div class="task-card__info-item">
                                    <i class="fas fa-barcode"></i>
                                    <span><strong>شماره سریال:</strong> <?= htmlspecialchars($task['serial_number'] ?? '---') ?></span>
                                </div>
                                
                                <div class="task-card__info-item">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span><strong>مکان:</strong> <?= htmlspecialchars($task['location_name'] ?? '---') ?></span>
                                </div>
                                
                                <?php if (!empty($task['workorder_id'])): ?>
                                <div class="task-card__info-item">
                                    <i class="fas fa-hashtag"></i>
                                    <span><strong>شماره دستورکار:</strong> <?= htmlspecialchars($task['workorder_id']) ?></span>
                                </div>
                                <?php endif; ?>
                                <div class="task-card__info-item">
                                    <i class="fas fa-calendar-alt"></i>
                                    <span><strong>تاریخ دستور کار:</strong> <?= format_shamsi_datetime($task['request_date']) ?></span>
                                </div>
                                <?php if (!empty($task['due_date']) && $task['due_date'] !== '0000-00-00'): ?>
                                <div class="task-card__info-item">
                                    <i class="fas fa-calendar-check"></i>
                                    <span><strong>تاریخ سررسید:</strong> <?= format_shamsi_datetime($task['due_date']) ?></span>
                                </div>
                                <?php endif; ?>
                                
                                <?php if (!empty($task['reporter_name'])): ?>
                                    <div class="task-card__info-item">
                                        <i class="fas fa-user-tag"></i>
                                        <span><strong>گزارش دهنده:</strong> <?= htmlspecialchars($task['reporter_name']) ?></span>
                                    </div>
                                <?php endif; ?>
                                
                                <?php if ($task['line_stopped'] == 1): ?>
                                    <div class="task-card__info-item task-card__line-stop">
                                        <i class="fas fa-exclamation-triangle"></i>
                                        <span><strong>توقف خط:</strong> 
                                        <?php if ($task['stop_datetime'] && $task['stop_datetime'] !== '0000-00-00 00:00:00'): ?>
                                            <?= format_shamsi_datetime($task['stop_datetime']) ?>
                                        <?php elseif ($task['line_stoppage_datetime'] && $task['line_stoppage_datetime'] !== '0000-00-00 00:00:00'): ?>
                                            <?= format_shamsi_datetime($task['line_stoppage_datetime']) ?>
                                        <?php else: ?>
                                            زمان دقیق ثبت نشده
                                        <?php endif; ?>
                                        </span>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                        <?php
                        // نمایش تصاویر پیوست دقیقا مشابه جزئیات
                        $stmt_att = $pdo->prepare("SELECT file_path FROM work_order_attachments WHERE work_order_id = ?");
                        $stmt_att->execute([$task['id']]);
                        $attachments = $stmt_att->fetchAll(PDO::FETCH_COLUMN);
                        $has_image = false;
                        foreach ($attachments as $filePath) {
                            $ext = strtolower(pathinfo($filePath, PATHINFO_EXTENSION));
                            if (in_array($ext, ['jpg','jpeg','png','gif','bmp','webp'])) {
                                $has_image = true; break;
                            }
                        }
                        if ($has_image): ?>
                        <div class="task-card__attachments" style="padding: 0.5rem 1.25rem 1rem 1.25rem;">
                            <div style="font-size: 0.95em; margin-bottom: 0.3em; color: #666;">تصاویر پیوست:</div>
                            <div style="display: flex; flex-wrap: wrap; gap: 8px;">
                                <?php foreach ($attachments as $filePath):
                                    $ext = strtolower(pathinfo($filePath, PATHINFO_EXTENSION));
                                    if (!in_array($ext, ['jpg','jpeg','png','gif','bmp','webp'])) continue;
                                    $img_url = '../' . ltrim(str_replace('../','',$filePath), '/'); ?>
                                    <img src="<?= htmlspecialchars($img_url) ?>" alt="پیوست" class="thumbnail-image" style="width:70px; height:70px; object-fit:cover; border-radius:6px; border:1px solid #ddd; cursor:pointer; background:#f8f9fa;" onclick="openImageViewer('<?= htmlspecialchars($img_url) ?>')">
                                <?php endforeach; ?>
                            </div>
                        </div>
                        <?php endif; ?>
                        <div class="task-card__footer">
                            <?php 
                            $status_badge_class = '';
                            switch ($task['status']) {
                                case 'انجام و تایید شده': $status_badge_class = 'status-done-confirmed'; break;
                                case 'انجام شده': $status_badge_class = 'status-انجام-شده'; break;
                                case 'منتظر تایید': $status_badge_class = 'status-منتظر-تایید'; break;
                                case 'تایید شده': $status_badge_class = 'status-تایید-شده'; break;
                                case 'بسته شده': $status_badge_class = 'status-بسته-شده'; break;
                            }
                            $is_rejected = ($task['status'] === 'برگشت جهت اصلاح');
                            ?>
                            <?php if ($is_finished): ?>
                                <button class="btn-task-action" disabled style="background:#e0e0e0;color:#888;border:1px solid #ccc;">
                                    <span class="task-status-icon"><i class="fas fa-check"></i></span>
                                    <span class="btn-text">پایان یافت</span>
                                </button>
                            <?php else: ?>
                                <button class="btn-task-action <?= $is_in_progress ? 'btn-stop' : 'btn-start' ?>"
                                        data-task-id="<?= $task['id'] ?>"
                                        data-status="<?= $is_in_progress ? 'inprogress' : 'pending' ?>"
                                        <?php if ($is_rejected): ?>disabled style="background:#e0e0e0;color:#888;border:1px solid #ccc;"<?php endif; ?>>
                                    <span class="task-status-icon" id="icon-<?= $task['id'] ?>">
                                        <?php if ($is_in_progress): ?><i class="fas fa-clock"></i><?php endif; ?>
                                    </span>
                                    <span class="btn-text"><?= $is_in_progress ? 'پایان کار' : 'شروع کار' ?></span>
                                </button>
                            <?php endif; ?>
                            <?php if ($task['status'] === 'برون سپاری شد'): ?>
                                <button class="btn-task-action btn-back-org" style="background:#fffde7;color:#f9a825;border:1px solid #ffe082;" data-task-id="<?= $task['id'] ?>" <?php if ($is_rejected): ?>disabled style="background:#e0e0e0;color:#888;border:1px solid #ccc;"<?php endif; ?>>
                                    <span class="task-status-icon"><i class="fas fa-clock"></i></span>
                                    <span class="btn-text">اعلام بازگشت</span>
                                </button>
                            <?php elseif ($task['status'] === 'پایان برون سپاری'): ?>
                                <button class="btn-task-action btn-finished-outsource" disabled style="background:#e0e0e0;color:#888;border:1px solid #ccc;">
                                    <span class="task-status-icon"><i class="fas fa-check"></i></span>
                                    پایان برون سپاری
                                </button>
                            <?php else: ?>
                                <button class="btn-task-action btn-outsource" data-task-id="<?= $task['id'] ?>" <?php if ($is_rejected): ?>disabled style="background:#e0e0e0;color:#888;border:1px solid #ccc;"<?php else: ?>style="background:#e3f2fd;color:#1976d2;border:1px solid #90caf9;"<?php endif; ?>>
                                    <?php if (!$is_rejected): ?><span class="task-status-icon"><i class="fas fa-share-square"></i></span><?php endif; ?>
                                    <span class="btn-text">برون سپاری</span>
                                </button>
                            <?php endif; ?>
                        <a href="action_report.php?work_order_id=<?= $task['id'] ?>" class="btn btn-report">ثبت گزارش</a>
                        </div>
                    </div>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>
    </div>

    <div id="history-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <div class="modal-header-top">
                    <h3 class="modal-title">سوابق کارهای انجام شده</h3>
                    <button type="button" class="close-modal-btn">&times;</button>
                </div>
                <div class="history-filters">
                    <input type="text" id="history-search" class="form-control" placeholder="جستجو در عنوان یا دستگاه...">
                    <div class="filter-actions">
                        <button type="button" id="toggle-filters-btn">فیلترهای پیشرفته <span class="icon">&#9662;</span></button>
                        <button type="button" id="clear-filters-btn" title="پاک کردن فیلترها" style="display: none;"><i class="fas fa-undo"></i></button>
                    </div>
                    <div id="advanced-filters" class="advanced-filters">
                        <div class="filter-row">
                            <input type="text" id="history-from-date" class="form-control" placeholder="از تاریخ">
                            <input type="text" id="history-to-date" class="form-control" placeholder="تا تاریخ">
                        </div>
                        <div class="filter-row">
                            <select id="history-status" class="form-control">
                                <option value="all">همه وضعیت‌ها</option>
                                <option value="انجام شده">انجام شده</option>
                                <option value="منتظر تایید">منتظر تایید</option>
                                <option value="تایید شده">تایید شده</option>
                                <option value="بسته شده">بسته شده</option>
                            </select>
                        </div>
                        <button id="history-filter-btn" class="btn btn-primary">اعمال فیلتر</button>
                    </div>
                </div>
            </div>
            <div class="modal-body">
                <div id="history-cards-container" class="history-cards-container">
                </div>
            </div>
        </div>
    </div>

    <div id="source-report-modal" class="modal">
        <div class="modal-content modal-lg">
            <div class="modal-header">
                <h3 class="modal-title">جزئیات گزارش خرابی مبدا</h3>
                <button type="button" class="close-modal-btn">&times;</button>
            </div>
            <div class="modal-body" id="source-report-modal-body">
            </div>
        </div>
    </div>
    
    <div id="image-viewer-modal" class="image-viewer-modal">
        <span class="close-viewer">&times;</span>
        <img class="modal-content-image" id="modal-image-content">
    </div>

    <!-- مودال برون سپاری -->
    <div id="outsource-modal" class="modal">
        <div class="modal-content" style="max-width:400px;">
            <div class="modal-header">
                <h3 class="modal-title">برون سپاری دستگاه</h3>
                <button type="button" class="close-modal-btn" onclick="document.getElementById('outsource-modal').style.display='none'">&times;</button>
            </div>
            <div class="modal-body">
                <form id="outsource-form">
                    <div class="form-row"><label>تاریخ ارسال:</label><input type="text" name="exit_date" id="outsource-exit-date" class="persian-datepicker" required autocomplete="off"></div>
                    <div class="form-row"><label>به کجا:</label><input type="text" name="exit_where" required></div>
                    <div class="form-row"><label>توضیحات:</label><textarea name="exit_desc"></textarea></div>
                    <input type="hidden" name="work_order_id" id="outsource-work-order-id">
                    <button type="submit" class="btn btn-primary" style="width:100%;margin-top:1rem;">خروج از سازمان</button>
                </form>
            </div>
        </div>
    </div>

    <!-- مودال اعلام بازگشت -->
    <div id="back-org-modal" class="modal">
        <div class="modal-content" style="max-width:400px;">
            <div class="modal-header">
                <h3 class="modal-title">اعلام بازگشت دستگاه</h3>
                <button type="button" class="close-modal-btn" onclick="document.getElementById('back-org-modal').style.display='none'">&times;</button>
            </div>
            <div class="modal-body">
                <form id="back-org-form">
                    <div class="form-row"><label>تاریخ ورود:</label><input type="text" name="back_date" id="back-org-date" class="persian-datepicker" required autocomplete="off"></div>
                    <div class="form-row"><label>توضیحات:</label><textarea name="back_desc"></textarea></div>
                    <div class="form-row"><label>مبلغ فاکتور (تومان):</label><input type="text" name="back_pay" pattern="[0-9,]*" inputmode="numeric"></div>
                    <input type="hidden" name="work_order_id" id="back-org-work-order-id">
                    <button type="submit" class="btn btn-primary" style="width:100%;margin-top:1rem;">ثبت ورود به سازمان</button>
                </form>
            </div>
        </div>
    </div>

    <?php include '../includes/footer.php'; ?>
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        let currentSourceReportData = null;

        document.querySelectorAll('.btn-start, .btn-stop').forEach(button => {
            const iconEl = button.querySelector('.task-status-icon');
            if (button.dataset.status === 'inprogress' && iconEl) {
                iconEl.classList.add('in-progress');
            }
            button.addEventListener('click', function() {
                const taskId = this.dataset.taskId;
                const action = (this.dataset.status === 'pending') ? 'start' : 'stop';
                const formData = new FormData();
                formData.append('action', action);
                formData.append('work_order_id', taskId);
                this.disabled = true;

                fetch('my_tasks.php', { method: 'POST', body: formData })
                .then(res => {
                    if (!res.ok) return res.json().then(err => Promise.reject(err));
                    return res.json();
                })
                .then(data => {
                    if (data.success) {
                        if (typeof showToast === 'function') {
                            showToast(data.message, 'success');
                            setTimeout(() => window.location.reload(), 1200);
                        } else {
                            window.location.reload();
                        }
                    } else {
                        if (typeof showToast === 'function') {
                            showToast(data.message || 'خطا', 'danger');
                        } else {
                            alert('خطا: ' + data.message);
                        }
                        this.disabled = false;
                    }
                }).catch(err => {
                    alert('خطا: ' + (err.message || 'خطای ارتباطی رخ داد.'));
                    this.disabled = false;
                });
            });
        });
        
        document.querySelectorAll('.close-modal-btn').forEach(function(btn) {
            btn.addEventListener('click', function() {
                const modal = this.closest('.modal');
                modal.style.display = 'none';
                // بازگرداندن اسکرول صفحه
                document.body.classList.remove('modal-open');
                const scrollTop = parseInt(document.body.style.top || '0') * -1;
                document.body.style.top = '';
                window.scrollTo(0, scrollTop);
            });
        });

        const historyModal = document.getElementById('history-modal');
        const historyContainer = document.getElementById('history-cards-container');
        const historyFilterBtn = document.getElementById('history-filter-btn');
        const toggleFiltersBtn = document.getElementById('toggle-filters-btn');
        const advancedFilters = document.getElementById('advanced-filters');
        const clearFiltersBtn = document.getElementById('clear-filters-btn');
        const historySearchInput = document.getElementById('history-search');
        const historyFromDateInput = document.getElementById('history-from-date');
        const historyToDateInput = document.getElementById('history-to-date');
        const historyStatusInput = document.getElementById('history-status');
        const filterInputs = [historySearchInput, historyFromDateInput, historyToDateInput, historyStatusInput];

        function updateClearButtonVisibility() {
            const isActive = filterInputs.some(input => (input.value.trim() !== '' && input.value !== 'all'));
            clearFiltersBtn.style.display = isActive ? 'inline-block' : 'none';
        }

        document.getElementById('history-btn').addEventListener('click', () => {
            // جلوگیری از اسکرول صفحه پشت
            const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
            document.body.style.top = `-${scrollTop}px`;
            document.body.classList.add('modal-open');

            historyModal.style.display = 'block';
            fetchAndRenderHistory();
            updateClearButtonVisibility();
        });

        toggleFiltersBtn.addEventListener('click', () => {
            const isVisible = advancedFilters.style.display === 'flex';
            advancedFilters.style.display = isVisible ? 'none' : 'flex';
            toggleFiltersBtn.classList.toggle('open');
        });

        historyFilterBtn.addEventListener('click', () => {
            fetchAndRenderHistory();
            updateClearButtonVisibility();
        });

        clearFiltersBtn.addEventListener('click', () => {
            filterInputs.forEach(input => input.value = (input.tagName === 'SELECT' ? 'all' : ''));
            fetchAndRenderHistory();
            updateClearButtonVisibility();
            advancedFilters.style.display = 'none';
            toggleFiltersBtn.classList.remove('open');
        });

        filterInputs.forEach(input => {
            input.addEventListener('input', updateClearButtonVisibility);
            input.addEventListener('change', updateClearButtonVisibility);
        });

        function fetchAndRenderHistory() {
            historyContainer.innerHTML = '<p>در حال بارگذاری سوابق...</p>';
            const params = new URLSearchParams({
                action: 'get_history_list',
                search: historySearchInput.value,
                from_date: historyFromDateInput.value,
                to_date: historyToDateInput.value,
                status: historyStatusInput.value
            });
            
            fetch(`my_tasks.php?${params.toString()}`)
            .then(res => res.json())
            .then(data => {
                if(data.error) {
                    historyContainer.innerHTML = `<p class="no-tasks">خطا در بارگذاری اطلاعات: ${data.error}</p>`;
                    return;
                }
                if (data.success && data.history.length > 0) {
                    let cardsHtml = '';
                    data.history.forEach(task => {
                        let statusClass = '';
                        switch (task.status) {
                            case 'انجام شده': statusClass = 'status-انجام-شده'; break;
                            case 'منتظر تایید': statusClass = 'status-منتظر-تایید'; break;
                            case 'تایید شده': statusClass = 'status-تایید-شده'; break;
                            case 'بسته شده': statusClass = 'status-بسته-شده'; break;
                            case 'انجام و تایید شده': statusClass = 'status-done-confirmed'; break;
                        }
                        const notes = task.completion_notes ? task.completion_notes.replace(/\n/g, '<br>') : 'ثبت نشده';
                        const labor = task.labor_users || 'ثبت نشده';
                        const parts = task.parts_used ? task.parts_used.replace(/,/g, '<br>') : 'ثبت نشده';
                        const delayReason = task.delay_reason ? `<p><strong>دلیل تاخیر:</strong><br>${task.delay_reason.replace(/\n/g, '<br>')}</p>` : '';
                        const verifier = task.verifier_name || 'ثبت نشده';
                        const startDate = task.actual_start_datetime_shamsi || 'ثبت نشده';
                        const endDate = task.actual_end_datetime_shamsi || 'ثبت نشده';

                        cardsHtml += `
                            <div class="history-card">
                                <div class="history-card-main">
                                    <div class="history-card-summary">
                                        <div class="history-card-title-line">
                                            <strong>${task.title}</strong>
                                            <span class="status-badge ${statusClass}">${task.status}</span>
                                        </div>
                                        <p>${task.device_name}</p>
                                        <p><small>${endDate}</small></p>
                                    </div>
                                    <button class="history-card-toggle"><i class="fas fa-chevron-down"></i></button>
                                </div>
                                <div class="history-card-collapsible-content">
                                    <hr class="history-card-divider">
                                    <div class="history-card-details">
                                        <p><strong>شرح اقدامات:</strong><br>${notes}</p>
                                        ${delayReason}
                                        <p><strong>نفرات:</strong> ${labor}</p>
                                        <p><strong>قطعات مصرفی:</strong><br>${parts}</p>
                                        <p><strong>شروع:</strong> ${startDate}</p>
                                        <p><strong>تحویل به:</strong> ${verifier}</p>
                                    </div>
                                </div>
                            </div>
                        `;
                    });
                    historyContainer.innerHTML = cardsHtml;
                } else {
                    historyContainer.innerHTML = '<p class="no-tasks">سابقه‌ای با این مشخصات یافت نشد.</p>';
                }
            });
        }
        
        historyContainer.addEventListener('click', function(e) {
            const header = e.target.closest('.history-card-main');
            if (header) {
                const card = header.parentElement;
                card.classList.toggle('active');
            }
        });

        const sourceReportModal = document.getElementById('source-report-modal');
        const sourceReportModalBody = document.getElementById('source-report-modal-body');

        function openSourceReportModal(taskId) {
            // جلوگیری از اسکرول صفحه پشت
            const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
            document.body.style.top = `-${scrollTop}px`;
            document.body.classList.add('modal-open');

            sourceReportModal.style.display = 'block';
            sourceReportModalBody.innerHTML = '<p style="text-align:center; padding: 2rem;">در حال بارگذاری جزئیات گزارش خرابی...</p>';

            fetch(`my_tasks.php?action=get_work_order_details&work_order_id=${taskId}`)
            .then(res => res.json())
            .then(data => {
                if (data.success) {
                    const { work_order, assignees, attachments } = data;
                    currentSourceReportData = work_order.source_report_details;
                    
                    let assigneesHtml = 'مشخص نشده';
                    if (assignees && assignees.length > 0) {
                        assigneesHtml = assignees.join('، ');
                    }

                    let priorityClass = '';
                    if(work_order.priority) {
                        const priority_map = {'بحرانی': 'bahrani', 'بالا': 'bala', 'متوسط': 'motavaset', 'پایین': 'paeen'};
                        priorityClass = priority_map[work_order.priority] || '';
                    }

                    let attachmentsHtml = '';
                    if (attachments && attachments.length > 0) {
                        attachmentsHtml += `<div class="detail-item detail-full-width"><strong>فایل‌های ضمیمه:</strong><div class="attachments-list">`;
                        attachments.forEach(filePath => {
                            const normalizedPath = `../${filePath.replace('../', '')}`;
                            attachmentsHtml += `<div class="attachment-item"><img src="${normalizedPath}" class="thumbnail-image" alt="پیوست"></div>`;
                        });
                        attachmentsHtml += `</div></div>`;
                    }

                    let sourceReportHtml = '';
                    if (work_order.source_report_details) {
                        sourceReportHtml = `<div class="detail-item detail-full-width"><strong>منبع:</strong><span>بر اساس <a href="javascript:void(0)" onclick="showSourceReportModal()">گزارش خرابی شماره ${work_order.source_report_details.id}</a> صادر شده است.</span></div>`;
                    }
                    
                    let lineStopHtml;
                    if (work_order.line_stopped == 1) {
                        lineStopHtml = `<strong>توقف خط:</strong><span>بله` + 
                                       (work_order.stop_datetime_shamsi && work_order.stop_datetime_shamsi !== '-' ? `، در تاریخ و ساعت: ${work_order.stop_datetime_shamsi}` : ` (زمان دقیق ثبت نشده)`) + 
                                       `</span>`;
                    } else {
                        lineStopHtml = `<strong>توقف خط:</strong><span>خیر</span>`;
                    }

                    const deviceInfoHtml = `${work_order.device_name || '-'} | ${work_order.serial_number || '---'} | ${work_order.location_name || '---'}`;

                    sourceReportModalBody.innerHTML = `
                        <div class="details-container">
                        <div class="details-grid">
                                <div class="details-grid-item"><strong>دستگاه:</strong> <span>${deviceInfoHtml}</span></div>
                                <div class="details-grid-item"><strong>مسئول(ان) اجرا:</strong><span>${assigneesHtml}</span></div>
                                <div class="details-grid-item"><strong>نوع دستور کار:</strong><span>${work_order.type}</span></div>
                                <div class="details-grid-item"><strong>ایجاد کننده:</strong><span>${work_order.requester_name || '-'}</span></div>
                                <div class="details-grid-item"><strong>اولویت:</strong><span class="priority-badge ${priorityClass}">${work_order.priority}</span></div>
                                <div class="details-grid-item"><strong>تاریخ سررسید:</strong><span>${work_order.due_date_shamsi}</span></div>
                            ${sourceReportHtml}
                            <div class="detail-item detail-full-width">${lineStopHtml}</div>
                            ${attachmentsHtml}
                            </div>
                        </div>
                    `;
                } else {
                    sourceReportModalBody.innerHTML = `<p class="no-tasks">خطا در دریافت اطلاعات: ${data.message}</p>`;
                }
            }).catch(err => {
                sourceReportModalBody.innerHTML = `<p class="no-tasks">یک خطای پیش‌بینی نشده رخ داد.</p>`;
                console.error(err);
            });
        }

        window.showSourceReportModal = function() {
            if (!currentSourceReportData) return;
            const reportData = currentSourceReportData;
            // جلوگیری از اسکرول صفحه پشت
            const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
            document.body.style.top = `-${scrollTop}px`;
            document.body.classList.add('modal-open');

            sourceReportModal.style.display = 'block';

            let imagesHtml = '';
            if (reportData.images && reportData.images.length > 0) {
                imagesHtml += `<h5>تصاویر پیوست:</h5><div class="attachments-list">`;
                reportData.images.forEach(imagePath => {
                    const fullImagePath = `../${imagePath.replace('../', '')}`;
                    imagesHtml += `<div class="attachment-item"><img src="${fullImagePath}" alt="تصویر خرابی" class="thumbnail-image"></div>`;
                });
                imagesHtml += `</div>`;
            }

            sourceReportModalBody.innerHTML = `
                <div class="details-container">
                    <div class="details-grid">
                        <div class="details-grid-item"><strong>دستگاه:</strong> <span>${reportData.device_name}</span></div>
                        <div class="details-grid-item"><strong>گزارش دهنده:</strong> <span>${reportData.reporter_name}</span></div>
                        <div class="details-grid-item"><strong>فوریت:</strong> <span>${reportData.urgency}</span></div>
                        <div class="details-grid-item"><strong>زمان خرابی:</strong> <span>${reportData.breakdown_datetime_shamsi}</span></div>
                        <div class="details-grid-item"><strong>زمان ثبت:</strong> <span>${reportData.report_datetime_shamsi}</span></div>
                        ${reportData.line_stoppage_datetime_shamsi ? `<div class="details-grid-item"><strong>زمان توقف خط:</strong> <span>${reportData.line_stoppage_datetime_shamsi}</span></div>` : ''}
                    </div>
                    <div class="details-section">
                        <h5>شرح کامل مشکل:</h5>
                        <p>${reportData.problem_description.replace(/\n/g, '<br>')}</p>
                    </div>
                    <div class="details-section">${imagesHtml}</div>
                </div>
            `;
        }

        document.querySelectorAll('.btn-details').forEach(button => {
            button.addEventListener('click', function() {
                openSourceReportModal(this.dataset.taskId);
            });
        });

        const imageViewerModal = document.getElementById('image-viewer-modal');
        const modalImage = document.getElementById('modal-image-content');
        function openImageViewer(src) {
            modalImage.src = src;
            imageViewerModal.style.display = "flex";
        }
        function closeImageViewer() {
            imageViewerModal.style.display = "none";
        }
        imageViewerModal.querySelector('.close-viewer').addEventListener('click', closeImageViewer);
        imageViewerModal.addEventListener('click', function(event) {
            if (event.target === imageViewerModal) {
                closeImageViewer();
            }
        });
        document.body.addEventListener('click', function(event) {
            if (event.target.classList.contains('thumbnail-image')) {
                openImageViewer(event.target.src);
            }
        });

        window.addEventListener('click', function(event) {
            document.querySelectorAll('.modal:not(#image-viewer-modal)').forEach(modal => {
                if (event.target == modal) {
                    modal.style.display = 'none';
                    // بازگرداندن اسکرول صفحه
                    document.body.classList.remove('modal-open');
                    const scrollTop = parseInt(document.body.style.top || '0') * -1;
                    document.body.style.top = '';
                    window.scrollTo(0, scrollTop);
                }
            });
        });

        if (typeof initializeDatepicker === 'function') {
            initializeDatepicker('#history-from-date');
            initializeDatepicker('#history-to-date');
        }

        document.querySelectorAll('.btn-outsource').forEach(btn => {
            btn.addEventListener('click', function() {
                // جلوگیری از اسکرول صفحه پشت
                const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
                document.body.style.top = `-${scrollTop}px`;
                document.body.classList.add('modal-open');

                document.getElementById('outsource-modal').style.display = 'block';
                document.getElementById('outsource-work-order-id').value = this.dataset.taskId;
            });
        });
        document.querySelectorAll('.close-modal-btn').forEach(function(btn) {
            btn.addEventListener('click', function() {
                this.closest('.modal').style.display = 'none';
            });
        });
        window.onclick = function(event) {
            if (event.target.classList.contains('modal')) {
                event.target.style.display = 'none';
            }
        }
        // راه‌اندازی تقویم شمسی برای تاریخ ارسال مودال برون سپاری
        initializeDatepicker('.persian-datepicker');
        // --- هندل کردن ارسال فرم برون سپاری با AJAX ---
        $('#outsource-form').on('submit', function(e) {
            e.preventDefault();
            var formData = $(this).serialize();
            $.post('my_tasks.php', formData + '&action=outsource_exit', function(response) {
                if (response.success) {
                    $('#outsource-modal').hide();
                    showToast('خروج دستگاه از سازمان ثبت شد', 'success');
                    setTimeout(function(){ location.reload(); }, 1200);
                } else {
                    showToast(response.message || 'خطا در ثبت خروج دستگاه', 'danger');
                }
            }, 'json').fail(function(){
                showToast('خطا در ارتباط با سرور', 'danger');
            });
        });

        // راه‌اندازی تقویم شمسی برای تاریخ ورود مودال بازگشت
        $('#back-org-date').persianDatepicker({
            format: 'YYYY/MM/DD',
            autoClose: true,
            initialValue: false
        });
        // باز کردن مودال بازگشت
        $(document).on('click', '.btn-back-org', function() {
            $('#back-org-modal').show();
            $('#back-org-work-order-id').val($(this).data('taskId'));
        });

        // --- هندل کردن ارسال فرم بازگشت با AJAX ---
        $('#back-org-form').on('submit', function(e) {
            e.preventDefault();
            var formData = $(this).serialize();
            $.post('my_tasks.php', formData + '&action=back_to_org', function(response) {
                if (response.success) {
                    $('#back-org-modal').hide();
                    showToast('اعلام بازگشت دستگاه با موفقیت ثبت شد.', 'success');
                    setTimeout(function(){ location.reload(); }, 1200);
                } else {
                    showToast(response.message || 'خطا در ثبت اعلام بازگشت', 'danger');
                }
            }, 'json').fail(function(){
                showToast('خطا در ارتباط با سرور', 'danger');
            });
        });

        // جداکننده هزارگان برای مبلغ فاکتور
        $(document).on('input', 'input[name="back_pay"]', function() {
            this.value = this.value.replace(/[^\d,]/g, '');
            let val = this.value.replace(/,/g, '');
            if (val && !isNaN(val)) {
                this.value = Number(val).toLocaleString('en-US');
            } else {
                this.value = '';
            }
        });
        // قبل از ارسال فرم، کاماها را حذف کن
        $('#back-org-form').on('submit', function() {
            let payInput = $(this).find('input[name="back_pay"]');
            payInput.val(payInput.val().replace(/,/g, ''));
        });

        // تغییر دکمه اعلام بازگشت به دکمه غیرفعال پایان برون سپاری پس از موفقیت
        $(document).ready(function() {
            // پس از ثبت موفقیت آمیز بازگشت، دکمه غیرفعال و متن تغییر کند
            $(document).on('submit', '#back-org-form', function(e) {
                var btn = $('.btn-back-org[data-task-id="'+$('#back-org-work-order-id').val()+'"]');
                setTimeout(function(){
                    btn.prop('disabled', true).removeClass('btn-back-org').addClass('btn-finished-outsource').css({'background':'#e0e0e0','color':'#888','border':'1px solid #ccc'}).html('<span class="task-status-icon"><i class="fas fa-check"></i></span> پایان برون سپاری');
                }, 1500);
            });
        });
    });
    </script>
</body>
</html>
