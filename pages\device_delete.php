<?php
require_once '../includes/auth.php';
require_once '../db_connection.php';

// Check delete permission
require_page_access('devices', 'delete');

if ($_SERVER['REQUEST_METHOD'] !== 'POST' || !isset($_POST['id'])) {
    $_SESSION['toast_message'] = 'درخواست نامعتبر است';
    $_SESSION['toast_type'] = 'danger';
    header('Location: devices.php');
    exit;
}

$id = (int)$_POST['id'];
$pdo = db_connect();

try {
    $pdo->beginTransaction();

    // 1. Get image filenames before deleting the device record
    $stmt = $pdo->prepare("SELECT images FROM devices WHERE id = ?");
    $stmt->execute([$id]);
    $images_json = $stmt->fetchColumn();

    // 2. Delete associated parts
    $stmt = $pdo->prepare("DELETE FROM device_parts WHERE device_id = ?");
    $stmt->execute([$id]);

    // 3. Delete associated motors
    $stmt = $pdo->prepare("DELETE FROM device_motors WHERE device_id = ?");
    $stmt->execute([$id]);

    // 4. Delete the device record
    $stmt = $pdo->prepare("DELETE FROM devices WHERE id = ?");
    $stmt->execute([$id]);

    // 5. If all DB operations are successful, delete the image files
    if ($images_json) {
        $images = json_decode($images_json, true);
        if (is_array($images)) {
            foreach ($images as $imageName) {
                $filePath = "../uploads/devices/" . $imageName;
                if (file_exists($filePath)) {
                    unlink($filePath);
                }
            }
        }
    }

    $pdo->commit();
    $_SESSION['toast_message'] = 'دستگاه و تمام اطلاعات مرتبط با آن با موفقیت حذف شد.';
    $_SESSION['toast_type'] = 'success';

} catch (PDOException $e) {
    $pdo->rollBack();
    $_SESSION['toast_message'] = 'خطا در حذف دستگاه: ' . $e->getMessage();
    $_SESSION['toast_type'] = 'danger';
}

header('Location: devices.php');
exit;