// assets/js/datepicker-init.js

// --- پچ سراسری و قطعی leapYearMode: 'astronomical' برای هر فراخوانی persianDatepicker در هر صفحه ---
if (window.jQuery && $.fn && $.fn.persianDatepicker) {
    if (!$.fn.persianDatepicker.defaults) $.fn.persianDatepicker.defaults = {};
    $.fn.persianDatepicker.defaults.calendar = {
        persian: {
            locale: 'fa',
            showHint: false,
            leapYearMode: 'astronomical'
        },
        gregorian: {
            locale: 'en',
            showHint: false
        }
    };
}

// --- پچ نهایی: value ورودی فیلدهای persian-datepicker قبل از initialize با persianDate نرمال می‌شود ---
if (window.jQuery && window.persianDate) {
    $(function () {
        $('.persian-datepicker').each(function() {
            var val = $(this).val();
            if (val) {
                try {
                    var norm = new persianDate(val);
                    if (norm.isValid && norm.isValid()) {
                        $(this).val(norm.format('YYYY/MM/DD'));
                    }
                } catch(e) {}
            }
        });
    });
}

function initializeDatepicker(selector) {
    if (!window.jQuery) {
        console.error("jQuery is not loaded.");
        return;
    }

    if (typeof $.fn.persianDatepicker !== 'function') {
        console.error("PersianDatepicker plugin is not loaded.");
        return;
    }

    if ($(selector).length) {
        $(selector).persianDatepicker({
            format: 'YYYY/MM/DD',
            initialValue: false,
            initialValueType: 'persian',
            observer: true,
            autoClose: true,
            calendarType: 'persian',
            calendar: {
                persian: {
                    locale: 'fa',
                    showHint: false,
                    leapYearMode: 'astronomical'
                },
                gregorian: {
                    locale: 'en',
                    showHint: false
                }
            },
            timePicker: {
                enabled: false
            }
        });
    } else {
        console.warn(`عنصر ${selector} در صفحه یافت نشد.`);
    }
}
