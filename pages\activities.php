<?php
session_start();
require_once '../includes/auth.php';
require_once '../db_connection.php';
require_once '../includes/date_helper.php';

// بررسی دسترسی به صفحه فعالیت‌ها
require_page_access('activities', 'view');

$pdo = db_connect();

// تابع پاسخ JSON
function json_response($data) {
    header('Content-Type: application/json');
    echo json_encode($data);
    exit;
}

// تابع بارگذاری لیست دستگاه‌ها
function get_devices() {
    global $pdo;
    // کوئری برای خواندن تمام اطلاعات لازم از دو جدول
    $stmt = $pdo->query("
        SELECT
            d.id,
            d.name,
            d.serial_number,
            l.location_name
        FROM
            devices d
        LEFT JOIN
            locations l ON d.location = l.id
        ORDER BY
            d.name
    ");
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

// تابع بارگذاری لیست دسته‌بندی‌ها
function get_categories() {
    global $pdo;
    $stmt = $pdo->query("SELECT id, name FROM categories ORDER BY name");
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

// تابع بارگذاری کاربران
function get_users() {
    global $pdo;
    $stmt = $pdo->query("SELECT id, name FROM users WHERE role != 'outsource' ORDER BY name");
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

// تابع بارگذاری پیوست‌های فعالیت
function get_attachments_for_activity($activity_id) {
    global $pdo;
    $stmt = $pdo->prepare("SELECT id, filename FROM activity_attachments WHERE activity_id = ?");
    $stmt->execute([$activity_id]);
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

// تابع بارگذاری مسئولین فعالیت
function get_assignees_for_activity($activity_id) {
    global $pdo;
    $stmt = $pdo->prepare("SELECT u.id, u.name
                            FROM users u
                            JOIN activity_assignees aa ON u.id = aa.user_id
                            WHERE aa.activity_id = ? AND u.role != 'outsource'");
    $stmt->execute([$activity_id]);
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

// عملیات POST
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    // افزودن فعالیت‌ها
    if ($action === 'add_activity') {
        $device_id = $_POST['device_id'] ?? null;
        $last_service_shamsi = $act['last_activity_time'] ?? null;
		$last_service = to_miladi($last_service_shamsi); // <-- تبدیل تاریخ

        // حل مشکل اصلی: دریافت و پردازش صحیح JSON
        $activities = [];
        if (!empty($_POST['activities'])) {
            $activities = json_decode($_POST['activities'], true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                $errorMsg = json_last_error_msg();
                json_response(['success' => false, 'errors' => ["خطا در پردازش JSON: $errorMsg"]]);
            }
        }
        
        $errors = [];
        if (!$device_id) $errors[] = "دستگاه انتخاب نشده است.";
        if (empty($activities)) $errors[] = "هیچ فعالیتی ارسال نشده است.";

        if (!empty($errors)) {
            json_response(['success' => false, 'errors' => $errors]);
        }

        try {
            $pdo->beginTransaction();
            $stmt_insert = $pdo->prepare("INSERT INTO activities (device_id, activity_name, service_interval_days, category_id, last_activity_time) VALUES (?, ?, ?, ?, ?)");
            $stmt_assignee = $pdo->prepare("INSERT INTO activity_assignees (activity_id, user_id) VALUES (?, ?)");

            // آرایه برای ذخیره شناسه‌های فعالیت‌های جدید
            $new_activity_ids = [];

            foreach ($activities as $index => $act) {
                $name = trim($act['activity_name'] ?? '');
                $interval = (int)($act['service_interval_days'] ?? 0);
                $cat_id = $act['category_id'] ?? null;
                $last_service_shamsi = $act['last_activity_time'] ?? null;
				$last_service = to_miladi($last_service_shamsi); // تبدیل به میلادی
                $assignees = $act['assignees'] ?? [];

                // تبدیل رشته خالی به null برای دسته‌بندی
                if ($cat_id === '') {
                    $cat_id = null;
                }

                if (empty($name) || $interval <= 0) {
                    $errors[] = "فعالیت #" . ($index + 1) . ": نام فعالیت و فاصله سرویس باید معتبر باشند.";
                    continue;
                }
                
                // اجرای درست کوئری با بررسی خطاها
                if (!$stmt_insert->execute([$device_id, $name, $interval, $cat_id, $last_service])) {
                    $errors[] = "خطا در ثبت فعالیت #" . ($index + 1);
                    continue;
                }
                
                $new_id = $pdo->lastInsertId();
                $new_activity_ids[] = $new_id;

                foreach ($assignees as $uid) {
                    if (!$stmt_assignee->execute([$new_id, $uid])) {
                        $errors[] = "خطا در ثبت مسئولین برای فعالیت #" . ($index + 1);
                    }
                }

                // === بارگذاری مستندات ارسالی برای این فعالیت ===
                if (isset($_FILES['new_attachments']) && isset($_FILES['new_attachments']['name'][$index])) {
                    $upload_dir = '../uploads/activities/';
                    if (!is_dir($upload_dir)) mkdir($upload_dir, 0777, true);

                    $files_for_activity = $_FILES['new_attachments'];

                    foreach ($files_for_activity['name'][$index] as $key => $filename) {
                        // اگر خطا وجود داشت از این فایل عبور کن
                        if ($files_for_activity['error'][$index][$key] !== UPLOAD_ERR_OK) continue;

                        $tmp_name = $files_for_activity['tmp_name'][$index][$key];
                        $cleanName = preg_replace('/[^a-zA-Z0-9\._-]/', '', $filename);
                        $new_filename = uniqid() . '_' . $cleanName;
                        $target_file = $upload_dir . $new_filename;

                        if (move_uploaded_file($tmp_name, $target_file)) {
                            $stmt_attachment = $pdo->prepare("INSERT INTO activity_attachments (activity_id, filename) VALUES (?, ?)");
                            $stmt_attachment->execute([$new_id, $new_filename]);
                        }
                    }
                }
            }

            if (!empty($errors)) {
                $pdo->rollBack();
                json_response([
                    'success' => false,
                    'errors' => $errors,
                    'debug' => ['activities' => $activities]
                ]);
            } else {
                $pdo->commit();
                json_response([
                    'success' => true, 
                    'message' => 'فعالیت‌ها با موفقیت ثبت شدند.',
                    'activity_ids' => $new_activity_ids
                ]);
            }
        } catch (PDOException $e) {
            $pdo->rollBack();
            json_response([
                'success' => false, 
                'errors' => ["خطا در ثبت اطلاعات: " . $e->getMessage()],
                'debug' => $e->getTrace()
            ]);
        }
    }
    
    // Handle delete activity action
    if ($action === 'delete_activity') {
        $activity_id = $_POST['activity_id'] ?? 0;
        if (!$activity_id) {
            json_response(['success' => false, 'errors' => ["فعالیت انتخاب نشده است."]]);
        }

        try {
            $pdo->beginTransaction();
            
            // First delete assignees
            $stmt = $pdo->prepare("DELETE FROM activity_assignees WHERE activity_id = ?");
            $stmt->execute([$activity_id]);
            
            // Then delete the activity
            $stmt = $pdo->prepare("DELETE FROM activities WHERE id = ?");
            $stmt->execute([$activity_id]);
            
            $pdo->commit();
            json_response(['success' => true, 'message' => 'فعالیت با موفقیت حذف شد.']);
        } catch (PDOException $e) {
            $pdo->rollBack();
            json_response(['success' => false, 'errors' => ["خطا در حذف فعالیت: " . $e->getMessage()]]);
        }
    }
    
    // Handle document upload
    if ($action === 'upload_documents') {
        $activity_id = $_POST['activity_id'] ?? 0;
        $documents = $_FILES['documents'] ?? [];
        
        if (!$activity_id) {
            json_response(['success' => false, 'errors' => ["فعالیت انتخاب نشده است."]]);
        }
        
        if (empty($documents) || $documents['error'][0] == 4) {
            json_response(['success' => false, 'errors' => ["هیچ فایلی برای آپلود انتخاب نشده است."]]);
        }
        
        try {
            $upload_dir = '../uploads/activities/';
            if (!is_dir($upload_dir)) {
                mkdir($upload_dir, 0777, true);
            }
            
            $stmt = $pdo->prepare("INSERT INTO activity_attachments (activity_id, filename) VALUES (?, ?)");
            $uploaded_files = [];
            
            // Process multiple files
            for ($i = 0; $i < count($documents['name']); $i++) {
                if ($documents['error'][$i] != UPLOAD_ERR_OK) {
                    continue;
                }
                
                $cleanName = preg_replace('/[^a-zA-Z0-9\._-]/', '', $documents['name'][$i]);
                $filename = uniqid() . '_' . $cleanName;
                $target_file = $upload_dir . $filename;
                
                if (move_uploaded_file($documents['tmp_name'][$i], $target_file)) {
                    $stmt->execute([$activity_id, $filename]);
                    $uploaded_files[] = $filename;
                }
            }
            
            json_response([
                'success' => true, 
                'message' => 'مستندات با موفقیت آپلود شدند.',
                'files' => $uploaded_files
            ]);
        } catch (Exception $e) {
            json_response(['success' => false, 'errors' => ["خطا در آپلود مستندات: " . $e->getMessage()]]);
        }
    }
    
    // Handle update activity
    if ($action === 'update_activity') {
        $activity_id = $_POST['activity_id'] ?? 0;
        $activity_name = $_POST['activity_name'] ?? '';
        $service_interval_days = $_POST['service_interval_days'] ?? 0;
        $category_id = $_POST['category_id'] ?? null;
        $assignees = $_POST['assignees'] ?? [];
        $last_activity_time_shamsi = $_POST['last_activity_time'] ?? null;
		
		 // فقط اگر تاریخ جدید ارسال شده، تبدیل انجام شود
		if ($last_activity_time_shamsi) {
			$last_activity_time = to_miladi($last_activity_time_shamsi);
		} else {
			$last_activity_time = null;
		}
		
        // Convert to array if not already
        if (!is_array($assignees)) {
            $assignees = [$assignees];
        }
        
        // تبدیل رشته خالی به null برای دسته‌بندی
        if ($category_id === '') {
            $category_id = null;
        }
        
        if (!$activity_id) {
            json_response(['success' => false, 'errors' => ["فعالیت انتخاب نشده است."]]);
        }
        
        if (empty($activity_name) || $service_interval_days <= 0) {
            json_response(['success' => false, 'errors' => ["نام فعالیت و فاصله سرویس باید معتبر باشند."]]);
        }
        
        try {
            $pdo->beginTransaction();
            
            // Update activity
            $stmt = $pdo->prepare("UPDATE activities 
                                    SET activity_name = ?, service_interval_days = ?, category_id = ?, last_activity_time = ?
                                    WHERE id = ?");
            $stmt->execute([$activity_name, $service_interval_days, $category_id, $last_activity_time, $activity_id]);
            
            // Update assignees
            $stmt_delete = $pdo->prepare("DELETE FROM activity_assignees WHERE activity_id = ?");
            $stmt_delete->execute([$activity_id]);
            
            $stmt_insert = $pdo->prepare("INSERT INTO activity_assignees (activity_id, user_id) VALUES (?, ?)");
            foreach ($assignees as $user_id) {
                $stmt_insert->execute([$activity_id, $user_id]);
            }
            
            $pdo->commit();
            json_response(['success' => true, 'message' => 'فعالیت با موفقیت به‌روزرسانی شد.']);
        } catch (PDOException $e) {
            $pdo->rollBack();
            json_response(['success' => false, 'errors' => ["خطا در به‌روزرسانی فعالیت: " . $e->getMessage()]]);
        }
    }
    
    // Handle delete document
    if ($action === 'delete_document') {
        $document_id = $_POST['document_id'] ?? 0;
        if (!$document_id) {
            json_response(['success' => false, 'errors' => ["سند انتخاب نشده است."]]);
        }

        try {
            // First, get the filename to delete the file
            $stmt = $pdo->prepare("SELECT filename FROM activity_attachments WHERE id = ?");
            $stmt->execute([$document_id]);
            $doc = $stmt->fetch(PDO::FETCH_ASSOC);

            if ($doc) {
                $file_path = '../uploads/activities/' . $doc['filename'];
                // Delete the file
                if (file_exists($file_path)) {
                    unlink($file_path);
                }
            }

            // Then delete the record
            $stmt = $pdo->prepare("DELETE FROM activity_attachments WHERE id = ?");
            $stmt->execute([$document_id]);

            json_response(['success' => true, 'message' => 'سند با موفقیت حذف شد.']);
        } catch (PDOException $e) {
            json_response(['success' => false, 'errors' => ["خطا در حذف سند: " . $e->getMessage()]]);
        }
    }
}
include '../includes/header.php';

// تابع برای ساخت هدرهای قابل مرتب‌سازی
function get_sortable_header($title, $column_name, $current_sort, $current_order) {
    $icon = '';
    $new_order = 'asc';

    if ($column_name === $current_sort) {
        if ($current_order === 'asc') {
            $new_order = 'desc';
            $icon = ' <i class="fas fa-sort-up"></i>';
        } elseif ($current_order === 'desc') {
            $new_order = ''; // کلیک سوم برای حذف مرتب‌سازی
            $icon = ' <i class="fas fa-sort-down"></i>';
        }
    }

    // کپی کردن پارامترهای فعلی GET
    $params = $_GET;

    if ($new_order) {
        $params['sort'] = $column_name;
        $params['order'] = $new_order;
    } else {
        // حذف پارامترهای مرتب‌سازی
        unset($params['sort'], $params['order']);
    }

    // ساخت کوئری استرینگ جدید
    $query_string = http_build_query($params);
    
    return '<th><a href="?' . $query_string . '" class="sortable-header">' . $title . $icon . '</a></th>';
}

// دریافت فیلترها و پارامترهای مرتب‌سازی
$filters = [
    'search' => $_GET['search'] ?? '',
];
$sort_column = $_GET['sort'] ?? '';
$sort_order = $_GET['order'] ?? '';

// بارگذاری داده‌ها
$devices = get_devices();
$categories = get_categories();
$users = get_users();

// بارگذاری فعالیت‌ها با فیلترها و مرتب‌سازی
$query = "SELECT a.*, 
                d.name as device_name, 
                c.name as category_name,
                DATE_ADD(a.last_activity_time, INTERVAL a.service_interval_days DAY) as next_service_date
            FROM activities a
            LEFT JOIN devices d ON a.device_id = d.id
            LEFT JOIN categories c ON a.category_id = c.id
            WHERE 1=1";
$params = [];

if ($filters['search']) {
    $query .= " AND (a.activity_name LIKE ? OR d.name LIKE ? OR c.name LIKE ? OR a.service_interval_days LIKE ?) ";
    $searchTerm = '%' . $filters['search'] . '%';
    $params[] = $searchTerm;
    $params[] = $searchTerm;
    $params[] = $searchTerm;
    $params[] = $searchTerm;
}

// لیست سفید ستون‌های قابل مرتب‌سازی
$sortable_columns = [
    'device_name' => 'd.name',
    'activity_name' => 'a.activity_name',
    'service_interval_days' => 'a.service_interval_days',
    'last_activity_time' => 'a.last_activity_time',
    'next_service_date' => 'next_service_date',
    'category_name' => 'c.name',
];

// اعمال مرتب‌سازی
if (isset($sortable_columns[$sort_column]) && in_array(strtolower($sort_order), ['asc', 'desc'])) {
    $query .= " ORDER BY " . $sortable_columns[$sort_column] . " " . strtoupper($sort_order);
} else {
    // مرتب‌سازی پیش‌فرض بر اساس وضعیت سرویس
    $query .= " ORDER BY 
                CASE 
                    WHEN next_service_date IS NULL THEN 4
                    WHEN next_service_date < CURDATE() THEN 1
                    WHEN next_service_date BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL 7 DAY) THEN 2
                    ELSE 3
                END,
                next_service_date ASC";
}

$stmt = $pdo->prepare($query);
$stmt->execute($params);
$activities = $stmt->fetchAll(PDO::FETCH_ASSOC);



// بارگذاری مستندات و مسئولین برای همه فعالیت‌ها
$activities_with_attachments = [];
foreach ($activities as $act) {
    $assignees = get_assignees_for_activity($act['id']);
    $attachments = get_attachments_for_activity($act['id']);
    
    // تعیین وضعیت سرویس
    $status = 'normal';
    if ($act['next_service_date']) {
        $next_service_timestamp = strtotime($act['next_service_date']);
        $today = time();
        $one_week_later = strtotime('+7 days');
        
        if ($next_service_timestamp < $today) {
            $status = 'overdue';
        } elseif ($next_service_timestamp <= $one_week_later) {
            $status = 'warning';
        }
    }
    
    $activities_with_attachments[] = [
        'id' => $act['id'],
        'device_name' => $act['device_name'],
        'activity_name' => $act['activity_name'],
        'service_interval_days' => $act['service_interval_days'],
        'category_name' => $act['category_name'] ?? '-',
        'category_id' => $act['category_id'] ?? null,
        'last_activity_time' => $act['last_activity_time'] ?? null,
        'last_activity_time_shamsi' => $act['last_activity_time'] 
            ? to_shamsi($act['last_activity_time']) 
            : '-',
        'next_service_date' => $act['next_service_date'] ?? null,
		'next_service_date_shamsi' => $act['next_service_date'] ? to_shamsi($act['next_service_date']) : '-', // <-- فیلد جدید برای نمایش
        'assignees' => $assignees,
        'attachments' => $attachments,
        'status' => $status
    ];
}
?>
<!DOCTYPE html>
<html lang="fa">
<head>
    <meta charset="UTF-8">
    <!-- افزودن متا تگ Viewport برای واکنش‌گرایی -->
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مدیریت فعالیت‌ها</title>
    <style>
        .sortable-header {
            color: inherit;
            text-decoration: none;
        }
        .sortable-header:hover {
            color: #007bff; /* یا هر رنگ دیگری که می‌پسندید */
        }
    </style>
    <!-- استایل‌های سفارشی برای واکنش‌گرایی -->
    <style>
        /* استایل‌های پایه و بهبودهای عمومی */
        * {
            box-sizing: border-box;
        }
        html, body {
            overflow-x: hidden; /* جلوگیری از اسکرول افقی در کل صفحه */
        }
        .container {
            padding-left: 15px;
            padding-right: 15px;
        }

        /* رفع مشکل اسکرول افقی جدول */
        .table-scroll-container {
            width: 100%;
            overflow-x: auto; /* اسکرول افقی فقط برای خود جدول در صورت نیاز */
            -webkit-overflow-scrolling: touch; /* اسکرول نرم در iOS */
        }

        /* بهبود استایل مودال (پاپ‌آپ) */
        .documents-popup {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.6);
            display: none; /* توسط جاوااسکریپت کنترل می‌شود */
            align-items: center;
            justify-content: center;
            z-index: 1000;
            padding: 15px;
        }
        .popup-content {
            background: #fff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
            width: 100%;
            max-width: 800px;
            max-height: 90vh; /* محدودیت ارتفاع برای جلوگیری از خروج از صفحه */
            overflow-y: auto; /* اسکرول عمودی در صورت بلند بودن محتوا */
            position: relative;
        }
        .document-item img {
            max-width: 100%;
            height: auto;
            border-radius: 4px;
        }

        /* واکنش‌گرایی کنترل‌های آپلود در مودال */
        .upload-controls {
            display: flex;
            flex-direction: column; /* چیدمان ستونی در موبایل */
            gap: 10px;
            margin-top: 15px;
            border-top: 1px solid #eee;
            padding-top: 15px;
        }
        .upload-controls input[type="file"],
        .upload-controls button {
            width: 100%;
        }
        
        .toggle-icon {
            display: none; /* مخفی کردن آیکون در حالت دسکتاپ */
        }

        /* وقتی بلاک در حالت مینیمایز است، بدنه مخفی می‌شود */
        .activity-block.collapsed .activity-body {
            display: none;
        }
        .activity-block .minimize-btn {
            cursor: pointer;
        }




        @media (min-width: 500px) {
            .upload-controls {
                flex-direction: row; /* چیدمان افقی در صفحات بزرگتر */
                align-items: center;
            }
            .upload-controls input[type="file"] {
                flex-grow: 1;
            }
            .upload-controls button {
                width: auto;
                flex-shrink: 0;
            }
        }

        /* =================================== */
        /* استایل‌های موبایل (برای نمایشگرهای کوچکتر از 768 پیکسل) */
        /* =================================== */
        @media (max-width: 768px) {
            .container {
                padding-left: 10px;
                padding-right: 10px;
            }
            
            .activities-layout {
                flex-direction: column; /* چیدمان ستونی برای فرم و لیست */
				padding:10px !important;
                gap: 20px;
            }

            .add-activity-column,
            .list-activities-column {
                width: 100%; /* عرض کامل برای هر دو ستون */
                margin: 0;
            }
            
            /* استایل‌های آکاردئون */
            .accordion-trigger {
                display: flex;
                justify-content: space-between;
                align-items: center;
                background-color: #f2f2f2;
               
                border-radius: 8px;
                cursor: pointer;
                
                
            }
            .accordion-trigger h3 {
                margin: 0;
                font-size: var(--fs-md);
            }
            .toggle-icon {
                display: block; /* نمایش آیکون در حالت موبایل */
                font-size: var(--fs-xl);
                font-weight: bold;
                transition: transform 0.2s ease-in-out;
            }
            .accordion-trigger.active .toggle-icon {
                transform: rotate(45deg);
            }
            #addActivityForm {
                display: none; /* مخفی کردن فرم به صورت پیش‌فرض */
                border: 1px solid #ddd;
                padding: 15px;
                border-radius: 8px;
            }


            .activities-table {
                border: none; /* حذف بوردر اصلی جدول */
                width: 100%;
            }

            .activities-table thead {
                display: none; /* مخفی کردن سربرگ جدول در موبایل */
            }

            .activities-table tr {
                display: block; /* نمایش هر ردیف به عنوان یک بلاک کارت-مانند */
                margin-bottom: 20px;
                border: 1px solid #ddd;
                border-radius: 8px;
                padding: 15px;
                box-shadow: 0 2px 4px rgba(0,0,0,0.05);
                background-color: #fff;
            }
            
            .activities-table tr.overdue {
                 border-right: 5px solid #e74c3c;
            }
            .activities-table tr.warning {
                 border-right: 5px solid #f39c12;
            }
            .activities-table tr.normal {
                 border-right: 5px solid #2ecc71;
            }

            .activities-table td {
                display: flex; /* استفاده از فلکس برای چیدمان بهتر محتوا */
                justify-content: space-between; /* محتوا در دو طرف سلول قرار می‌گیرد */
                align-items: center;
                padding: 10px 5px;
                text-align: right; /* تراز متن به سمت راست */
                
            }

            .activities-table td:last-child {
                border-bottom: none; /* حذف خط زیرین برای آخرین سلول */
            }

            /* افزودن عنوان ستون قبل از محتوای هر سلول */
            .activities-table td::before {
                content: attr(data-label); /* خواندن عنوان از اتریبیوت data-label */
                font-weight: bold;
                margin-left: 10px;
                color: #333;
                flex-shrink: 0; /* جلوگیری از کوچک شدن عنوان */
            }
            
            .activities-table td[data-label="عملیات"] {
                flex-direction: row;
                justify-content: flex-end;
                gap: 5px;
            }
            
            .activities-table td[data-label="عملیات"]::before {
                display: none; /* عدم نمایش لیبل برای ستون عملیات */
            }
            
            .activities-table td button {
                padding: 8px 12px;
                font-size: var(--fs-xs);
            }

            /* استایل برای فرم ویرایش در جدول */
            .edit-form-row td {
                display: flex; /* تغییر به فلکس برای نمایش لیبل */
                justify-content: space-between;
                align-items: center;
                padding: 5px;
            }
            .edit-form-row td::before {
                 display: block; /* نمایش لیبل در حالت ویرایش */
                 content: attr(data-label);
                 font-weight: bold;
                 margin-left: 10px;
                 color: #333;
                 flex-shrink: 0;
            }
             .edit-form-row td[data-label="عملیات"]::before {
                 display: none;
             }
            .edit-form-row input, .edit-form-row select {
                width: 100%;
                box-sizing: border-box;
            }
             /* حذف عرض حداقلی که باعث سرریز می‌شد */
            .edit-form-row select[name="assignees"] {
                min-width: 0;
            }
            
            .toolbar {
                flex-direction: column;
                gap: 10px;
            }

            #documentsPopup .popup-content {
                width: 95%;
                padding: 15px;
            }
            
            .documents-grid {
                display: block;
            }
        }
         /* === سفارشی‌سازی جدید برای نمایش فقط بندانگشتی و آیکون حذف === */
         #documentsContainer .document-item {
             position: relative !important;
             display: inline-block !important;
             margin: 6px !important;
         }
         #documentsContainer .document-item .thumbnail-image {
             max-width: 100px;
             max-height: 100px;
             border-radius: 6px;
             cursor: pointer;
         }
         #documentsContainer .document-item .delete-document-btn {
             position: absolute !important;
             top: 2px !important;
             right: 2px !important;
             width: 20px !important;
             height: 20px !important;
             line-height: 18px !important;
             border-radius: 50% !important;
             background: rgba(0,0,0,0.6) !important;
             color: #fff !important;
             border: none !important;
             text-align: center !important;
             cursor: pointer;
             font-size: var(--fs-sm) !important;
             font-weight: bold !important;
             padding: 0 !important; /* حذف هرگونه پدینگ اضافی */
         }
         #documentsContainer .document-item .delete-document-btn:hover {
            background: #e74c3c !important;
        }
         
        /* === [جدید] استایل‌های موبایل برای مودال مستندات === */
        @media (max-width: 768px) {
            #documentsContainer .document-item {
                display: block !important;
                width: 100% !important;
                margin: 10px 0 !important; /* فاصله‌گذاری عمودی */
            }

            #documentsContainer .document-item .thumbnail-image,
            #documentsContainer .document-item a > div {
                width: 100% !important;
                max-width: 100% !important;
                height: auto !important;
                max-height: none !important; /* حذف محدودیت ارتفاع */
            }

            #documentsContainer .document-item a > div {
                height: 200px !important; /* ارتفاع ثابت برای فایل‌های غیرتصویری */
                font-size: 60px !important;
            }
        }
    </style>
</head>
<body class="activities-page">
<div class="container">
    <div class="activities-layout"> 
        <div class="add-activity-column">
            <div class="accordion-trigger">
                <h3>افزودن فعالیت جدید</h3>
                <span class="toggle-icon">+</span>
            </div>
			<div class="accardeon-panel">
            <form id="addActivityForm">
                <div class="form-group">
                    <label> نام دستگاه :</label>
                    <select name="device_id" id="device-select" required>
                        <option value="">انتخاب دستگاه</option>
                        <?php foreach ($devices as $d): ?>
                        <option 
                            value="<?= $d['id'] ?>" 
                            data-location="<?= htmlspecialchars($d['location_name'] ?: 'بدون محل') ?>" 
                            data-serial="<?= htmlspecialchars($d['serial_number'] ?: 'بدون سریال') ?>">
                            
                            <?= htmlspecialchars($d['name']) ?>
                        </option>
                        <?php endforeach; ?>
                    </select>
					
                </div>
                <div id="newActivitiesPlaceholder">
                    <div class="activity-block">
                        <div class="activity-header">
                            <span class="activity-title">فعالیت #1</span>
                            <div class="activity-controls">
                                <button type="button" class="minimize-btn" title="کوچک کردن">_</button>
                                <button type="button" class="remove-btn" title="حذف">×</button>
                            </div>
                        </div>
                        <div class="activity-body">
                            <div class="form-group">
                                <label>عنوان فعالیت:</label>
                                <input type="text" data-field="activity_name" required />
                            </div>
                            <div class="form-group">
                                <label>فاصله سرویس (روز):</label>
                                <input type="number" data-field="service_interval_days" min="1" required />
                            </div>
                            <div class="form-group">
                                <label>تاریخ آخرین سرویس:</label>
                                <input type="text" class="persian-datepicker" data-field="last_activity_time" placeholder="انتخاب تاریخ" required />

                            </div>
                            <div class="form-group">
                                <label>مسئول اجرا:</label>
                                <select data-field="assignees" multiple>
                                    <?php foreach ($users as $u): ?>
                                        <option value="<?= $u['id'] ?>"><?= htmlspecialchars($u['name']) ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="form-group">
                                <label>دسته‌بندی:</label>
                                <select data-field="category_id">
                                    <option value="">بدون دسته‌بندی</option>
                                    <?php foreach ($categories as $cat): ?>
                                        <option value="<?= $cat['id'] ?>"><?= htmlspecialchars($cat['name']) ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="form-group">
                                <?php
                                require_once '../includes/file_uploader.php';
                                echo render_file_uploader([
                                    'id' => 'activityAttachments_' . uniqid(),
                                    'name' => 'activity_attachments[]',
                                    'accept' => 'image/*,.pdf',
                                    'max_size' => 2,
                                    'label' => 'مستندات',
                                    'description' => 'فایل‌های عکس و PDF - حداکثر 2 مگابایت',
                                    'show_existing' => false
                                ]);
                                ?>
                            </div>
                        </div>
                    </div>
                </div>
                <button type="button" id="addMoreActivitiesBtn">+ فعالیت جدید</button>
                <br><br>
                <button type="submit" id="save-activities-btn">افزودن فعالیت ها</button>
            </form>
			</div>
        </div>
        
        <div class="list-activities-column">
            <h3>لیست فعالیت های نت</h3>
            <div class="toolbar">
                <div class="search-container">
                    <input type="text" id="realTimeSearch" placeholder="جستجو در تمام فیلدها..." value="<?= htmlspecialchars($filters['search']) ?>">
                </div>
            </div>
            <div class="table-scroll-container">
            <table class="activities-table" border="1" cellspacing="0" cellpadding="4">
                <thead>
                    <tr>
                        <th>دستگاه</th>
                        <th>فعالیت</th>
                        <th>فاصله سرویس (روز)</th>
                        <th>آخرین سرویس</th>
                        <th>سرویس بعدی</th>
                        <th>دسته‌بندی</th>
                        <th>مسئولین اجرا</th>
                        <th>عملیات</th>
                    </tr>
                </thead>
                <tbody id="activitiesTableBody">
                    <?php if (empty($activities_with_attachments)): ?>
                        <tr class="no-results-row"><td colspan="8">فعالیتی یافت نشد.</td></tr>
                    <?php else: ?>
                        <?php foreach ($activities_with_attachments as $act): ?>
                        <tr data-activity-id="<?= $act['id'] ?>" class="<?= $act['status'] ?>">
                            <td data-label="دستگاه"><?= htmlspecialchars($act['device_name']) ?></td>
                            <td data-label="فعالیت" class="editable" data-field="activity_name"><?= htmlspecialchars($act['activity_name']) ?></td>
                            <td data-label="فاصله سرویس" class="editable" data-field="service_interval_days"><?= $act['service_interval_days'] ?></td>
                            <td data-label="آخرین سرویس" class="date-cell"><?= htmlspecialchars($act['last_activity_time_shamsi']) ?></td>
							<td data-label="سرویس بعدی" class="date-cell"><?= htmlspecialchars($act['next_service_date_shamsi']) ?></td>
                            <td data-label="دسته‌بندی" class="editable" data-field="category_id" data-current="<?= $act['category_id'] ?? 0 ?>">
                                <?= htmlspecialchars($act['category_name'] ?? '-') ?>
                            </td>
                            <td data-label="مسئولین" class="editable-multi" data-field="assignees">
                                <?= htmlspecialchars(implode(', ', array_column($act['assignees'], 'name'))) ?>
                            </td>
                            <td data-label="عملیات">
                                <?php if (!empty($act['attachments'])): ?>
                                    <button class="viewDocumentsBtn btn btn-sm btn-info" data-activity-id="<?= $act['id'] ?>" title="مشاهده مستندات">مستندات</button>
                                <?php endif; ?>
                                <button class="editActivityBtn btn btn-sm btn-warning" data-activity-id="<?= $act['id'] ?>" title="ویرایش">ویرایش</button>
                                <button class="deleteActivityBtn btn btn-sm btn-danger" data-activity-id="<?= $act['id'] ?>" title="حذف">حذف</button>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </tbody>
            </table>
			</div>
        </div>
    </div>
</div>

<!-- پاپ‌آپ نمایش مستندات با قابلیت‌های جدید -->
<div id="documentsPopup" class="documents-popup">
    <div class="popup-content">
        <span class="close-popup">&times;</span>
        <h3>مستندات فعالیت</h3>
        
        <div id="documentsContainer" class="documents-grid">
            <!-- مستندات اینجا نمایش داده می‌شوند -->
        </div>
        
        <div class="upload-section">
            <?php
            require_once '../includes/file_uploader.php';
            echo render_file_uploader([
                'id' => 'activityDocuments',
                'name' => 'documents[]',
                'accept' => 'image/*,.pdf',
                'max_size' => 2,
                'label' => 'افزودن مستندات جدید',
                'description' => 'فایل‌های عکس و PDF - حداکثر 2 مگابایت',
                'show_existing' => false
            ]);
            ?>
            <button type="button" id="uploadNewDocumentsBtn" class="btn btn-primary" style="margin-top: 1rem;">آپلود مستندات</button>
        </div>
    </div>
</div>
<?php include '../includes/footer.php'; ?>

<script>

$(document).ready(function() {
	 if (history.scrollRestoration) {
        history.scrollRestoration = 'manual';
    }

    // ==========================================================
    // بخش ۱: راه‌اندازی کتابخانه Select2 با استایل سفارشی
    // ==========================================================
    $('#device-select').select2({
        placeholder: ' جستجو یا انتخاب دستگاه...',
        allowClear: true,
        width: '100%',
        templateResult: function(data) {
            if (!data.id) {
                return data.text;
            }
            var location = $(data.element).data('location');
            var serial = $(data.element).data('serial');
            var $result = $(
                '<span>' +
                data.text +
                ' <span class="select2-extra-info">| ' + location + ' | ' + serial + '</span>' +
                '</span>'
            );
            return $result;
        }
    });

    // ==========================================================
    // بخش ۲: تعریف متغیرها و داده‌های اولیه
    // ==========================================================


    const allUsers = <?= json_encode($users) ?>;
    const allCategories = <?= json_encode($categories) ?>;
    const activitiesData = <?= json_encode($activities_with_attachments) ?>;
    let currentActivityIdForDocuments = null;
    let documentsPopup = document.getElementById('documentsPopup');
    let documentsContainer = document.getElementById('documentsContainer');
    const addMoreBtn = document.getElementById('addMoreActivitiesBtn');
    const activitiesContainer = document.getElementById('newActivitiesPlaceholder');
    let activityIndex = 0; // مقدار اولیه صفر تا اولین بلاک شماره ۱ بگیرد

    // ==========================================================
    // بخش ۳: تمام توابع و رویدادهای صفحه
    // ==========================================================
    
    // *** NEW ***
    // تابع برای راه‌اندازی Select2 روی فیلدهای مشخص
    function initializeSelect2InContext(context) {
        // برای فیلد مسئولین
        $(context).find('select[data-field="assignees"], select[name="assignees"]').select2({
            placeholder: 'مسئول یا مسئولین را انتخاب کنید',
            width: '100%',
            closeOnSelect: false // برای انتخاب چندگانه، منو باز بماند
        });
        // برای فیلد دسته‌بندی
        $(context).find('select[data-field="category_id"], select[name="category_id"]').select2({
           placeholder: 'دسته بندی را انتخاب کنید',
           width: '100%',
           allowClear: true
       });
    }

    // *** CHANGED ***
    // راه‌اندازی Select2 برای فرم اولیه در زمان بارگذاری صفحه
    initializeSelect2InContext('#addActivityForm');




    // افزودن بلاک فعالیت جدید
    addMoreBtn.addEventListener('click', function() {
        let userOptions = allUsers.map(user => `<option value="${user.id}">${user.name}</option>`).join('');
        let categoryOptions = '<option value="">بدون دسته‌بندی</option>' + allCategories.map(cat => `<option value="${cat.id}">${cat.name}</option>`).join('');
        activityIndex++;
        
        const newActivity = document.createElement('div');
        newActivity.className = 'activity-block';
        newActivity.innerHTML = `
            <div class="activity-header">
                <span class="activity-title">فعالیت #${activityIndex}</span>
                <div class="activity-controls">
                    <button type="button" class="minimize-btn" title="کوچک کردن">_</button>
                    <button type="button" class="remove-btn" title="حذف">×</button>
                </div>
            </div>
            <div class="activity-body">
                <div class="form-group"><label>عنوان فعالیت:</label><input type="text" data-field="activity_name" required /></div>
                <div class="form-group"><label>فاصله سرویس (روز):</label><input type="number" data-field="service_interval_days" min="1" required /></div>
                <div class="form-group"><label>تاریخ آخرین سرویس:</label><input type="text" class="persian-datepicker" data-field="last_activity_time" placeholder="انتخاب تاریخ" required /></div>
                <div class="form-group"><label>مسئول اجرا:</label><select data-field="assignees" multiple>${userOptions}</select></div>
                <div class="form-group"><label>دسته‌بندی:</label><select data-field="category_id">${categoryOptions}</select></div>
                <div class="form-group">
                    <div class="activity-file-uploader-placeholder">
                        <!-- کامپوننت آپلود فایل اینجا اضافه می‌شود -->
                    </div>
                </div>
            </div>`;
        
        // راه‌اندازی کامپوننت آپلود فایل قبل از اضافه کردن به DOM
        const fileUploaderPlaceholder = newActivity.querySelector('.activity-file-uploader-placeholder');
        const uniqueId = 'activityAttachments_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);

        // ایجاد کامپوننت آپلود فایل مشابه کامپوننت اصلی
        fileUploaderPlaceholder.innerHTML = `
            <div class="file-uploader-container" id="${uniqueId}Container">
                <label class="file-uploader-label">مستندات:</label>

                <div class="file-drop-area" id="${uniqueId}DropArea">
                    <div class="drop-area-content">
                        <i class="fas fa-cloud-upload-alt drop-icon"></i>
                        <p class="drop-text">فایل‌ها را اینجا بکشید و رها کنید یا کلیک کنید</p>
                        <button type="button" class="btn-select-files" id="${uniqueId}SelectBtn">
                            انتخاب فایل‌ها
                        </button>
                    </div>
                    <input type="file"
                           id="${uniqueId}Input"
                           name="activity_attachments[]"
                           accept="image/*,.pdf"
                           multiple
                           style="display: none;">
                </div>

                <div class="new-files-preview" id="${uniqueId}Preview"></div>

                <div class="file-uploader-info">
                    <small>فایل‌های عکس و PDF - حداکثر 2 مگابایت</small>
                </div>
            </div>
        `;

        activitiesContainer.appendChild(newActivity);
        updateActivityNumbers(); // به‌روزرسانی شماره‌ها پس از افزودن

        // راه‌اندازی کتابخانه‌ها برای بلاک جدید
        initializeSelect2InContext(newActivity); // Select2 روی فیلدهای جدید

        initializeDatepicker($(newActivity).find('.persian-datepicker'));

        // راه‌اندازی کنترل‌های بلاک
        setupActivityControls(newActivity);

        // **فراخوانی تابع markRequiredFields برای فعالیت جدید**
        setTimeout(function() {
            if (typeof markRequiredFields === 'function') {
                markRequiredFields();
                console.log('markRequiredFields called for new activity');
            } else {
                console.error('markRequiredFields function not found');
            }
        }, 100); // تأخیر کافی برای اطمینان از اضافه شدن به DOM

        // راه‌اندازی کامپوننت آپلود فایل
        setTimeout(() => {
            if (typeof FileUploader !== 'undefined') {
                window.fileUploaders = window.fileUploaders || {};
                window.fileUploaders[uniqueId] = new FileUploader(uniqueId, {
                    maxSize: 2 * 1024 * 1024,
                    maxFiles: 10,
                    allowedTypes: ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'application/pdf'],
                    allowedExtensions: ['jpg', 'jpeg', 'png', 'gif', 'pdf']
                });
                console.log('FileUploader initialized for new activity:', uniqueId);
            } else {
                console.error('FileUploader class not found');
                // تلاش مجدد
                setTimeout(() => {
                    if (typeof FileUploader !== 'undefined') {
                        window.fileUploaders = window.fileUploaders || {};
                        window.fileUploaders[uniqueId] = new FileUploader(uniqueId, {
                            maxSize: 2 * 1024 * 1024,
                            maxFiles: 10,
                            allowedTypes: ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'application/pdf'],
                            allowedExtensions: ['jpg', 'jpeg', 'png', 'gif', 'pdf']
                        });
                        console.log('FileUploader initialized for new activity (retry):', uniqueId);
                    }
                }, 500);
            }
        }, 200);
    });

    // استفاده از Delegation برای کنترل‌های کوچک‌سازی و حذف، تا اولین بلوک و تمام بلوک‌های بعدی به‌درستی کار کنند
    activitiesContainer.addEventListener('click', function(e) {
        // دکمه کوچک‌سازی
        if (e.target.classList.contains('minimize-btn')) {
            const block = e.target.closest('.activity-block');
            if (block) {
                block.classList.toggle('collapsed');
            }
        }
        // دکمه حذف
        if (e.target.classList.contains('remove-btn')) {
            const block = e.target.closest('.activity-block');
            if (block && activitiesContainer.querySelectorAll('.activity-block').length > 1) {
                $(block).find('select').select2('destroy');
                block.remove();
                updateActivityNumbers();
            } else if (block) {
                alert('حداقل یک فعالیت باید وجود داشته باشد.');
            }
        }
    });

    // تابعی برای عملیات اضافی (Select2 و ...) که هنوز نیاز به راه‌اندازی تکی دارند
    function setupActivityControls(block) {
        // دیگر نیازی به بایند کردن رویدادهای کلیک نیست؛ فقط سایر تنظیمات در صورت نیاز اینجا قرار می‌گیرد.
    }

    // به‌روزرسانی شماره فعالیت‌ها
    function updateActivityNumbers() {
        const blocks = activitiesContainer.querySelectorAll('.activity-block');
        blocks.forEach((block, index) => {
            const title = block.querySelector('.activity-title');
            if (title) {
                title.textContent = `فعالیت #${index + 1}`;
            }
        });
    }

    // راه‌اندازی کنترل‌های فعالیت‌های موجود
    document.querySelectorAll('.activity-block').forEach(block => {
        setupActivityControls(block);
    });

    // شماره‌گذاری اولیه
    updateActivityNumbers();

    // ارسال فرم اصلی
    document.getElementById('addActivityForm').addEventListener('submit', async function(e) {
        e.preventDefault();
        
        const formData = new FormData();
        formData.append('action', 'add_activity');
        formData.append('device_id', this.querySelector('[name="device_id"]').value);
        
        const newActivitiesData = [];
        const filesData = [];
        
        this.querySelectorAll('.activity-block').forEach((block, index) => {
            const activity = {
                activity_name: block.querySelector('[data-field="activity_name"]').value,
                service_interval_days: block.querySelector('[data-field="service_interval_days"]').value,
                last_activity_time: block.querySelector('[data-field="last_activity_time"]').value,
                category_id: block.querySelector('[data-field="category_id"]').value,
                assignees: Array.from(block.querySelector('[data-field="assignees"]').selectedOptions).map(opt => opt.value)
            };
            newActivitiesData.push(activity);
            // ارسال فایل‌های هر بلاک از کامپوننت آپلود فایل
            // جستجو در کامپوننت اصلی (فعالیت اول)
            let fileUploaderContainer = block.querySelector('.file-uploader-container');

            // اگر پیدا نشد، جستجو در کامپوننت‌های جدید
            if (!fileUploaderContainer) {
                fileUploaderContainer = block.querySelector('.activity-file-uploader-placeholder .file-uploader-container');
            }

            if (fileUploaderContainer) {
                // پیدا کردن ID کامپوننت
                const uploaderInput = fileUploaderContainer.querySelector('input[type="file"]');
                if (uploaderInput) {
                    const uploaderId = uploaderInput.id.replace('Input', '');
                    console.log(`Activity ${index + 1} - Uploader ID:`, uploaderId);

                    if (uploaderId && window.fileUploaders && window.fileUploaders[uploaderId]) {
                        const uploader = window.fileUploaders[uploaderId];
                        const files = uploader.getSelectedFiles();
                        console.log(`Activity ${index + 1} - Selected files:`, files.length);

                        files.forEach(file => {
                            formData.append(`new_attachments[${index}][]`, file, file.name);
                            console.log(`Activity ${index + 1} - Added file:`, file.name);
                        });
                    } else {
                        console.log(`Activity ${index + 1} - No uploader found for ID:`, uploaderId);
                    }
                }
            } else {
                console.log(`Activity ${index + 1} - No file uploader container found`);
            }
        });
        
        formData.append('activities', JSON.stringify(newActivitiesData));
        const submitBtn = document.getElementById('save-activities-btn');
        submitBtn.disabled = true;
        submitBtn.textContent = 'در حال ذخیره...';
        
        try {
            const response = await fetch('activities.php', { method: 'POST', body: formData });
            const contentType = response.headers.get('content-type');
            if (!contentType || !contentType.includes('application/json')) {
                const text = await response.text();
                throw new Error(`پاسخ غیرمنتظره از سرور: ${text.substring(0, 100)}`);
            }
            const result = await response.json();
            
            if (result.success) {
                if (result.activity_ids && result.activity_ids.length > 0 && filesData.length > 0) {
                    const uploadPromises = [];
                    result.activity_ids.forEach((activityId, index) => {
                        if (filesData[index] && filesData[index].length > 0) {
                            const uploadForm = new FormData();
                            uploadForm.append('action', 'upload_documents');
                            uploadForm.append('activity_id', activityId);
                            filesData[index].forEach(file => {
                                uploadForm.append('documents[]', file, file.name);
                            });
                            uploadPromises.push(fetch('activities.php', { method: 'POST', body: uploadForm }));
                        }
                    });
                    await Promise.all(uploadPromises);
                }
                alert('فعالیت‌ها و مستندات با موفقیت ثبت شدند.');
                window.location.reload();
            } else {
                const errorMsg = result.errors ? result.errors.join('\n') : 'خطای نامشخص';
                alert('خطا: ' + errorMsg);
            }
        } catch (error) {
            alert('خطا در ارسال اطلاعات: ' + error.message);
            console.error('Fetch Error:', error);
        } finally {
            submitBtn.disabled = false;
            submitBtn.textContent = 'افزودن فعالیت ها';
        }
    });

    // حذف فعالیت
    document.body.addEventListener('click', async function(e) {
        if (e.target.classList.contains('deleteActivityBtn')) {
            if (!confirm('آیا از حذف این فعالیت مطمئن هستید؟')) return;
            
            const activityId = e.target.getAttribute('data-activity-id');
            const formData = new FormData();
            formData.append('action', 'delete_activity');
            formData.append('activity_id', activityId);
            
            try {
                const response = await fetch('activities.php', { method: 'POST', body: formData });
                const contentType = response.headers.get('content-type');
                if (!contentType || !contentType.includes('application/json')) {
                    const text = await response.text();
                    throw new Error(`پاسخ غیرمنتظره از سرور: ${text.substring(0, 100)}`);
                }
                const result = await response.json();
                
                if (result.success) {
                    alert(result.message);
                    e.target.closest('tr').remove();
                    showNoResultsMessage();
                } else {
                    const errorMsg = result.errors ? result.errors.join('\n') : 'خطای نامشخص';
                    alert('خطا: ' + errorMsg);
                }
            } catch (error) {
                alert('خطا در ارسال درخواست: ' + error.message);
                console.error(error);
            }
        }
    });

    // ویرایش فعالیت‌ها در لیست
    document.getElementById('activitiesTableBody').addEventListener('click', function(e) {
        if (e.target.classList.contains('editActivityBtn')) {
            const activityId = e.target.getAttribute('data-activity-id');
            editActivity(activityId);
        }
    });

    // تابع ویرایش فعالیت
    function editActivity(activityId) {
        const activity = activitiesData.find(a => a.id == activityId);
        if (!activity) return;
        
        const row = document.querySelector(`tr[data-activity-id="${activityId}"]`);
        if (!row) return;
        
        const editRow = document.createElement('tr');
        editRow.className = 'edit-form-row';
        editRow.dataset.activityId = activityId;
        
        let categoryOptions = allCategories.map(cat => `<option value="${cat.id}" ${activity.category_id == cat.id ? 'selected' : ''}>${cat.name}</option>`).join('');
        let assigneeOptions = allUsers.map(user => `<option value="${user.id}" ${activity.assignees.some(a => a.id == user.id) ? 'selected' : ''}>${user.name}</option>`).join('');
        const lastServiceDateShamsi = activity.last_activity_time_shamsi;
        
        editRow.innerHTML = `
            <td data-label="دستگاه">${activity.device_name}</td>
            <td data-label="فعالیت"><input type="text" class="edit-field" name="activity_name" value="${activity.activity_name}" required></td>
            <td data-label="فاصله سرویس"><input type="number" class="edit-field" name="service_interval_days" value="${activity.service_interval_days}" min="1" required></td>
            <td data-label="آخرین سرویس"><input type="text" class="edit-field persian-datepicker" name="last_activity_time" value="${lastServiceDateShamsi}" required></td>
            <td data-label="سرویس بعدی" class="date-cell">${activity.next_service_date_shamsi || '-'}</td>
            <td data-label="دسته‌بندی"><select class="edit-field" name="category_id"><option value="">بدون دسته‌بندی</option>${categoryOptions}</select></td>
            <td data-label="مسئولین"><select class="edit-field" name="assignees" multiple>${assigneeOptions}</select></td>
            <td data-label="عملیات"><div class="edit-controls"><button class="save-edit-btn save-btn">ذخیره</button><button class="cancel-edit-btn cancel-btn">لغو</button></div></td>`;
        
        row.replaceWith(editRow);
        
        // *** CHANGED ***
        // راه‌اندازی کتابخانه‌ها برای ردیف ویرایش
        initializeSelect2InContext(editRow); // اعمال Select2 روی فیلدهای ویرایش

        initializeDatepicker($(editRow).find('.persian-datepicker'));
        
        editRow.querySelector('.save-edit-btn').addEventListener('click', saveActivityEdit);
        editRow.querySelector('.cancel-edit-btn').addEventListener('click', cancelActivityEdit);
    }

    // تابع ذخیره ویرایش
    async function saveActivityEdit() {
        const editRow = this.closest('tr');
        const activityId = editRow.dataset.activityId;
        
        const formData = new FormData();
        formData.append('action', 'update_activity');
        formData.append('activity_id', activityId);
        formData.append('activity_name', editRow.querySelector('[name="activity_name"]').value);
        formData.append('service_interval_days', editRow.querySelector('[name="service_interval_days"]').value);
        formData.append('last_activity_time', editRow.querySelector('[name="last_activity_time"]').value);
        formData.append('category_id', editRow.querySelector('[name="category_id"]').value);
        
        const assigneesSelect = editRow.querySelector('[name="assignees"]');
        const selectedOptions = Array.from(assigneesSelect.selectedOptions);
        selectedOptions.forEach(option => {
            formData.append('assignees[]', option.value);
        });
        
        try {
            const response = await fetch('activities.php', { method: 'POST', body: formData });
            const contentType = response.headers.get('content-type');
            if (!contentType || !contentType.includes('application/json')) {
                const text = await response.text();
                throw new Error(`پاسخ غیرمنتظره از سرور: ${text.substring(0, 100)}`);
            }
            const result = await response.json();
            
            if (result.success) {
                alert(result.message);
                window.location.reload();
            } else {
                const errorMsg = result.errors ? result.errors.join('\n') : 'خطای نامشخص';
                alert('خطا: ' + errorMsg);
            }
        } catch (error) {
            alert('خطا در ارسال اطلاعات: ' + error.message);
            console.error('Fetch Error:', error);
        }
    }

    // تابع لغو ویرایش
    function cancelActivityEdit() {
        const editRow = this.closest('tr');
        const activityId = editRow.dataset.activityId;
        const activity = activitiesData.find(a => a.id == activityId);
        if (!activity) return;
        
        const mainRow = document.createElement('tr');
        mainRow.dataset.activityId = activityId;
        mainRow.className = activity.status;
        mainRow.innerHTML = `
            <td data-label="دستگاه">${activity.device_name}</td>
            <td data-label="فعالیت" class="editable" data-field="activity_name">${activity.activity_name}</td>
            <td data-label="فاصله سرویس" class="editable" data-field="service_interval_days">${activity.service_interval_days}</td>
            <td data-label="آخرین سرویس" class="date-cell">${activity.last_activity_time_shamsi || '-'}</td>
            <td data-label="سرویس بعدی" class="date-cell">${activity.next_service_date_shamsi || '-'}</td>
            <td data-label="دسته‌بندی" class="editable" data-field="category_id" data-current="${activity.category_id || 0}">${activity.category_name || '-'}</td>
            <td data-label="مسئولین" class="editable-multi" data-field="assignees">${activity.assignees.map(a => a.name).join(', ')}</td>
            <td data-label="عملیات">
                ${activity.attachments.length ? `<button class="viewDocumentsBtn btn btn-sm btn-info" data-activity-id="${activityId}" title="مشاهده مستندات">مستندات</button>` : ''}
                <button class="editActivityBtn btn btn-sm btn-warning" data-activity-id="${activityId}" title="ویرایش">ویرایش</button>
                <button class="deleteActivityBtn btn btn-sm btn-danger" data-activity-id="${activityId}" title="حذف">حذف</button>
            </td>`;
            
        editRow.replaceWith(mainRow);
    }

    // جستجوی آنی در جدول
    const realTimeSearch = document.getElementById('realTimeSearch');
    const tableBody = document.getElementById('activitiesTableBody');
    
    function performSearch() {
        const searchTerm = realTimeSearch.value.toLowerCase();
        const rows = tableBody.querySelectorAll('tr');
        
        rows.forEach(row => {
            if (row.classList.contains('no-results-row') || row.classList.contains('edit-form-row')) return;
            let matchFound = false;
            for (let i = 0; i < row.cells.length - 1; i++) {
                const cellText = row.cells[i].textContent.toLowerCase();
                if (cellText.includes(searchTerm)) {
                    matchFound = true;
                    break;
                }
            }
            if (searchTerm === '') {
                row.style.display = '';
            } else {
                row.style.display = matchFound ? '' : 'none';
            }
        });
        showNoResultsMessage();
    }

    function showNoResultsMessage() {
        const visibleRows = tableBody.querySelectorAll('tr:not([style*="display: none"]):not(.no-results-row):not(.edit-form-row)');
        let existingNoResults = tableBody.querySelector('.no-results-row');
        if (visibleRows.length === 0) {
            if (!existingNoResults) {
                const noResultsRow = document.createElement('tr');
                noResultsRow.className = 'no-results-row';
                noResultsRow.innerHTML = '<td colspan="8">هیچ فعالیتی با این مشخصات یافت نشد.</td>';
                tableBody.appendChild(noResultsRow);
            }
        } else {
            if (existingNoResults) {
                existingNoResults.remove();
            }
        }
    }
    
    realTimeSearch.addEventListener('input', performSearch);

    // مدیریت مستندات (پاپ‌آپ)
    const closePopup = document.querySelector('.close-popup');
    const uploadNewDocumentsBtn = document.getElementById('uploadNewDocumentsBtn');
    const newDocumentsInput = document.getElementById('newDocumentsInput');
    
    document.body.addEventListener('click', function(e) {
        if (e.target.classList.contains('viewDocumentsBtn')) {
            const activityId = e.target.getAttribute('data-activity-id');
            currentActivityIdForDocuments = activityId;
            const activity = activitiesData.find(a => a.id == activityId);
            if (activity && activity.attachments && activity.attachments.length > 0) {
                renderDocuments(activity.attachments);
            } else {
                documentsContainer.innerHTML = '<p>هیچ مستندی برای این فعالیت یافت نشد.</p>';
            }
            // جلوگیری از اسکرول صفحه پشت
            const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
            document.body.style.top = `-${scrollTop}px`;
            document.body.classList.add('modal-open');

            documentsPopup.style.display = 'flex';
        }
    });

    // نمایش مستندات در popup با thumbnail و بزرگ‌نمایی
    function renderDocuments(attachments) {
        documentsContainer.innerHTML = '';
        if (attachments.length === 0) {
            documentsContainer.innerHTML = '<p>هیچ مستندی برای این فعالیت یافت نشد.</p>';
            return;
        }
        attachments.forEach(doc => {
            const docItem = document.createElement('div');
            docItem.className = 'document-item';
            if (doc.filename.match(/\.(jpg|jpeg|png|gif)$/i)) {
                docItem.innerHTML = `
                    <img src="../uploads/activities/${doc.filename}" alt="" class="thumbnail-image" title="برای بزرگ‌نمایی کلیک کنید">
                    <button type="button" class="delete-document-btn" data-document-id="${doc.id}">&times;</button>`;
            } else {
                docItem.innerHTML = `
                    <a href="../uploads/activities/${doc.filename}" target="_blank" title="دانلود فایل">
                        <div style="width:100px;height:100px;display:flex;align-items:center;justify-content:center;background:#f1f1f1;border-radius:6px;font-size:32px;">📄</div>
                    </a>
                    <button type="button" class="delete-document-btn" data-document-id="${doc.id}">&times;</button>`;
            }
            documentsContainer.appendChild(docItem);
        });
        // delegation برای حذف و بزرگ‌نمایی
        documentsContainer.querySelectorAll('.thumbnail-image').forEach(img => {
            img.addEventListener('click', function() {
                showImageViewerModal(this.src);
            });
        });
        documentsContainer.querySelectorAll('.delete-document-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const documentId = this.getAttribute('data-document-id');
                deleteDocument(documentId);
            });
        });
    }

    async function deleteDocument(documentId) {
        if (!confirm('آیا از حذف این مستند مطمئن هستید؟')) return;
        try {
            const formData = new FormData();
            formData.append('action', 'delete_document');
            formData.append('document_id', documentId);
            const response = await fetch('activities.php', { method: 'POST', body: formData });
            const result = await response.json();
            if (result.success) {
                alert(result.message);
                const activity = activitiesData.find(a => a.id == currentActivityIdForDocuments);
                if (activity) {
                    const updatedAttachments = activity.attachments.filter(a => a.id != documentId);
                    activity.attachments = updatedAttachments; // Update the main data array
                    renderDocuments(updatedAttachments);
                }
            } else {
                alert('خطا: ' + (result.errors ? result.errors.join('\n') : 'خطای نامشخص'));
            }
        } catch (error) {
            alert('خطا در ارسال درخواست: ' + error.message);
        }
    }

    uploadNewDocumentsBtn.addEventListener('click', async function() {
        // دریافت فایل‌ها از کامپوننت file uploader
        const uploader = window.fileUploaders?.activityDocuments;
        if (!uploader) {
            alert('کامپوننت آپلود فایل یافت نشد');
            return;
        }

        const files = uploader.getSelectedFiles();
        if (files.length === 0) {
            alert('لطفاً حداقل یک فایل انتخاب کنید');
            return;
        }
        try {
            const formData = new FormData();
            formData.append('action', 'upload_documents');
            formData.append('activity_id', currentActivityIdForDocuments);
            files.forEach(file => {
                formData.append('documents[]', file);
            });
            const response = await fetch('activities.php', { method: 'POST', body: formData });
            const result = await response.json();
            if (result.success) {
                alert(result.message);
                uploader.clearFiles(); // پاک کردن فایل‌های انتخاب شده
                window.location.reload();
            } else {
                alert('خطا: ' + (result.errors ? result.errors.join('\n') : 'خطای نامشخص'));
            }
        } catch (error) {
            alert('خطا در آپلود مستندات: ' + error.message);
        }
    });
    
    closePopup.addEventListener('click', function() {
        documentsPopup.style.display = 'none';
        // بازگرداندن اسکرول صفحه
        document.body.classList.remove('modal-open');
        const scrollTop = parseInt(document.body.style.top || '0') * -1;
        document.body.style.top = '';
        window.scrollTo(0, scrollTop);
    });

    window.addEventListener('click', function(e) {
        if (e.target === documentsPopup) {
            documentsPopup.style.display = 'none';
            // بازگرداندن اسکرول صفحه
            document.body.classList.remove('modal-open');
            const scrollTop = parseInt(document.body.style.top || '0') * -1;
            document.body.style.top = '';
            window.scrollTo(0, scrollTop);
        }
    });

    // === [جدید: modal بزرگ‌نمایی تصویر فقط یکبار در انتهای body] ===
    if (!document.getElementById('imageViewerModal')) {
        const modalHtml = `
        <div id="imageViewerModal" class="image-modal" style="display:none;position:fixed;z-index:2000;left:0;top:0;width:100%;height:100%;background:rgba(0,0,0,0.9);align-items:center;justify-content:center;">
            <span class="image-modal-close" style="position:absolute;top:15px;right:35px;color:#f1f1f1;font-size: var(--fs-4xl);font-weight:bold;cursor:pointer;">&times;</span>
            <img class="image-modal-content" id="modalImageView" style="max-width:90vw;max-height:90vh;object-fit:contain;">
        </div>`;
        document.body.insertAdjacentHTML('beforeend', modalHtml);
    }
    function showImageViewerModal(src) {
        const modal = document.getElementById('imageViewerModal');
        const modalImg = document.getElementById('modalImageView');

        // جلوگیری از اسکرول صفحه پشت
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
        document.body.style.top = `-${scrollTop}px`;
        document.body.classList.add('modal-open');

        modalImg.src = src;
        modal.style.display = 'flex';
    }
    // === [جدید: بستن modal بزرگ‌نمایی] ===
    document.body.addEventListener('click', function(e) {
        const modal = document.getElementById('imageViewerModal');
        if (!modal) return;
        if (e.target === modal || (e.target.classList && e.target.classList.contains('image-modal-close'))) {
            modal.style.display = 'none';
            // بازگرداندن اسکرول صفحه
            document.body.classList.remove('modal-open');
            const scrollTop = parseInt(document.body.style.top || '0') * -1;
            document.body.style.top = '';
            window.scrollTo(0, scrollTop);
        }
    });

    // راه‌اندازی اولیه
    showNoResultsMessage();
    
    // تابع برای راه‌اندازی آکاردئون در حالت موبایل
    function setupAccordion() {
        const trigger = $('.accordion-trigger');
        const content = $('#addActivityForm');

        trigger.off('click').on('click', function() {
            if (window.innerWidth <= 768) {
                $(this).toggleClass('active');
                content.slideToggle(300);
            }
        });

        $(window).off('resize.accordion').on('resize.accordion', function() {
            if (window.innerWidth > 768) {
                content.show();
                trigger.removeClass('active');
            } else {
                if (!trigger.hasClass('active')) {
                    content.hide();
                }
            }
        }).trigger('resize');
    }

    setupAccordion();
});
</script>
</body>
</html>
