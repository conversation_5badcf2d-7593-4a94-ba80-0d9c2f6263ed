<?php
require_once '../includes/auth.php';
require_once '../db_connection.php';
header('Content-Type: application/json; charset=utf-8');

$current_user_id = current_user_id();
$pdo = db_connect();

$stmt = $pdo->prepare('SELECT COUNT(*) FROM breakdown_report_pokes WHERE recipient_id = ? AND (is_read IS NULL OR is_read = 0)');
$stmt->execute([$current_user_id]);
$count = $stmt->fetchColumn();

echo json_encode([
    'success' => true,
    'count' => (int)$count
], JSON_UNESCAPED_UNICODE);
