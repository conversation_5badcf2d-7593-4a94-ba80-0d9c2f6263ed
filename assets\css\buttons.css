/* استایل دکمه‌های استاندارد پروژه */
:root {
    --primary-color: #2563eb;
    --primary-dark: #1d4ed8;
    --primary-light: #3b82f6;
    --success-color: #10b981;
    --danger-color: #ef4444;
    --warning-color: #f59e0b;
    --info-color: #06b6d4;
    --gray-50: #f9fafb;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-400: #9ca3af;
    --gray-500: #6b7280;
    --gray-600: #4b5563;
    --gray-700: #374151;
    --gray-800: #1f2937;
    --gray-900: #111827;
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
    --border-radius: 0.5rem;
    --border-radius-lg: 0.75rem;
    --border-radius-xl: 1rem;
}

/* استایل پایه دکمه‌ها */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    border: none;
    border-radius: var(--border-radius);
    font-size: 0.9rem;
    font-weight: 600;
    cursor: pointer;
    text-decoration: none;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    font-family: inherit;
    line-height: 1.5;
    text-align: center;
    vertical-align: middle;
    user-select: none;
}

/* انیمیشن درخشش دکمه‌ها */
.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

/* انواع دکمه‌ها بر اساس رنگ */

/* دکمه اصلی */
.btn-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    color: white;
    box-shadow: var(--shadow);
}

.btn-primary:hover {
    
    box-shadow: var(--shadow-md);
}

.btn-primary:focus {
    outline: none;
    box-shadow: var(--shadow-md), 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.btn-primary:active {
    transform: translateY(0);
    box-shadow: var(--shadow);
}

/* دکمه موفقیت */
.btn-success {
    background: linear-gradient(135deg, var(--success-color), #34d399);
    color: white;
    box-shadow: var(--shadow);
}

.btn-success:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-success:focus {
    outline: none;
    box-shadow: var(--shadow-md), 0 0 0 3px rgba(16, 185, 129, 0.1);
}

.btn-success:active {
    transform: translateY(0);
    box-shadow: var(--shadow);
}

/* دکمه خطر */
.btn-danger {
    background: linear-gradient(135deg, var(--danger-color), #f87171);
    color: white;
    box-shadow: var(--shadow);
}

.btn-danger:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-danger:focus {
    outline: none;
    box-shadow: var(--shadow-md), 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.btn-danger:active {
    transform: translateY(0);
    box-shadow: var(--shadow);
}

/* دکمه هشدار */
.btn-warning {
    background: linear-gradient(135deg, var(--warning-color), #fbbf24);
    color: white;
    box-shadow: var(--shadow);
}

.btn-warning:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-warning:focus {
    outline: none;
    box-shadow: var(--shadow-md), 0 0 0 3px rgba(245, 158, 11, 0.1);
}

.btn-warning:active {
    transform: translateY(0);
    box-shadow: var(--shadow);
}

/* دکمه اطلاعات */
.btn-info {
    background: linear-gradient(135deg, var(--info-color), #22d3ee);
    color: white;
    box-shadow: var(--shadow);
}

.btn-info:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-info:focus {
    outline: none;
    box-shadow: var(--shadow-md), 0 0 0 3px rgba(6, 182, 212, 0.1);
}

.btn-info:active {
    transform: translateY(0);
    box-shadow: var(--shadow);
}

/* دکمه ثانویه */
.btn-secondary {
    background: var(--gray-100);
    color: var(--gray-700);
    border: 1px solid var(--gray-300);
    box-shadow: var(--shadow-sm);
}

.btn-secondary:hover {
    box-shadow: var(--shadow);
}

.btn-secondary:focus {
    outline: none;
    box-shadow: var(--shadow), 0 0 0 3px rgba(107, 114, 128, 0.1);
}

.btn-secondary:active {
    transform: translateY(0);
    background: var(--gray-300);
}

/* دکمه تیره */
.btn-dark {
    background: linear-gradient(135deg, var(--gray-800), var(--gray-700));
    color: white;
    box-shadow: var(--shadow);
}

.btn-dark:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-dark:focus {
    outline: none;
    box-shadow: var(--shadow-md), 0 0 0 3px rgba(31, 41, 55, 0.1);
}

.btn-dark:active {
    transform: translateY(0);
    box-shadow: var(--shadow);
}

/* دکمه روشن */
.btn-light {
    background: white;
    color: var(--gray-700);
    border: 1px solid var(--gray-300);
    box-shadow: var(--shadow-sm);
}

.btn-light:hover {
    background: var(--gray-50);
    transform: translateY(-1px);
    box-shadow: var(--shadow);
}

.btn-light:focus {
    outline: none;
    box-shadow: var(--shadow), 0 0 0 3px rgba(0, 0, 0, 0.05);
}

.btn-light:active {
    transform: translateY(0);
    background: var(--gray-100);
}

/* اندازه‌های مختلف دکمه‌ها */

/* دکمه کوچک */
.btn-sm {
    padding: 0.5rem 0.75rem;
    font-size: 0.8rem;
}

/* دکمه معمولی (پیش‌فرض) */
.btn {
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
}

/* دکمه بزرگ */
.btn-lg {
    padding: 1rem 2rem;
    font-size: 1rem;
}

/* دکمه خیلی بزرگ */
.btn-xl {
    padding: 1.25rem 2.5rem;
    font-size: 1.1rem;
}

/* دکمه تمام عرض */
.btn-block {
    width: 100%;
    justify-content: center;
}

/* وضعیت غیرفعال */
.btn:disabled,
.btn.disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
    box-shadow: none !important;
}

.btn:disabled::before,
.btn.disabled::before {
    display: none;
}

/* گروه دکمه‌ها */
.btn-group {
    display: inline-flex;
    vertical-align: middle;
}

.btn-group .btn {
    position: relative;
    flex: 1 1 auto;
}

.btn-group .btn:not(:first-child) {
    margin-right: -1px;
}

.btn-group .btn:not(:last-child) {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
}

.btn-group .btn:not(:first-child) {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
}

.btn-group .btn:hover {
    z-index: 1;
}

.btn-group .btn:focus {
    z-index: 2;
}

/* دکمه‌های آیکون */
.btn-icon {
    padding: 0.75rem;
    width: 2.75rem;
    height: 2.75rem;
    justify-content: center;
}

.btn-icon.btn-sm {
    padding: 0.5rem;
    width: 2.25rem;
    height: 2.25rem;
}

.btn-icon.btn-lg {
    padding: 1rem;
    width: 3.5rem;
    height: 3.5rem;
}

/* دکمه‌های شفاف */
.btn-outline-primary {
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
    background: transparent;
}

.btn-outline-primary:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-outline-success {
    color: var(--success-color);
    border: 2px solid var(--success-color);
    background: transparent;
}

.btn-outline-success:hover {
    background: var(--success-color);
    color: white;
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-outline-danger {
    color: var(--danger-color);
    border: 2px solid var(--danger-color);
    background: transparent;
}

.btn-outline-danger:hover {
    background: var(--danger-color);
    color: white;
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-outline-warning {
    color: var(--warning-color);
    border: 2px solid var(--warning-color);
    background: transparent;
}

.btn-outline-warning:hover {
    background: var(--warning-color);
    color: white;
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

/* دکمه‌های لینک */
.btn-link {
    color: var(--primary-color);
    background: transparent;
    border: none;
    text-decoration: underline;
    box-shadow: none;
}

.btn-link:hover {
    color: var(--primary-dark);
    text-decoration: none;
    transform: none;
    box-shadow: none;
}

.btn-link::before {
    display: none;
}

/* واکنش‌گرا */
@media (max-width: 768px) {
    .btn {
        padding: 0.625rem 1.25rem;
        font-size: 0.85rem;
    }
    
    .btn-sm {
        padding: 0.45rem 0.65rem;
        font-size: 0.75rem;
    }
    
    .btn-lg {
        padding: 0.875rem 1.75rem;
        font-size: 0.95rem;
    }
    
    .btn-xl {
        padding: 1.125rem 2.25rem;
        font-size: 1.05rem;
    }
}

@media (max-width: 480px) {
    .btn {
        padding: 0.5rem 1rem;
        font-size: 0.8rem;
    }
    
    .btn-sm {
        padding: 0.375rem 0.5rem;
        font-size: 0.7rem;
    }
}

/* تنظیمات چاپ */
@media print {
    .btn {
        background: transparent !important;
        color: black !important;
        border: 1px solid black !important;
        box-shadow: none !important;
        transform: none !important;
    }
    
    .btn::before {
        display: none !important;
    }
}
