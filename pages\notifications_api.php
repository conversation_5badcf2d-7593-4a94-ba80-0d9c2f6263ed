<?php
require_once '../includes/auth.php';
require_once '../db_connection.php';
require_once '../includes/date_helper.php';

header('Content-Type: application/json');

$pdo = db_connect();
$user_id = current_user_id();

if (!$user_id) {
    echo json_encode(['success' => false, 'message' => 'Authentication required.']);
    exit;
}

$action = $_POST['action'] ?? $_GET['action'] ?? '';

try {
    switch ($action) {
        case 'fetch':
            $stmt = $pdo->prepare("
                SELECT p.id, p.message, p.created_at, p.is_read, u.name as sender_name, br.id as report_id
                FROM breakdown_report_pokes p
                JOIN users u ON p.sender_id = u.id
                JOIN breakdown_reports br ON p.report_id = br.id
                WHERE p.recipient_id = ?
                ORDER BY p.created_at DESC
            ");
            $stmt->execute([$user_id]);
            $pokes = $stmt->fetchAll(PDO::FETCH_ASSOC);

            $unread_count = 0;
            $notifications = [];
            foreach ($pokes as $poke) {
                if (!$poke['is_read']) {
                    $unread_count++;
                }
                $notifications[] = [
                    'id' => $poke['id'],
                    'message' => htmlspecialchars($poke['message']),
                    'sender_name' => htmlspecialchars($poke['sender_name']),
                    'time_ago' => time_ago($poke['created_at']),
                    'is_read' => $poke['is_read'],
                    'link' => 'reports_list.php?highlight=' . $poke['report_id']
                ];
            }

            echo json_encode(['success' => true, 'notifications' => $notifications, 'unread_count' => $unread_count]);
            break;

        case 'mark_read':
            $id = $_POST['id'] ?? null;
            if ($id) {
                $stmt = $pdo->prepare("UPDATE breakdown_report_pokes SET is_read = 1 WHERE id = ? AND recipient_id = ?");
                $stmt->execute([$id, $user_id]);
                echo json_encode(['success' => $stmt->rowCount() > 0]);
            } else {
                throw new Exception('Notification ID is required.');
            }
            break;

        case 'mark_all_read':
            $stmt = $pdo->prepare("UPDATE breakdown_report_pokes SET is_read = 1 WHERE recipient_id = ?");
            $stmt->execute([$user_id]);
            echo json_encode(['success' => true]);
            break;

        default:
            throw new Exception('Invalid action.');
    }
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => $e->getMessage()]);
}
?>