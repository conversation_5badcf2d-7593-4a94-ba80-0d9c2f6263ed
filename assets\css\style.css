:root {
    --main-color: #d94307; /* نارنجی */
    --main-color-light: #ffb366;
    --card-bg: #fff;
    --card-shadow: rgba(255, 102, 0, 0.2);
    --text-color: #333;
    --text-color-light: #777;
    --background-color: #f9f9f9;
    /* Typography scale for desktop (>=992px) */
    --fs-xxs: 0.625rem; /* 10px */
    --fs-xs: 0.75rem;   /* 12px */
    --fs-sm: 0.875rem;  /* 14px */
    --fs-md: 1rem;      /* 16px */
    --fs-lg: 1.125rem;  /* 18px */
    --fs-xl: 1.25rem;   /* 20px */
    --fs-2xl: 1.5rem;   /* 24px */
    --fs-3xl: 2rem;     /* 32px */
    --fs-4xl: 2.5rem;   /* 40px */
}
@font-face {
    font-family: 'Vazirmatn';
    src: url('../fonts/Vazirmatn-Regular.woff2') format('woff2'),
         url('../fonts/Vazirmatn-Regular.ttf') format('truetype');
    font-weight: normal;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Vazirmatn';
    src: url('../fonts/Vazirmatn-Bold.woff2') format('woff2'),
    url('../fonts/Vazirmatn-Bold.ttf') format('truetype');
    font-weight: bold;
    font-style: normal;
    font-display: swap;
}
/* فونت پیش‌فرض برای کل پروژه */
html, body {
    font-family: 'Vazirmatn', sans-serif !important;
    font-size: var(--fs-md);
}

/* اعمال فونت به همه عناصر */
*:not(i), *::before, *::after {
    font-family: inherit;
}

body {
    background-color: var(--background-color);
    
    color: var(--text-color);
    margin: 0;
    padding: 0;
    line-height: 1.6;
}

/* استایل منوی ناوبری هدر قدیمی - غیرفعال شده */
/* این استایل‌ها با هدر جدید تداخل داشتند */
/*
nav {
    background-color: var(--main-color);
    padding: 12px 20px;
    display: flex;
    justify-content: center;
    gap: 20px;
    border-radius: 0 0 15px 15px;
    box-shadow: 0 4px 12px var(--card-shadow);
}

nav a {
    color: white;
    text-decoration: none;
    font-size: 1.1rem;
    padding: 8px 12px;
    border-radius: 8px;
    transition: background-color 0.3s ease;
}

nav a:hover,
nav a:focus {
    background-color: var(--main-color-light);
    outline: none;
    color: #333;
}
*/

table {
  width: 100%;
  border-collapse: collapse;
  background: white;
  border-radius: 8px;
  
  box-shadow: 0 2px 10px rgb(0 0 0 / 0.1);
}

th, td {
  padding: 12px 15px;
  border-bottom: 1px solid #e0e0e0;
  text-align: center;
}

th {
  background-color: #f0f4f8;
  font-weight: 400;
  color: #333;
}

tr.service-alert {
  background-color: #ffe5e5;
}

img {
  max-width: 100px;
  max-height: 80px;
  border-radius: 6px;
  object-fit: cover;
}

h2 {
    color: var(--main-color);
    
    font-weight: 600;
    font-size: 24px;
    text-align: center;
}
input[readonly] {
    background: none;
    color: #7d7c7b;
}

footer {
    color: #000;
    text-align: center;
    padding: 15px 20px;
    font-size: 0.9rem;
    border-radius: 15px 15px 0 0;
    margin-top: 40px;
}

footer a {
    color: var(--main-color-light);
    text-decoration: underline;
}

footer a:hover,
footer a:focus {
    color: #fff;
    text-decoration: none;
    outline: none;
}

.dashboard-container {
    max-width: 1100px;
    margin: 30px auto;
    padding: 0 15px 40px;
}

.dashboard-cards {
    display: flex;
    gap: 25px;
    flex-wrap: wrap;
    justify-content: center;
    margin-bottom: 40px;
}

.card {
    background: var(--card-bg);
    border-radius: 15px;
    box-shadow: 0 6px 20px var(--card-shadow);
    padding: 25px 30px;
    flex: 1 1 250px;
    display: flex;
    align-items: center;
    cursor: default;
    transition: transform 0.2s ease;
}

.card:hover,
.card:focus {
    transform: translateY(-8px);
    box-shadow: 0 10px 30px var(--card-shadow);
    outline: none;
}

.card-icon {
    font-size: 3.5rem;
    margin-right: 20px;
    user-select: none;
}

.card-info h3 {
    margin: 0 0 8px;
    color: var(--main-color);
    font-weight: 500;
}

.card-info p {
    font-size: 2rem;
    color: var(--text-color);
    margin: 0;
	text-align: center;
}

.dashboard-lower-row {
    display: flex;
    gap: 25px;
    flex-wrap: nowrap;
    justify-content: space-between;
}

.dashboard-lower-row .box {
    background: var(--card-bg);
    padding: 25px 30px;
    border-radius: 15px;
    box-shadow: 0 8px 25px rgba(255, 102, 0, 0.15);
    flex: 1 1 48%;
    min-width: 320px;
    max-height: 400px;
    overflow-y: auto;
}

.dashboard-lower-row .alerts-box {
    text-align: right;
}

.dashboard-lower-row .box h3 {
    margin-bottom: 20px;
    padding-bottom: 8px;
    color: var(--main-color);
    font-weight: 500;
    font-size: 18px;
}

.dashboard-lower-row .box ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.dashboard-lower-row .box ul li {
    border-bottom: 1px solid #eee;
    padding: 12px 0;
    font-size: 1rem;
    color: var(--text-color);
}

.dashboard-lower-row .box ul li:last-child {
    border-bottom: none;
}

.dashboard-lower-row .box ul li strong {
    color: var(--main-color);
}

.dashboard-lower-row .box ul li small {
    display: block;
    margin-top: 4px;
    color: var(--text-color-light);
    font-size: 0.85rem;
}

/* ریسپانسیو موبایل */
@media screen and (max-width: 768px) {
    .dashboard-cards {
        flex-direction: column;
        align-items: center;
    }

    .dashboard-lower-row {
        flex-direction: column;
    }

    .dashboard-lower-row .box {
        max-height: none;
        flex-basis: 100%;
    }
}

.service-alert {
    background-color: #ffcccc;
}

#deviceDetailsModal button {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
}

/* Device Certificate (Shenasname) Modal Styles */
.device-certificate-modal .modal-body {
    background: #f9f9f9;
    border-radius: 15px;
    border: 1px solid #ddd;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    padding: 0;
    max-width: 450px; /* Adjust width for a card-like feel */
    font-family: 'Vazirmatn', sans-serif;
}

.certificate-header {
    background-color: var(--main-color);
    color: white;
    padding: 15px 20px;
    border-radius: 15px 15px 0 0;
    text-align: center;
    position: relative;
}

.certificate-header h4 {
    margin: 0;
    font-size: 1.5rem;
    font-weight: bold;
}

.certificate-header .device-id {
    font-size: 0.9rem;
    opacity: 0.8;
}

.certificate-body {
    padding: 25px;
    display: grid;
    grid-template-columns: 120px 1fr;
    gap: 20px;
    align-items: center;
}

.device-photo {
    width: 120px;
    height: 120px;
    border-radius: 10px;
    object-fit: cover;
    border: 3px solid #fff;
    box-shadow: 0 4px 10px rgba(0,0,0,0.15);
}

.device-main-info h5 {
    margin: 0 0 10px;
    font-size: 1.4rem;
    color: #333;
}

.device-main-info p {
    margin: 4px 0;
    font-size: 0.95rem;
    color: #555;
}

.certificate-details {
    padding: 0 25px 25px;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
    border-top: 1px dashed #ccc;
    margin: 0 25px;
    padding-top: 20px;
}

.detail-item {
    font-size: 0.9rem;
}

.detail-item strong {
    display: block;
    color: #777;
    margin-bottom: 3px;
}

.certificate-footer {
    text-align: center;
    padding: 15px;
    font-size: 0.8rem;
    color: #999;
    background-color: #f1f1f1;
    border-radius: 0 0 15px 15px;
}
/* Reset جزئی و فونت فارسی خوانا */
body {
    
    background: #f7f9fc;
    color: #222;
    line-height: 1.6;
   
}

/* فرم تعریف دستگاه */
form#addDeviceForm {
    background: #fff;
    padding: 25px 30px;
    border-radius: 12px;
    box-shadow: 0 6px 15px rgba(0,0,0,0.1);
    
    max-width: 800px;
    margin: auto;
}

form#addDeviceForm h3 {
    margin-bottom: 20px;
    font-weight: 700;
    color: #0057b7;
}

/* ورودی‌ها و textarea و select */
form#addDeviceForm input[type="text"],
form#addDeviceForm input[type="tel"],
form#addDeviceForm input[type="number"],
form#addDeviceForm input[type="date"],
form#addDeviceForm input[type="file"],
form#addDeviceForm select,
form#addDeviceForm textarea {
    width: 100%;
    padding: 12px 15px;
    margin-top: 6px;
    margin-bottom: 18px;
    border: 1.8px solid #ccc;
    border-radius: 10px;
    font-size: 13px;
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
    box-sizing: border-box;
    resize: vertical;
}


form#addDeviceForm textarea {
    min-height: 90px;
    font-size: 14px;
}

form#addDeviceForm input[type="text"]:focus,
form#addDeviceForm input[type="tel"]:focus,
form#addDeviceForm input[type="number"]:focus,
form#addDeviceForm input[type="date"]:focus,
form#addDeviceForm input[type="file"]:focus,
form#addDeviceForm select:focus,
form#addDeviceForm textarea:focus {
    border-color: #0057b7;
    box-shadow: 0 0 6px rgba(0,87,183,0.4);
    outline: none;
}

/* برچسب‌ها */
form#addDeviceForm label {
    color: #444;
    margin-bottom: 4px;
    font-size: 14px;
}

/* دکمه ارسال */
form#addDeviceForm button[type="submit"] {
    background: #0057b7;
    color: white;
    font-weight: 700;
    font-size: 17px;
    border: none;
    border-radius: 12px;
    padding: 14px 28px;
    cursor: pointer;
    transition: background-color 0.3s ease;
    box-shadow: 0 4px 8px rgba(0,87,183,0.3);
    display: block;
    margin-top: 10px;
    width: 100%;
}

form#addDeviceForm button[type="submit"]:hover {
    background: #003d8f;
}

/* جدول لیست دستگاه‌ها */
table {
    width: 100%;
    font-size: 14px;
    box-shadow: 0 3px 15px rgba(0,0,0,0.07);
    background: white;
    border-radius: 12px;
    
}

thead tr {
    background: #0057b7;
    color: white;
    text-align: center;
    font-weight: 600;
    font-size: 15px;
}

tbody tr {
    background: #fff;
    transition: background-color 0.25s ease;
    cursor: default;
}

tbody tr:hover {
    background: #f0f6ff;
}

tbody tr.service-alert {
    background: #ffe6e6 !important;
    color: #a70000 !important;
    font-weight: 600;
}

/* سلول‌ها */
td, th {
    padding: 14px 12px;
    text-align: center;
    vertical-align: middle;
   border: 1px solid #dee1e3;
   
}
@media (max-width: 768px) {
/* سلول‌ها */
td, th {
   border: none;
   border-bottom: 1px dotted #c7c6c6;
}}

/* تصاویر در جدول */
td img {
    max-width: 90px;
    max-height: 70px;
    border-radius: 10px;
    object-fit: cover;
    box-shadow: 0 2px 7px rgba(0,0,0,0.1);
}

/* دکمه‌ها در جدول */
td button {
    background: #007bff;
    border: none;
    color: white;
    padding: 8px 12px;
    margin: 2px 3px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 13px;
    transition: background-color 0.3s ease;
}


td a button {
    background: #28a745;
}

td a button:hover {
    background: #1e7e34;
}

td button:last-child {
    background: #dc3545;
}

td button:last-child:hover {
    background: #a71d2a;
}

/* مودال نمایش جزئیات دستگاه */



/* دکمه بستن */
#deviceDetailsModal button.close-btn {
    
    cursor: pointer;
    color: #999999;
    transition: color 0.25s ease;
    
}

#deviceDetailsModal button.close-btn:hover {
    color: #e63946;
}

/* تیتر مودال */
#deviceDetailsModal h4 {
    margin-top: 0;
    margin-bottom: 20px;
    font-weight: 600;
    font-size: 20px;
    color: #1d3557;
    border-bottom: 2px solid #a8dadc;
    padding-bottom: 12px;
	padding-top: 40px;
}

/* پاراگراف‌ها */
#deviceDetailsModal p {
    margin: 10px 0;
    line-height: 1.65;
    font-size: 16px;
    color: #444444;
}

/* لیست قطعات مصرفی */
#deviceDetailsModal h5 {
    margin-top: 30px;
    font-weight: 600;
    font-size: 16px;
    color: #457b9d;
    border-bottom: 1.5px solid #cce3e9;
    padding-bottom: 8px;
}

#deviceDetailsModal ul {
    list-style: inside disc;
    margin: 15px 0 0 0;
    padding-left: 0;
    color: #555555;
}

#deviceDetailsModal ul li {
    margin-bottom: 10px;
    font-size: 15px;
    font-weight: 500;
}

/* تصویر دستگاه */
#deviceDetailsModal img {
    display: block;
    max-width: 100%;
    margin-top: 20px;
    border-radius: 14px;
    box-shadow: 0 6px 20px rgba(0,0,0,0.12);
    object-fit: contain;
}

/* اسکرول بار زیبا */
#deviceDetailsModal::-webkit-scrollbar {
    width: 8px;
}

/* Removed stray incomplete selector that caused CSS parsing errors */

/* ریسپانسیو */
@media (max-width: 720px) {
    form#addDeviceForm {
        padding: 20px;
        margin-bottom: 30px;
    }

    table {
        font-size: 12px;
    }

    td img {
        max-width: 60px;
        max-height: 50px;
    }

    td button {
        padding: 6px 8px;
        font-size: 11px;
    }
}
.device-container {
  display: flex;
  gap: 20px;
  align-items: flex-start;
  margin-top: 20px;
  direction: rtl; /* چون صفحه فارسی هست */
  padding: 10px 50px;
}

.add-device {
  flex: 0 0 25%;
  background: #f3f3f3;
  padding: 20px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgb(0 0 0 / 13%);
  box-sizing: border-box;
}

.device-list {
  flex: 1 ;
  overflow-x: auto;
  padding: inherit;
}

/* دکمه‌ها */
button {
  background: #bf3a04;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 8px 16px;
  cursor: pointer;
  font-weight: 600;
  transition: background-color 0.3s ease;
}

button:hover {
  filter: brightness(90%);
}

/* فرم inputs */
input[type=text],
input[type=tel],
input[type=number],
input[type=date],
select,
textarea,
input[type=file],input#stop-time[type=time] {
    width: 100%;
    padding: 12px 15px;
    margin-top: 6px;
    margin-bottom: 18px;
    border: 1.8px solid #ccc;
    border-radius: 10px;
    font-size: 13px!important;
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
    box-sizing: border-box;
    resize: vertical;
  
}

input[type=text]:focus,
input[type=tel]:focus,
input[type=number]:focus,
input[type=date]:focus,
select:focus,
textarea:focus {
  border-color: #007bff;
  outline: none;
  box-shadow: 0 0 8px rgba(0, 123, 255, 0.3);
}

textarea {
  resize: vertical;
  min-height: 80px;
}

.login-page * {
  box-sizing: border-box;
}

.login-page {
  
  background: linear-gradient(135deg, #e26d4a, #bd9b3563);
  direction: rtl;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  margin: 0;
  padding: 0;
  overflow: hidden;
}

.login-page .background-overlay {
  position: fixed;
  top: 0; left: 0;
  width: 100%; height: 100%;
  background: url('https://images.unsplash.com/photo-1506744038136-46273834b3fb?auto=format&fit=crop&w=1470&q=80') center/cover no-repeat;
  filter: brightness(0.45) blur(7px);
  z-index: 0;
}

.login-page .login-wrapper {
  position: relative;
  z-index: 1;
  width: 380px;
  max-width: 90vw;
}

.login-page .login-box {
  background: rgba(255, 255, 255, 0.95);
  padding: 40px 36px;
  border-radius: 20px;
  box-shadow: 0 20px 50px rgba(21, 54, 101, 0.3);
  text-align: center;
  transition: transform 0.3s ease;
}

.login-page .login-box:hover {
  transform: translateY(-5px);
}

.login-page .logo {
  margin-bottom: 20px;
}

.login-page .login-box h2 {
  font-weight: 900;
  color: #d94307;
  margin-bottom: 28px;
  font-size: 24px;
 
}

.login-page .input-group {
  position: relative;
  margin-bottom: 25px;
}

.login-page .login-form input[type="text"],
.login-page .login-form input[type="password"] {
  width: 100%;
  padding: 14px 48px 14px 14px;
  font-size: 17px;
  border-radius: 14px;
  border: 2px solid #cbd5e1;
  transition: border-color 0.3s ease, box-shadow 0.3s ease;
  outline: none;
  color: #222;
  background: #f9fbff;
  box-shadow: inset 2px 2px 5px #e3e7f1, inset -2px -2px 5px #ffffff;
  
}

.login-page .input-icon {
  position: absolute;
  top: 50%;
  right: 16px;
  transform: translateY(-50%);
  font-size: 20px;
  color: #7a8db9;
  pointer-events: none;
  user-select: none;
}

.login-page .login-form input[type="text"]:focus,
.login-page .login-form input[type="password"]:focus {
  border-color: #4a90e2;
  box-shadow: 0 0 10px #4a90e2;
  background: #ffffff;
}

.login-page .login-form button {
  width: 100%;
  padding: 16px 0;
  background: #d55741;
  border: none;
  border-radius: 16px;
  color: #fff;
  font-weight: 800;
  font-size: 18px;
  cursor: pointer;
  letter-spacing: 1.2px;
  box-shadow: 0 8px 25px rgb(210 148 145);
  transition: background-color 0.3s ease, box-shadow 0.3s ease;
 
}

.login-page .login-form button:hover {
  background: #d34c34;
  box-shadow: 0 12px 30px #d68670;
}

.login-page .error-msg {
  margin-top: 18px;
  color: #d32f2f;
  font-weight: 700;
  font-size: 15px;
  letter-spacing: 0.7px;
  text-align: center;
}

@media (max-width: 400px) {
  .login-page .login-wrapper {
    width: 95vw;
  }
  .login-page .login-box {
    padding: 30px 20px;
    border-radius: 16px;
  }
  .login-page .login-box h2 {
    font-size: 22px;
  }
  .login-page .login-form input[type="text"],
  .login-page .login-form input[type="password"] {
    font-size: 16px;
    padding: 12px 44px 12px 12px;
  }
  .login-page .login-form button {
    font-size: 16px;
    padding: 14px 0;
  }
}
#consumableFieldset {
    border: 1px solid #ccc;
    padding: 10px;
	border-radius: inherit;
	margin-top: 10px;

}

#consumableFieldset #consumablesContainer .consumable-row {
    display: flex;
    gap: 10px;
    margin-bottom: 10px;
    align-items: center;
}

#consumableFieldset input[type="text"],
#consumableFieldset input[type="number"] {
    padding: 5px 8px;
    font-size: 14px;
}

#consumableFieldset input[type="number"] {
    width: 60px;
}

#consumableFieldset button {
    background-color: #e74c3c;
    border: none;
    color: white;
    font-size: 14px;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.2s ease;
	width: auto;
    padding: 5px 15px;
}

#consumableFieldset button:hover {
    background-color: #c0392b;
}

#consumableFieldset > button {
    width: auto;
    padding: 5px 15px;
    font-size: 14px;
    border-radius: 6px;
    background-color: #3498db;
    margin-top: 5px;
	

}

#consumableFieldset > button:hover {
    background-color: #2980b9;
}

.device_edit_div{
	padding: 10px 20px;
	display: grid;
    justify-items: center;
}
/* == فرم ویرایش دستگاه == */

body, input, select, textarea, button {
  font-family: 'Vazirmatn', sans-serif;
  font-size: 14px;
  direction: rtl;
  box-sizing: border-box;
}
/* فرم دو ستونی */
#editDeviceForm .form-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

#editDeviceForm .form-grid .full-width {
  grid-column: 1 / -1;
}

h3 {
  font-size: 20px;
  color: #333;
  margin-bottom: 24px;
}

/* فرم اصلی */
form#editDeviceForm {
  max-width: 800px;
  background-color: #fdfdfd;
  padding: 24px;
  border: 1px solid #ddd;
  border-radius: 8px;
  margin: auto;
}

/* فیلدهای متنی، عددی، تاریخ، انتخاب‌گر، فایل، textarea */
form#editDeviceForm input[type="text"],
form#editDeviceForm input[type="tel"],
form#editDeviceForm input[type="number"],
form#editDeviceForm input[type="date"],
form#editDeviceForm input[type="file"],
form#editDeviceForm select,
form#editDeviceForm textarea {
  width: 100%;
  padding: 10px 14px;
  border: 1px solid #ccc;
  border-radius: 6px;
  background-color: #fafafa;
  transition: border 0.3s ease;
}

form#editDeviceForm input:focus,
form#editDeviceForm textarea:focus,
form#editDeviceForm select:focus {
  border-color: #2196f3;
  background-color: #fff;
  outline: none;
}

/* تصویر دستگاه */
form#editDeviceForm img {
  max-width: 150px;
  border-radius: 6px;
  border: 1px solid #ccc;
  margin: 12px 0;
}

/* لیبل‌ها */
form#editDeviceForm label {
  display: block;
  font-weight: 500;
  margin-bottom: 6px;
  color: #444;
}


/* ردیف‌های قطعه */
#partsContainer .part-row {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-bottom: 12px;
}

#partsContainer .part-row input[type="text"],
#partsContainer .part-row input[type="number"] {
  padding: 8px 10px;
  border-radius: 5px;
  border: 1px solid #ccc;
  background-color: #f7f7f7;
  min-width: 120px;
}


/* ساختار ردیفی برای قطعات مصرفی */
#partsContainer .part-row {
  display: flex;
  gap: 10px;
  margin-bottom: 8px;
  align-items: center;
}

#partsContainer .part-row input[type="text"]:nth-of-type(1) {
  flex: 2; /* نام قطعه - عرض بیشتر */
}

#partsContainer .part-row input[type="number"] {
  width: 70px; /* فقط به اندازه‌ی عدد مثل "100" */
}

#partsContainer .part-row input[type="text"]:nth-of-type(2) {
  width: 80px; /* واحد شمارش - عرض کم */
}

#partsContainer .part-row input[type="text"]:nth-of-type(3) {
  flex: 1; /* توضیحات - عرض متوسط */
}

#partsContainer .part-row button {
  white-space: nowrap;
}
.device-search-container {
  margin-bottom: 15px;
  text-align: right;
}

.device-search-input {
  padding: 8px 14px;
  border: 1px solid #ccc;
  border-radius: 8px;
  width: 100%;
  max-width: 350px;
  font-size: 15px;
  direction: rtl;
}

/*صفحه ی فعالیت های پیشگیرانه*/
.activities-page .activities-layout {
    display: flex;
    gap: 20px;
    align-items: flex-start;
    margin-top: 20px;
    direction: rtl;
    padding: 10px 50px;
}

.activities-page .add-activity-column {
	flex: 0 0 25%;
     background: #f3f3f3;
  padding: 20px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgb(0 0 0 / 13%);
  box-sizing: border-box;
}

.activities-page .list-activities-column {
  flex: 1; 
   padding: inherit;
}
.activities-page form#addActivityForm {
    background: #fff;
    padding: 25px 30px;
    border-radius: 12px;
    box-shadow: 0 6px 15px rgba(0,0,0,0.1);
    max-width: 600px;
    margin-bottom: 40px;
}
.activities-page button#clearFilters {
    background: var(--text-color-light);
    margin-top: 6px;
    margin-bottom: 14px;
}
input#realTimeSearch {
    max-width: 350px;
}

.removeActivityBtn {
      background: #ff5252;
      color: white;
      border: none;
      border-radius: 4px;
      padding: 5px 10px;
      cursor: pointer;
      margin-top: 10px;
    }
    .activity-block {
      border: 1px solid #ddd;
      padding: 15px;
      margin-bottom: 15px;
      border-radius: 5px;
      position: relative;
    }
    
    .toolbar {
      margin-bottom: 15px;
    }
    .search-container {
      display: flex;
      gap: 10px;
      margin-bottom: 15px;
    }

    .documents-popup {
      display: none;
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0,0,0,0.7);
      z-index: 1000;
      justify-content: center;
      align-items: center;
    }
    
    .documents-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
      gap: 10px;
      margin-top: 15px;
    }
    .document-item {
      text-align: center;
    }
    .document-item img {
      max-width: 100%;
      height: 100px;
      object-fit: cover;
      border: 1px solid #ddd;
      border-radius: 4px;
    }
    .close-popup {
      float: right;
      font-size: 24px;
      cursor: pointer;
    }
    /* استایل‌های جدید برای مدیریت فایل‌ها */
    .file-manager {
      margin-top: 10px;
    }
    .file-input-wrapper {
      position: relative;
      overflow: hidden;
      display: inline-block;
      margin-bottom: 10px;
    }
    .file-input-wrapper input[type=file] {
      position: absolute;
      left: 0;
      top: 0;
      opacity: 0;
      width: 100%;
      height: 100%;
      cursor: pointer;
    }
    .btn-add-file {
      background: #4CAF50;
      color: white;
      border: none;
      padding: 8px 15px;
      border-radius: 4px;
      cursor: pointer;
    }
    .documents-list {
      list-style: none;
      padding: 0;
      margin-top: 10px;
    }
    .document-item-row {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 8px;
      background: #f9f9f9;
      border-radius: 4px;
      margin-bottom: 5px;
      border: 1px solid #eee;
    }
    .document-item-name {
      flex: 1;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .remove-document-btn {
      background: #ff5252;
      color: white;
      border: none;
	width: 24px;
      height: 24px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      margin-left: 10px;
    }
    .file-size {
      font-size: 12px;
      color: #777;
      margin-left: 5px;
    }
	    /* استایل‌های جدید برای کنترل‌های فعالیت */
    .activity-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        background: #f0f0f0;
        padding: 8px 12px;
        border-radius: 4px;
        margin-bottom: 10px;
        border: 1px solid #ddd;
    }
    
    .activity-controls {
        display: flex;
        gap: 5px;
    }
    
    .minimize-btn, .remove-btn {
        width: 24px;
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #e0e0e0;
        border: none;
        border-radius: 3px;
        cursor: pointer;
        font-size: 14px;
    }
    
    .minimize-btn:hover, .remove-btn:hover {
        background: #d0d0d0;
    }
    
    .activity-block.collapsed .activity-body {
        display: none;
    }
    
    .activity-title {
        font-weight: bold;
    }
    
    /* استایل‌های جدید برای حالت ویرایش */
    .edit-form-row {
        background-color: #fffde7;
    }
    
    .edit-form-row td {
        padding: 8px;
    }
    
    .edit-controls {
        display: flex;
        gap: 5px;
		justify-content: center;
    }
    
    .edit-controls button {
        padding: 6px 12px;
        border: none;
        border-radius: 4px;
        cursor: pointer;
    }
    
    .save-btn {
        background-color: #4CAF50;
        color: white;
    }
    
    .cancel-btn {
        background-color: #cc1818 !important;
        color: white;
    }


    
    .popup-content {
        background: white;
        padding: 20px;
        border-radius: 8px;
        width: 80%;
        max-width: 800px;
        max-height: 90vh;
        overflow-y: auto;
    }
    
    .document-item {
        border: 1px solid #ddd;
        border-radius: 4px;
        overflow: hidden;
        position: relative;
    }
    
        
    .document-item .document-info {
        padding: 10px;
        background: #f9f9f9;
    }
    
    .document-item .document-actions {
        display: flex;
        justify-content: space-between;
        padding: 10px;
        background: #f1f1f1;
    }
    
    .upload-section {
        margin-top: 20px;
        padding: 15px;
        background: #f9f9f9;
        border-radius: 4px;
    }
    
    .upload-controls {
        display: flex;
        gap: 10px;
        margin-top: 10px;
    }
    
    .upload-controls input[type="file"] {
        flex-grow: 1;
    }
    


.activities-page h1 {
  text-align: center;
  margin: 25px 0 15px;
  font-weight: 700;
  color: #0d47a1;
}

.activities-page .filters {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgb(0 0 0 / 0.1);
  padding: 15px 20px;
  margin-bottom: 25px;
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
  justify-content: center;
}

/* و بقیه استایل‌ها همه با .activities-page شروع میشن */

.activities-page .filters label {
  font-weight: 600;
  margin-bottom: 5px;
  display: block;
  color: #444;
}

.activities-page .filters select,
.activities-page .filters input[type="text"] {
  border: 1.5px solid #ccc;
  border-radius: 5px;
  padding: 6px 10px;
  font-size: 15px;
  min-width: 150px;
  transition: border-color 0.3s ease;
}

.activities-page .filters select:focus,
.activities-page .filters input[type="text"]:focus {
  outline: none;
  border-color: #0d47a1;
  box-shadow: 0 0 6px #0d47a1aa;
}

.activities-page .filters button {
  background-color: #0d47a1;
  color: white;
  border: none;
  border-radius: 5px;
  padding: 7px 18px;
  font-size: 16px;
  cursor: pointer;
  transition: background-color 0.25s ease;
  height: 38px;
  align-self: flex-end;
}

.activities-page .filters button:hover {
  background-color: #083770;
}



.activities-page .activity-block {
  background-color: #fff;
  padding: 15px 20px;
  border-radius: 8px;
  box-shadow: 0 1px 6px rgb(0 0 0 / 0.1);
  margin-bottom: 20px;
  position: relative;
}

.activities-page .activity-block .form-group {
  margin-bottom: 15px;
}

.activities-page .activity-block label {
  color: #444;
    margin-bottom: 4px;
    font-size: 14px;
}

.activities-page .activity-block input[type="text"]:focus,
.activities-page .activity-block input[type="number"]:focus,
.activities-page .activity-block select:focus {
  border-color: #0d47a1;
  outline: none;
  box-shadow: 0 0 7px #0d47a1aa;
}

.activities-page button#addMoreActivitiesBtn {
  background-color: #1976d2;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 15px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.activities-page button#addMoreActivitiesBtn:hover {
  background-color: #115293;
}

.activities-page .removeActivityBtn {
  top: 15px;
  left: 15px;
  background-color: #e53935;
  color: white;
  border: none;
  border-radius: 5px;
  padding: 5px 10px;
  font-size: 13px;
  cursor: pointer;
  transition: background-color 0.25s ease;
}

.activities-page .removeActivityBtn:hover {
  background-color: #ab000d;
}

.activities-page button[type="submit"] {
 color: white;
    font-weight: 700;
    font-size: 17px;
    cursor: pointer;
    box-shadow: rgba(0, 87, 183, 0.3) 0px 4px 8px;
    display: block;
    margin-top: 10px;
    width: 100%;
    background: rgb(0, 87, 183);
    border-width: initial;
    border-style: none;
    border-color: initial;
    border-image: initial;
    border-radius: 12px;
    padding: 14px 28px;
    transition: background-color 0.3s;
}

.activities-page button[type="submit"]:hover {
  background-color: #083770;
}

.activities-page .action-btn {
  background-color: transparent;
  border: none;
  cursor: pointer;
  font-size: 18px;
  margin: 0 5px;
  transition: color 0.3s ease;
}

.activities-page .action-btn:hover {
  color: #0d47a1;
}

.activities-page .action-btn.delete {
  color: #e53935;
}

.activities-page .action-btn.upload {
  color: #43a047;
}

.activities-page .attachment-icon {
  font-size: 18px;
  vertical-align: middle;
  margin-left: 4px;
  color: #0d47a1;
}

.activities-page .modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0,0,0,0.6);
  display: none;
  justify-content: center;
  align-items: center;
  z-index: 2000;
}

.activities-page .modal-content {
  background-color: white;
  padding: 25px 30px;
  border-radius: 10px;
  width: 320px;
  box-shadow: 0 2px 15px rgb(0 0 0 / 0.2);
  position: relative;
}

.activities-page .modal-content h2 {
  margin-top: 0;
  font-weight: 700;
  color: #0d47a1;
  text-align: center;
  margin-bottom: 20px;
}

.activities-page .modal-content label {
  font-weight: 600;
  margin-bottom: 7px;
  display: block;
}

.activities-page .modal-content input[type="file"] {
  width: 100%;
  padding: 7px 10px;
  border: 1.5px solid #ccc;
  border-radius: 6px;
  font-size: 14px;
  box-sizing: border-box;
  transition: border-color 0.3s ease;
}

.activities-page .modal-content input[type="file"]:focus {
  border-color: #0d47a1;
  outline: none;
  box-shadow: 0 0 7px #0d47a1aa;
}

.activities-page .modal-content button {
  background-color: #0d47a1;
  color: white;
  border: none;
  border-radius: 7px;
  padding: 10px 18px;
  font-size: 16px;
  cursor: pointer;
  transition: background-color 0.3s ease;
  margin-top: 15px;
  width: 100%;
}

.activities-page .modal-content button:hover {
  background-color: #083770;
}

.activities-page #closeUploadModal {
  position: absolute;
  top: 12px;
  left: 12px;
  background: none;
  border: none;
  font-size: 22px;
  cursor: pointer;
  color: #555;
  font-weight: 700;
  transition: color 0.3s ease;
}

.activities-page #closeUploadModal:hover {
  color: #0d47a1;
}

/* Scrollbar برای جداول و فرم‌ها */
.activities-page table,
.activities-page .filters,
.activities-page .activity-block {
  scrollbar-width: thin;
  scrollbar-color: #0d47a1 #e0e0e0;
}


@media (max-width: 850px) {
  .activities-page .filters {
    flex-direction: column;
    gap: 10px;
  }
  .activities-page table,
  .activities-page .filters,
  .activities-page .activity-block {
    font-size: 13px;
  }
}

@media (max-width: 480px) {
  .activities-page button#addMoreActivitiesBtn,
  .activities-page button[type="submit"],
  .activities-page .filters button {
    width: 100%;
    box-sizing: border-box;
  }
}
     
    /* استایل‌های جدید برای نمایش پیام عدم وجود نتیجه */
    .no-results-row td {
        text-align: center;
        color: #999;
        font-style: italic;
    }
    
    /* استایل‌های وضعیت سرویس */
    tr.overdue {
        background-color: rgba(255, 0, 0, 0.1) !important;
    }
    
    tr.warning {
        background-color: rgba(255, 255, 0, 0.2) !important;
    }
    
    /* استایل‌های جدید برای تاریخ‌ها */
    .date-cell {
        white-space: nowrap;
    }
.device-option {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    font-size: 14px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    display: block;
}

.select2-container--default .select2-results__option {
    padding: 8px 12px;
}


      /* **جدید**: استایل برای ساختار دو ستونی */
      .wo-container {
       display: flex;
    gap: 20px;
    align-items: flex-start;
    margin-top: 20px;
    direction: rtl;
    padding: 10px 50px;
      }
      .wo-list {
        flex: 1;
        min-width: 400px; /* حداقل عرض برای ستون لیست */
		padding: inherit;
      }
      .wo-add {
       flex: 0 0 30%;
     background: #f3f3f3;
  padding: 20px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgb(0 0 0 / 13%);
  box-sizing: border-box;
      }
form#add-wo-form {
    background: #fff;
    padding: 25px 30px;
    border-radius: 12px;
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.1);
    max-width: 600px;
    margin-bottom: 40px;
}
button#save-wo-btn{
	color: white;
    font-weight: 700;
    font-size: 17px;
    cursor: pointer;
    box-shadow: rgba(0, 87, 183, 0.3) 0px 4px 8px;
    display: block;
    margin-top: 10px;
    width: 100%;
    background: rgb(0, 87, 183);
    border-radius: 12px;
    padding: 14px 28px;
    transition: background-color 0.3s;
}
button#pish-wo-btn{
	color: #636363;
    font-weight: 400;
    font-size: 14px;
    cursor: pointer;
    display: block;
    margin-top: 10px;
    width: 100%;
    padding: 14px 28px;
	background:none;
	text-decoration-line: underline;
}
label.form-label {
	 color: #444;
    margin-bottom: 4px;
    font-size: 14px;
}
span.select2-selection.select2-selection--single ,  .select2-container--default .select2-selection--multiple {
    width: 100%!important;
    padding: 12px 15px!important;
    margin-top: 6px!important;
    margin-bottom: 18px!important;
    border: 1.8px solid #ccc!important;
    border-radius: 10px!important;
    font-size: 13px !important;
    transition: border-color 0.3s ease, box-shadow 0.3s ease!important;
    box-sizing: border-box!important;
    resize: vertical!important;
	height: auto!important;
}
 
 .select2-container--default .select2-selection--multiple .select2-selection__choice{
 display:grid!important;
 }
.select2-container--default .select2-selection--single .select2-selection__arrow {
top: 13px!important;
}
.select2-container--default .select2-selection--single .select2-selection__rendered{
	line-height:normal!important;
}
.select2-container .select2-search--inline .select2-search__field{
	min-height:18px;
}
.mb-3.form-check{
    width: 100%;
    padding: 12px 5px;
    margin: 20px 0;
    font-size: 13px !important;
    font-weight: 600;
    font-size: 16px !important;
}
.wo-add-head{
	display:flex;
	text-align: center;
}

.wo-add-head input.form-control{
	border: none;
    padding: 0;
	text-align: center;
}
.wo-add h3,.add-activity-column h3,.add-device h3 {
    text-align: center;
}
.wo-add-1{
	display:flex;
	gap: 5px;
}
.mb-3 {
    width: 100%;
}
#stop-time-section .row {
    display: flex
;
    gap: 5px;
}
.time-input-container {
    display: flex;
    align-items: center; /* تراز عمودی آیتم‌ها */
    gap: 5px; /* ایجاد فاصله کوچک بین عناصر */
}
.time-input-container input {
    width: 70px; /* تعیین یک عرض ثابت برای ورودی‌ها */
    text-align: center;
}
.time-colon {
    font-weight: 400;
    font-size: 1.4em;
}
.select2-dropdown{
	top:-25px!important;
	border: 1px solid #cccccc!important;
	border-top: none !important;
}
.select2-container--default .select2-search--dropdown .select2-search__field {
    border: none !important;
    border-bottom: 1px solid #cccccc !important
}

.select2-extra-info {
    color: #888; /* رنگ خاکستری برای متن کمرنگ */
    font-size: 0.9em; /* کمی کوچک‌تر کردن فونت */
    margin-right: 5px;
}
.required-asterisk {
    color: red;
    font-weight: bold;
    margin-right: 3px; /* کمی فاصله از متن اصلی */
}
/* کد اصلاح‌شده برای کانتینر جدول */
.table-scroll-container {
    max-height: 600px;
    overflow-y: auto;
    border: 1px solid #ddd;
    position: relative;
    border-radius: 8px; /* گرد کردن گوشه‌ها به اینجا منتقل شد */
}

/* این بخش سربرگ جدول را در بالا ثابت نگه می‌دارد */
.table-scroll-container th {
    position: sticky;
    top: 0;
    background-color: #f8f9fa; /* رنگ پس‌زمینه برای پوشاندن محتوای زیرین */
    z-index: 2; /* برای اطمینان از قرار گرفتن روی بقیه محتوا */
}
/* استایل‌های عمومی مودال‌ها */
.modal {
    display: none;
    position: fixed;
    z-index: 9999;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(4px);
    overflow: hidden;
}

/* جلوگیری از اسکرول صفحه پشت مودال */
body.modal-open {
    overflow: hidden;
    position: fixed;
    width: 100%;
}

/* استایل‌های جدید برای مودال جزئیات */
#detailsModalBody {
    background-color: #f9fafb; /* پس‌زمینه کمی خاکستری */
    margin: 2% auto;
    padding: 2rem; /* پدینگ بیشتر */
    width: 90%;
    max-width: 800px;
    max-height: 90vh;
    overflow-y: auto;
    border-radius: 12px;
    box-shadow: 0 10px 25px rgba(0,0,0,0.1);
    border: none;
}

/* واکنشگرایی مودال‌ها */
@media (max-width: 768px) {
    .modal .modal-content,
    .modal .modal-body,
    #detailsModalBody {
        width: 95%;
        margin: 5% auto;
        padding: 1rem;
        max-height: 85vh;
    }

    .modal .modal-header {
        padding: 1rem;
        flex-direction: column;
        gap: 0.5rem;
        text-align: center;
    }

    .modal .modal-header h3,
    .modal .modal-header h4 {
        font-size: 1.1rem;
        margin: 0;
    }

    .modal .btn {
        padding: 0.5rem 1rem;
        font-size: 0.9rem;
    }
}

#modalContent h4 {
    color: #1f2937;
    font-weight: 600;
    border-bottom: 2px solid #e5e7eb;
    padding-bottom: 1rem;
    margin-bottom: 1.5rem;
}

.details-grid {
    display: grid;
        grid-template-columns: repeat(auto-fit, minmax(33%, 1fr)); /* گرید واکنش‌گرا */
    
}

.detail-item {
    background-color: #ffffff;
    padding: 1rem;
    border-radius: 8px;
    border: 1px solid #e5e7eb;
	margin: 10px!important;
}

.detail-item strong {
    display: block;
    color: #6b7280; /* رنگ خاکستری برای عنوان */
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
}

.detail-item span, .detail-item div {
    font-size: 1rem;
    font-weight: 500;
}

.detail-item .priority-badge {
    display: inline;
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-weight: 600;
}
.priority-high { background-color: #fee2e2; color: #b91c1c; } /* قرمز */
.priority-medium { background-color: #fef3c7; color: #a16207; } /* زرد */
.priority-low { background-color: #dbeafe; color: #1e40af; } /* آبی */

.detail-full-width {
    grid-column: 1 / -1; /* آیتم‌هایی که تمام عرض را می‌گیرند */
}

.attachment-link a {
    text-decoration: none;
    color: #2563eb;
    font-weight: 500;
}
.attachment-link a:hover {
    text-decoration: underline;
}
input#workOrderSearch {
    max-width: 350px;
}
  /* این استایل‌ها فقط روی صفحه‌ای با id="page-submit-report" اعمال می‌شوند */
    #page-submit-report .report-container {
        max-width: 700px;
        margin: 2rem auto;
        padding: 1rem;
    }
    #page-submit-report .report-card {
        background-color: #ffffff;
        border-radius: 16px;
        padding: 2rem 2.5rem; /* پدینگ برای دسکتاپ */
        box-shadow: 0 8px 30px rgba(0, 0, 0, 0.05);
        border: 1px solid #e9ecef;
    }
    #page-submit-report .report-header {
        display: flex;
        justify-content: space-between;
        background-color: #f8f9fa;
        padding: 0.8rem 1rem;
        border-radius: 8px;
        margin-bottom: 2rem;
        font-size: 14px;
        color: #6c757d;
        border: 1px solid #dee2e6;
    }
    #page-submit-report .report-header strong {
        color: #495057;
    }
    #page-submit-report .report-title {
        text-align: center;
        color: #212529;
        margin-bottom: 2.5rem;
        font-weight: 700;
        font-size: 19px;
    }
    #page-submit-report .report-title i {
        color: #dc3545;
        margin-left: 0.5rem;
    }
    #page-submit-report .form-group {
        margin-bottom: 1.75rem;
    }
    #page-submit-report .form-label {
        display: block;
        margin-bottom: 0.6rem;
        font-weight: 600;
        font-size: 14px;
        color: #495057;
    }
    #page-submit-report .form-control {
        width: 100%;
        padding: 0.8rem 1rem;
        border: 1px solid #ced4da;
        border-radius: 8px;
        font-size: 14px;
        background-color: #fff;
        transition: border-color 0.2s, box-shadow 0.2s;
        box-sizing: border-box;
    }
    #page-submit-report .form-control:focus {
        border-color: var(--main-color, #d94307);
        box-shadow: 0 0 0 3px var(--card-shadow, rgba(255, 102, 0, 0.2));
        outline: none;
    }
    #page-submit-report .urgency-group {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 1rem;
    }
    #page-submit-report .urgency-group input[type="radio"] {
        display: none;
    }
    #page-submit-report .urgency-group label {
        padding: 1rem;
        border: 2px solid #e9ecef;
        border-radius: 8px;
        text-align: center;
        cursor: pointer;
        transition: all 0.2s ease-in-out;
        font-weight: 600;
    }
    #page-submit-report .urgency-group input[type="radio"]:checked + label {
        border-color: var(--main-color, #d94307);
        background-color: #fff5f0;
        color: #d94307;
    }
    #page-submit-report .form-check-input {
         float: right;
         margin-left: .5em;
    }
    #page-submit-report .hidden-section {
        display: none !important; 
    }
    #page-submit-report .submit-btn {
        width: 100%;
        padding: 1rem;
        border: none;
        border-radius: 8px;
        background: var(--main-color, #d94307);
        color: white;
        font-size: 16px;
        font-weight: 700;
        cursor: pointer;
        transition: background-color 0.2s;
    }
    #page-submit-report .submit-btn:disabled {
        background: #ced4da;
        cursor: not-allowed;
    }
    #page-submit-report #image-preview-container {
        display: flex;
        flex-wrap: wrap;
        gap: 1rem;
        margin-top: 1rem;
    }
    #page-submit-report .img-preview-wrapper {
        width: 100px;
        height: 100px;
    }
    #page-submit-report .img-preview {
        width: 100%;
        height: 100%;
        border-radius: 8px;
        object-fit: cover;
        border: 1px solid #ddd;
    }
    #page-submit-report #recent-reports-container {
         display: none;
         margin-top: 1rem;
         border-top: 1px solid #e9ecef;
         padding-top: 1rem;
    }
    #page-submit-report .recent-report-item {
         background-color: #fffbe6;
         border-right: 4px solid #f59e0b;
         padding: 0.8rem;
         border-radius: 6px;
         font-size: 14px;
         margin-bottom: 0.5rem;
    }

    /* ========================================================= */
    /* ## بخش جدید: رفع اسکرول و تنظیم فونت برای موبایل ## */
    /* ========================================================= */
    @media (max-width: 768px) {
       
        /* کاهش پدینگ افقی کارت برای جلوگیری از اسکرول */
        #page-submit-report .report-card {
            padding: 1.5rem 1rem;
        }
    }
/* =============================================
   استایل‌های هدر مدرن و واکنش‌گرا
   ============================================= */

/* استایل‌های اصلی هدر */
.main-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.75rem 1rem;
    background: linear-gradient(135deg, #d94307 0%, #ff6b35 100%);
    box-shadow: 0 2px 10px rgba(217, 67, 7, 0.15);
    position: relative;
    z-index: 1000;
}

/* برند/لوگو */
.header-brand {
    display: flex;
    align-items: center;
    flex-shrink: 0;
}

.brand-logo {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: white;
    font-weight: 700;
    font-size: 1.25rem;
    text-decoration: none;
}

.brand-logo i {
    font-size: 1.5rem;
}

.brand-text {
    font-family: 'Vazirmatn', sans-serif;
}

/* دکمه منوی موبایل */
.mobile-menu-toggle {
    display: none;
    flex-direction: column;
    justify-content: space-around;
    width: 2rem;
    height: 2rem;
    background: transparent;
    border: none;
    cursor: pointer;
    padding: 0;
    z-index: 1001;
}

.hamburger-line {
    width: 100%;
    height: 0.2rem;
    background: white;
    border-radius: 2px;
    transition: all 0.3s ease;
}

.mobile-menu-toggle.active .hamburger-line:nth-child(1) {
    transform: rotate(45deg) translate(0.4rem, 0.4rem);
}

.mobile-menu-toggle.active .hamburger-line:nth-child(2) {
    opacity: 0;
}

.mobile-menu-toggle.active .hamburger-line:nth-child(3) {
    transform: rotate(-45deg) translate(0.4rem, -0.4rem);
}

/* ناوبری اصلی */
.main-navigation {
    flex: 1;
    display: flex;
    justify-content: center;
}

.nav-menu {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    list-style: none;
    margin: 0;
    padding: 0;
}

.nav-item {
    position: relative;
}

.nav-link {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    color: white;
    text-decoration: none;
    border-radius: 6px;
    transition: all 0.2s ease;
    font-weight: 500;
    white-space: nowrap;
}

.nav-link:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-1px);
}

.nav-link i {
    font-size: 0.9rem;
}

/* دراپ‌داون منو */
.dropdown {
    position: relative;
}

.dropdown-toggle {
    cursor: pointer;
}

.dropdown-arrow {
    font-size: 0.7rem !important;
    transition: transform 0.2s ease;
}

.dropdown.active .dropdown-arrow {
    transform: rotate(180deg);
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    min-width: 200px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.2s ease;
    z-index: 1001; /* بالاتر از هدر */
    list-style: none;
    margin: 0.5rem 0 0 0;
    padding: 0.5rem 0;
    border: 1px solid rgba(0, 0, 0, 0.1);
}

.dropdown.active .dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-link {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1rem;
    color: #374151;
    text-decoration: none;
    transition: all 0.2s ease;
}

.dropdown-link:hover {
    background: #f3f4f6;
    color: #d94307;
}

.dropdown-link i {
    font-size: 0.9rem;
    width: 1rem;
    text-align: center;
}

/* شمارنده منو */
.nav-badge {
    background: #ff4757;
    color: white;
    font-size: 0.7rem;
    font-weight: 600;
    padding: 0.2rem 0.4rem;
    border-radius: 10px;
    
    min-width: 1.2rem;
    text-align: center;
    line-height: 1;
}

/* اطلاعات کاربر */
.header-user {
    display: flex;
    align-items: center;
    flex-shrink: 0;
}

.user-info {
    position: relative;
}

.user-profile-toggle {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    color: white;
    text-decoration: none;
    padding: 0.5rem;
    border-radius: 8px;
    transition: all 0.2s ease;
}

.user-profile-toggle:hover {
    background: rgba(255, 255, 255, 0.1);
}

.user-avatar {
    width: 2.5rem;
    height: 2.5rem;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.1rem;
    flex-shrink: 0;
}

.user-details {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    line-height: 1.2;
}

.user-name {
    font-weight: 600;
    font-size: 0.9rem;
}

.user-role {
    font-size: 0.75rem;
    opacity: 0.8;
}

.user-dropdown-arrow {
    font-size: 0.7rem !important;
    transition: transform 0.2s ease;
    margin-right: 0.5rem;
}

.user-info.dropdown.active .user-dropdown-arrow {
    transform: rotate(180deg);
}

.user-dropdown-menu {
    right: 0;
    min-width: 180px;
    margin-top: 0.5rem;
}

.dropdown-divider {
    height: 1px;
    background: #e5e7eb;
    margin: 0.5rem 0;
}

.logout-link {
    color: #dc2626 !important;
}

.logout-link:hover {
    background: #fef2f2 !important;
    color: #dc2626 !important;
}

/* استایل‌های موبایل */
@media (max-width: 768px) {
    .mobile-menu-toggle {
        display: flex;
    }

    .main-navigation {
        position: fixed;
        top: 0;
        right: -100%;
        width: 280px;
        height: 100vh;
        background: linear-gradient(135deg, #d94307 0%, #ff6b35 100%);
        padding: 5rem 1rem 2rem;
        transition: right 0.3s ease;
        z-index: 999;
        overflow-y: auto;
    }

    .main-navigation.active {
        right: 0;
    }

    .nav-menu {
        flex-direction: column;
        gap: 0;
        width: 100%;
    }

    .nav-item {
        width: 100%;
    }

    .nav-link {
        padding: 1rem;
        border-radius: 0;
        justify-content: flex-start;
        width: 100%;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }

    .dropdown-menu {
        position: static;
        background: rgba(255, 255, 255, 0.1);
        box-shadow: none;
        border-radius: 0;
        opacity: 1;
        visibility: visible;
        transform: none;
        margin: 0;
        padding: 0;
        max-height: 0;
        overflow: hidden;
        transition: max-height 0.3s ease;
    }

    .dropdown.active .dropdown-menu {
        max-height: 300px;
    }

    .dropdown-link {
        color: rgba(255, 255, 255, 0.9);
        padding: 0.75rem 2rem;
        border-bottom: 1px solid rgba(255, 255, 255, 0.05);
    }

    .dropdown-link:hover {
        background: rgba(255, 255, 255, 0.1);
        color: white;
    }

    .user-details {
        display: none;
    }

    .user-avatar {
        width: 2rem;
        height: 2rem;
        font-size: 0.9rem;
    }

    .user-dropdown-arrow {
        display: none;
    }

    .user-dropdown-menu {
        position: static;
        background: rgba(255, 255, 255, 0.1);
        box-shadow: none;
        border-radius: 0;
        opacity: 1;
        visibility: visible;
        transform: none;
        margin: 0;
        padding: 0;
        max-height: 0;
        overflow: hidden;
        transition: max-height 0.3s ease;
    }

    .user-info.dropdown.active .user-dropdown-menu {
        max-height: 200px;
    }

    .nav-badge {
        font-size: 0.6rem;
        padding: 0.15rem 0.3rem;
    }
}

/* استایل‌های دسکتاپ */
@media (min-width: 769px) {
    .main-header {
        padding: 1rem 2rem;
    }

    .nav-link {
        font-size: 0.9rem;
    }

    .brand-logo {
        font-size: 1.4rem;
    }

    .brand-logo i {
        font-size: 1.6rem;
    }
}

/* استایل‌های دکمه یادآوری در هدر جدید */
#reminders-btn {
    margin-left: 1rem;
    order: -1; /* قرار دادن قبل از اطلاعات کاربر */
    border: none;
    font-family: inherit;
}

.header-user {
    display: flex;
    align-items: center;
    gap: 1rem;
}

@media (max-width: 768px) {
    #reminders-btn {
        margin-left: 0.5rem;
        padding: 6px 12px;
        font-size: 0.85rem;
    }

    .header-user {
        gap: 0.5rem;
    }
}
.alert.alert-info.mb-3 {
    background: #d4ebff;
    padding: 10px;
    margin: 10px 0px;
    width: auto;
}
 /* ✨ استایل‌های ضروری برای مودال تصویر که وجود نداشتند ✨ */
    .image-modal {
        display: none;
        position: fixed;
        z-index: 1050; /* بالاتر از مودال جزئیات */
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        overflow: auto;
        background-color: rgba(0, 0, 0, 0.85);
        -webkit-animation-name: fadeIn;
        animation-name: fadeIn;
        -webkit-animation-duration: 0.4s;
        animation-duration: 0.4s;
    }
    .image-modal-content {
        margin: auto;
        display: block;
        max-width: 80%;
        max-height: 80%;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
    }
    .image-modal-close {
        position: absolute;
        top: 20px;
        right: 35px;
        color: #fff;
        font-size: 40px;
        font-weight: bold;
        transition: 0.3s;
        cursor: pointer;
    }
    .image-modal-close:hover, .image-modal-close:focus {
        color: #bbb;
        text-decoration: none;
    }
	 /* استایل‌های بخش پیوست‌ها */
    .attachments-container { border: 1px solid #ddd; padding: 1rem; border-radius: 8px; background-color: #f9f9f9; margin: 1rem;width:auto; }
    .attachments-list { display: flex; flex-wrap: wrap; gap: 1rem; margin-top: 1rem; border-top: 1px solid #eee; padding-top: 1rem; }
    .attachment-item { position: relative; }
    .attachment-item img { width: 100px; height: 100px; object-fit: cover; border-radius: 5px; border: 1px solid #ccc; }
    .attachment-item .remove-btn { 
        position: absolute; top: -7px; left: -7px; width: 22px; height: 22px; 
        background-color: #dc3545; color: white; border: none; border-radius: 50%; 
        font-size: 14px; line-height: 22px; text-align: center; cursor: pointer; 
        font-weight: bold; box-shadow: 0 2px 5px rgba(0,0,0,0.2); 
    }
/* ========================================================================== */
/* Typography Standardization                                                 */
/* ========================================================================== */

html { font-size: 16px; } /* Desktop base */

@media (max-width: 991.98px) {
  html { font-size: 14px; } /* Mobile base */
}

body {
  font-size: var(--fs-md);
}

h1 { font-size: var(--fs-3xl); line-height: 1.2; }
h2 { font-size: var(--fs-2xl); line-height: 1.3; }
h3 { font-size: var(--fs-xl); line-height: 1.35; }
h4 { font-size: var(--fs-lg); line-height: 1.4; }
h5 { font-size: var(--fs-md); line-height: 1.45; }
h6 { font-size: var(--fs-md); line-height: 1.5; }

/* Utility font-size classes */
.text-xxs { font-size: var(--fs-xxs) !important; }
.text-xs  { font-size: var(--fs-xs) !important; }
.text-sm  { font-size: var(--fs-sm) !important; }
.text-md  { font-size: var(--fs-md) !important; }
.text-lg  { font-size: var(--fs-lg) !important; }
.text-xl  { font-size: var(--fs-xl) !important; }
.text-2xl { font-size: var(--fs-2xl) !important; }
.text-3xl { font-size: var(--fs-3xl) !important; }
.text-4xl { font-size: var(--fs-4xl) !important; }
a.btn-poke, .btn-poke-icon {
  background: linear-gradient(90deg, #ffb366 0%, #d94307 100%);
  color: #fff;
  border: none;
  border-radius: 8px;
  padding: 8px 16px;
  font-size: 1rem;
  font-weight: 600;
  box-shadow: 0 2px 8px rgba(255, 102, 0, 0.12);
  cursor: pointer;
  transition: background 0.2s, box-shadow 0.2s;
  margin: 0 4px;
  display: inline-flex;
  align-items: center;
  gap: 6px;
}
a.btn-poke:hover, .btn-poke-icon:hover {
  background: linear-gradient(90deg, #d94307 0%, #ffb366 100%);
  box-shadow: 0 4px 16px rgba(255, 102, 0, 0.18);
}
.btn-poke-logs {
  background: #fff3e0;
  color: #d94307;
  border: 1px solid #ffb366;
  border-radius: 8px;
  padding: 7px 14px;
  font-size: 0.95rem;
  font-weight: 500;
  cursor: pointer;
  transition: background 0.2s, border 0.2s;
  margin: 0 4px;
  display: inline-flex;
  align-items: center;
  gap: 6px;
}
.btn-poke-logs:hover {
  background: #ffb366;
  color: #fff;
  border: 1px solid #d94307;
}
.predefined-messages {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 10px;
}
.predefined-msg-btn {
  background: #f9f9f9;
  color: #d94307;
  border: 1px solid #ffb366;
  border-radius: 6px;
  padding: 6px 12px;
  font-size: 0.95rem;
  cursor: pointer;
  transition: background 0.2s, color 0.2s;
}
.predefined-msg-btn:hover {
  background: #ffb366;
  color: #fff;
}
.recipients-info {
  background: #fffbe6;
  color: #a16207;
  border-radius: 6px;
  padding: 6px 10px;
  font-size: 0.95rem;
  margin-top: 8px;
  display: flex;
  align-items: center;
  gap: 6px;
}
.form-actions {
  display: flex;
  gap: 10px;
  margin-top: 12px;
  flex-wrap: wrap;
}
#pokeModal .modal-content, #pokeLogsModal .modal-content {
  background: #fff;
  border-radius: 16px;
  padding: 2rem 1.5rem;
  max-width: 400px;
  margin: 5% auto;
  box-shadow: 0 8px 32px rgba(255, 102, 0, 0.15);
  border: none;
}
#pokeModal h3, #pokeLogsModal h3 {
  color: #d94307;
  font-size: 1.3rem;
  font-weight: 700;
  margin-bottom: 1.2rem;
  text-align: center;
}
#pokeLogsContent {
  max-height: 300px;
  overflow-y: auto;
  padding: 8px 0;
}
#pokeLogsContent .no-logs {
  color: #888;
  text-align: center;
  padding: 16px 0;
  font-size: 1rem;
}
@media (max-width: 600px) {
  #pokeModal .modal-content, #pokeLogsModal .modal-content {
    max-width: 98vw;
    padding: 1rem 0.5rem;
  }
  .predefined-messages {
    flex-direction: column;
    gap: 6px;
  }
  .form-actions {
    flex-direction: column;
    gap: 8px;
  }
  .btn-poke, .btn-poke-logs, .btn-poke-icon {
    width: 100%;
    justify-content: center;
    font-size: 1.05rem;
  }
}
a.btn.btn-secondary {
    background: #ffffff;
    color: #3f3f3f;
    border: solid 1px #c9c9c9;
    border-radius: 8px;
    padding: 6px 10px;
    cursor: pointer;
    transition: background-color 0.3s ease;
    text-decoration: auto;
    margin-bottom: 10px;
    display: inline-block;
}
