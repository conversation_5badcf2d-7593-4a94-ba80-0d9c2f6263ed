<?php
require_once '../includes/auth.php';
require_once '../db_connection.php';
require_once '../includes/date_helper.php';

// بررسی دسترسی به صفحه دستگاه‌ها
require_page_access('devices', 'create');

$pdo = db_connect();

function get_locations() {
    global $pdo;
    $stmt = $pdo->query("SELECT id, location_name FROM locations ORDER BY location_name");
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

// افزودن دستگاه جدید و قطعات مصرفی
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'add_device') {
    try {
        $uploaded_filenames = [];
        if (isset($_FILES['images']) && !empty($_FILES['images']['name'][0])) {
            $file_count = count($_FILES['images']['name']);
            $targetDir = "../uploads/devices/";
            if (!is_dir($targetDir)) mkdir($targetDir, 0755, true);

            for ($i = 0; $i < $file_count; $i++) {
                if ($_FILES['images']['error'][$i] === UPLOAD_ERR_OK) {
                    $filename = uniqid() . "_" . basename($_FILES['images']['name'][$i]);
                    if (move_uploaded_file($_FILES['images']['tmp_name'][$i], $targetDir . $filename)) {
                        $uploaded_filenames[] = $filename;
                    }
                }
            }
        }
        $images_json = !empty($uploaded_filenames) ? json_encode($uploaded_filenames) : null;

        $purchase_date = !empty($_POST['purchase_date']) ? to_miladi($_POST['purchase_date']) : null;
        $installation_date = !empty($_POST['installation_date']) ? to_miladi($_POST['installation_date']) : null;
        $operation_date = !empty($_POST['operation_date']) ? to_miladi($_POST['operation_date']) : null;
        $manufacture_year = !empty($_POST['manufacture_year']) ? (int)$_POST['manufacture_year'] : null;
        
        $pdo->beginTransaction();

        $stmt = $pdo->prepare("INSERT INTO devices 
        (name, serial_number, description, status, purchase_date, vendor_name, vendor_phone, images, location, manufacture_year, installation_date, operation_date) 
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
        $stmt->execute([
            $_POST['name'],
            $_POST['serial_number'],
            $_POST['description'],
            $_POST['status'],
            $purchase_date,
            $_POST['vendor_name'],
            $_POST['vendor_phone'],
            $images_json,
            $_POST['location'],
            $manufacture_year,
            $installation_date,
            $operation_date
        ]);
        $deviceId = $pdo->lastInsertId();

        if (!empty($_POST['consumable_name']) && is_array($_POST['consumable_name'])) {
            $stmtPart = $pdo->prepare("INSERT INTO device_parts (device_id, part_name, quantity, unit, description, created_at) VALUES (?, ?, ?, ?, ?, NOW())");
            foreach ($_POST['consumable_name'] as $index => $partName) {
                $quantity = isset($_POST['consumable_quantity'][$index]) ? (int)$_POST['consumable_quantity'][$index] : 1;
                $unit = trim($_POST['consumable_unit'][$index] ?? 'عدد');
                $desc = $_POST['consumable_description'][$index] ?? null;
                if(trim($partName) !== '') {
                    $stmtPart->execute([$deviceId, $partName, $quantity, $unit, $desc]);
                }
            }
        }

        // ذخیره مشخصات الکتروموتورها
        if (!empty($_POST['motor_name']) && is_array($_POST['motor_name'])) {
            $stmtMotor = $pdo->prepare("INSERT INTO device_motors (device_id, motor_name, voltage, power, current, rpm, description, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, NOW())");
            foreach ($_POST['motor_name'] as $index => $motorName) {
                $voltage = $_POST['motor_voltage'][$index] ?? null;
                $power = $_POST['motor_power'][$index] ?? null;
                $current = $_POST['motor_current'][$index] ?? null;
                $rpm = $_POST['motor_rpm'][$index] ?? null;
                $desc = $_POST['motor_description'][$index] ?? null;
                if(trim($motorName) !== '') {
                    $stmtMotor->execute([$deviceId, $motorName, $voltage, $power, $current, $rpm, $desc]);
                }
            }
        }

        $pdo->commit();

        $_SESSION['toast_message'] = 'دستگاه با موفقیت افزوده شد.';
        $_SESSION['toast_type'] = 'success';
        header('Location: devices.php');
        exit;

    } catch (PDOException $e) {
        $pdo->rollBack();
        $_SESSION['toast_message'] = 'خطا در پایگاه داده: ' . $e->getMessage();
        $_SESSION['toast_type'] = 'danger';
        header('Location: device_add.php');
        exit;
    }
}

$locations = get_locations();

include '../includes/header.php';
?>
<head>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="../assets/css/persian-datepicker.min.css">
    <script src="../assets/js/jquery.min.js"></script>
    <script src="../assets/js/persian-date.min.js"></script>
    <script src="../assets/js/persian-datepicker.min.js"></script>

    <style>
        * { box-sizing: border-box; }
        body { overflow-x: hidden; }

        .add-device-container {
           
            margin: 0 auto;
            padding: 20px;
        }

        .form-row { 
            margin-bottom: 30px; 
        }
        .form-row:last-child { 
            margin-bottom: 0; 
        }
        .form-row label { 
            display: block; 
            margin-bottom: 5px; 
            font-weight: bold;
        }
        .form-row input,
        .form-row select,
        .form-row textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }
        .form-row textarea {
            min-height: 100px;
            resize: vertical;
        }

        /* استایل برای ردیف‌های چند ستونه */
        .form-row-multi {
            display: flex;
            gap: 15px;
            margin-bottom: 15px;
        }
        
        .form-row-multi .form-field {
            flex: 1;
        }
        
        .form-row-multi label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        
        .form-row-multi input,
        .form-row-multi select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }

        #consumableFieldset, #motorFieldset { 
            border: 1px solid #ddd; 
            padding: 15px; 
            border-radius: 5px; 
            background-color: #f9f9f9;
        }

        .consumable-row, .motor-row { 
            display: flex; 
            flex-wrap: wrap; 
            gap: 10px; 
            margin-bottom: 15px;
            align-items: flex-end;
            padding: 15px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            background-color: #fafafa;
        }
        
        .consumable-field, .motor-field {
            flex: 1;
            min-width: 120px;
        }
        
        .consumable-field label, .motor-field label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            font-size: 12px;
            color: #555;
        }
        
        .consumable-field input, .motor-field input {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .consumable-row button, .motor-row button {
            background-color: #dc3545;
            color: white;
            border: none;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            cursor: pointer;
            font-weight: bold;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;
        }

        .add-consumable-btn, .add-motor-btn {
            background-color: #28a745;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }

        .submit-btn {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            width: 100%;
            margin-top: 20px;
        }

        .submit-btn:hover {
            background-color: #0056b3;
        }

        .submit-btn:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }

        .back-btn {
            background-color: #6c757d;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 5px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin-bottom: 20px;
        }

        .back-btn:hover {
            background-color: #545b62;
            color: white;
            text-decoration: none;
        }

        .page-title {
            text-align: center;
            margin-bottom: 30px;
            color: #333;
        }

        @media (max-width: 768px) {
            .add-device-container {
                padding: 15px;
            }
            
            .form-row-multi {
                flex-direction: column;
                gap: 10px;
            }
            
            .consumable-row, .motor-row {
                flex-direction: column;
                align-items: stretch;
            }
            
            .consumable-field, .motor-field {
                min-width: auto;
            }
        }
    </style>
</head>
<body>
<div class="add-device-container">
    <a href="devices.php" class="back-btn">
        <i class="fas fa-arrow-right"></i> بازگشت به لیست دستگاه‌ها
    </a>
    
    <h2 class="page-title">تعریف دستگاه جدید</h2>
    
    <form method="post" enctype="multipart/form-data" id="addDeviceForm">
        <input type="hidden" name="action" value="add_device">
        
        <div class="form-row-multi">
            <div class="form-field">
                <label for="name">نام دستگاه:</label>
                <input type="text" id="name" name="name" required>
            </div>
            <div class="form-field">
                <label for="serial_number">شماره سریال:</label>
                <input type="text" id="serial_number" name="serial_number" required>
            </div>
        </div>
        
        <div class="form-row-multi">
            <div class="form-field">
                <label for="location">محل استفاده:</label>
                <select name="location" id="location" required>
                    <option value="">انتخاب محل استفاده</option>
                    <?php foreach ($locations as $loc): ?>
                        <option value="<?= $loc['id'] ?>"><?= htmlspecialchars($loc['location_name']) ?></option>
                    <?php endforeach; ?>
                </select>
            </div>
            <div class="form-field">
                <label for="status">وضعیت:</label>
                <select name="status" id="status" required>
                    <option value="فعال">فعال</option>
                    <option value="غیرفعال">غیرفعال</option>
                    <option value="در حال تعمیر">در حال تعمیر</option>
                </select>
            </div>
        </div>
        
        <div class="form-row-multi">
            <div class="form-field">
                <label for="purchase_date">تاریخ خرید:</label>
                <input type="text" class="persian-datepicker" id="purchase_date" name="purchase_date" placeholder="انتخاب تاریخ" autocomplete="off">
            </div>
            
            <div class="form-field">
                <label for="vendor_name">فروشنده:</label>
                <input type="text" id="vendor_name" name="vendor_name">
            </div>
            
            <div class="form-field">
                <label for="vendor_phone">شماره تماس فروشنده:</label>
                <input type="tel" id="vendor_phone" name="vendor_phone">
            </div>
        </div>
        
        <div class="form-row-multi">
            <div class="form-field">
                <label for="manufacture_year">سال ساخت:</label>
                <input type="number" id="manufacture_year" name="manufacture_year" placeholder="مثال: 1402" min="1300" max="1500">
            </div>
            
            <div class="form-field">
                <label for="installation_date">تاریخ نصب:</label>
                <input type="text" class="persian-datepicker" id="installation_date" name="installation_date" placeholder="انتخاب تاریخ" autocomplete="off">
            </div>
            
            <div class="form-field">
                <label for="operation_date">تاریخ بهره‌برداری:</label>
                <input type="text" class="persian-datepicker" id="operation_date" name="operation_date" placeholder="انتخاب تاریخ" autocomplete="off">
            </div>
        </div>
        
        <div class="form-row">
            <label>قطعات مصرفی:</label>
            <fieldset id="consumableFieldset">
                <div id="consumablesContainer"></div>
                <button type="button" onclick="addConsumableRow()" class="add-consumable-btn">
                    <i class="fas fa-plus"></i> افزودن قطعه
                </button>
            </fieldset>
        </div>
        
        <div class="form-row">
            <label>مشخصات الکتروموتورها:</label>
            <fieldset id="motorFieldset">
                <div id="motorsContainer"></div>
                <button type="button" onclick="addMotorRow()" class="add-motor-btn">
                    <i class="fas fa-plus"></i> افزودن مشخصات
                </button>
            </fieldset>
        </div>
        
        <div class="form-row">
            <?php
            require_once '../includes/file_uploader.php';
            echo render_file_uploader([
                'id' => 'deviceImages',
                'name' => 'images[]',
                'accept' => 'image/*',
                'max_size' => 2,
                'label' => 'تصاویر دستگاه',
                'description' => 'فایل‌های عکس - حداکثر 2 مگابایت',
                'show_existing' => false
            ]);
            ?>
        </div>
        
        <div class="form-row">
            <label for="description">توضیحات:</label>
            <textarea name="description" id="description" placeholder="توضیحات اضافی درباره دستگاه..."></textarea>
        </div>
        
        <button type="submit" id="add-device-btn" class="submit-btn">افزودن دستگاه</button>
    </form>
</div>

<?php include '../includes/footer.php'; ?>

<script>
function addConsumableRow() {
    const container = document.getElementById('consumablesContainer');
    const div = document.createElement('div');
    div.className = 'consumable-row';
    div.innerHTML = `
        <div class="consumable-field">
            <label>نام قطعه:</label>
            <input type="text" name="consumable_name[]" placeholder="نام قطعه" required>
        </div>
        <div class="consumable-field">
            <label>تعداد:</label>
            <input type="number" name="consumable_quantity[]" placeholder="تعداد" min="1" value="1" required>
        </div>
        <div class="consumable-field">
            <label>واحد:</label>
            <input type="text" name="consumable_unit[]" placeholder="واحد شمارش" value="عدد" required>
        </div>
        <div class="consumable-field">
            <label>توضیحات:</label>
            <input type="text" name="consumable_description[]" >
        </div>
        <button type="button" onclick="removeConsumableRow(this)">×</button>
    `;
    container.appendChild(div);
}

function removeConsumableRow(button) { 
    button.parentElement.remove(); 
}

function addMotorRow() {
    const container = document.getElementById('motorsContainer');
    const div = document.createElement('div');
    div.className = 'motor-row';
    div.innerHTML = `
        <div class="motor-field">
            <label>نام الکتروموتور:</label>
            <input type="text" name="motor_name[]" placeholder="نام الکتروموتور" required>
        </div>
        <div class="motor-field">
            <label>ولتاژ:</label>
            <input type="text" name="motor_voltage[]" placeholder="مثال: 380V">
        </div>
        <div class="motor-field">
            <label>توان:</label>
            <input type="text" name="motor_power[]" placeholder="مثال: 5.5KW">
        </div>
        <div class="motor-field">
            <label>جریان:</label>
            <input type="text" name="motor_current[]" placeholder="مثال: 11A">
        </div>
        <div class="motor-field">
            <label>RPM:</label>
            <input type="text" name="motor_rpm[]" placeholder="مثال: 1450">
        </div>
        <div class="motor-field">
            <label>توضیحات:</label>
            <input type="text" name="motor_description[]" placeholder="توضیحات اضافی">
        </div>
        <button type="button" onclick="removeMotorRow(this)">×</button>
    `;
    container.appendChild(div);
}

function removeMotorRow(button) { 
    button.parentElement.remove(); 
}

document.getElementById('addDeviceForm').addEventListener('submit', function(event) {
    const form = event.target;
    const btn = document.getElementById('add-device-btn');

    // بررسی validation
    if (!form.checkValidity()) {
        event.preventDefault();
        showToast('لطفاً تمام فیلدهای اجباری را پر کنید.', 'warning');
        return;
    }

    btn.disabled = true;
    btn.textContent = 'در حال افزودن...';

    // فرم به صورت معمولی ارسال می‌شود
    // فایل‌ها از طریق کامپوننت file uploader مدیریت می‌شوند
});

$(document).ready(function() {
    initializeDatepicker('.persian-datepicker');
    // Rows are added by user action, not on page load.
});
</script>
</body>
</html>
