<?php

if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// بارگذاری کلاس‌های مورد نیاز
require_once __DIR__ . '/PermissionSystem.php';
require_once __DIR__ . '/../db_connection.php';

// ایجاد اتصال دیتابیس و نمونه سیستم مجوزها
$pdo = db_connect();
$permission_system = new PermissionSystem($pdo);

// اگر کاربر لاگین نکرده باشد به صفحه لاگین هدایت می‌شود
if (!isset($_SESSION['user'])) {
    header("Location: ../pages/login.php");
    exit;
}

// بررسی فعال بودن کاربر
if (!$permission_system->is_user_active($_SESSION['user']['id'])) {
    // کاربر غیرفعال است، session را پاک کن و به صفحه لاگین هدایت کن
    session_destroy();
    session_start();
    $_SESSION['toast_message'] = 'حساب کاربری شما غیرفعال شده است. لطفاً با مدیر سیستم تماس بگیرید.';
    $_SESSION['toast_type'] = 'danger';
    header("Location: ../pages/login.php");
    exit;
}

// تابع بررسی نقش ادمین (برای سازگاری با کد قدیمی)
function is_admin() {
    return isset($_SESSION['user']['role']) && $_SESSION['user']['role'] === 'admin';
}

// تابع جدید: دریافت اطلاعات کاربر جاری
function current_user() {
    return $_SESSION['user'] ?? null;
}

// تابع جدید: دریافت شناسه کاربر جاری
function current_user_id() {
    return $_SESSION['user']['id'] ?? null;
}

// تابع جدید: دریافت نام کاربر جاری
function current_user_name() {
    return $_SESSION['user']['name'] ?? $_SESSION['user']['username'] ?? 'کاربر ناشناس';
}

// تابع جدید: بررسی مجوز
function has_permission($page_name, $permission_name) {
    global $permission_system;
    return $permission_system->has_permission($page_name, $permission_name);
}

// تابع جدید: الزام دسترسی به صفحه
function require_page_access($page_name, $permission_name = 'view', $redirect_url = '../pages/dashboard.php') {
    global $permission_system;
    $permission_system->require_page_access($page_name, $permission_name, $redirect_url);
}

// تابع جدید: دریافت صفحات قابل دسترس
function get_accessible_pages($user_id = null) {
    global $permission_system;
    return $permission_system->get_accessible_pages($user_id);
}

// تابع جدید: بررسی فعال بودن کاربر
function is_user_active($user_id) {
    global $permission_system;
    return $permission_system->is_user_active($user_id);
}

// تابع بررسی احراز هویت کاربر
function check_auth() {
    if (!isset($_SESSION['user'])) {
        header("Location: ../pages/login.php");
        exit;
    }

    global $permission_system;
    if (!$permission_system->is_user_active($_SESSION['user']['id'])) {
        session_destroy();
        session_start();
        $_SESSION['toast_message'] = 'حساب کاربری شما غیرفعال شده است. لطفاً با مدیر سیستم تماس بگیرید.';
        $_SESSION['toast_type'] = 'danger';
        header("Location: ../pages/login.php");
        exit;
    }
}
?>