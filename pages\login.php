<?php
session_start();
require '../db_connection.php';
$pdo = db_connect();

/**
 * تابع بررسی رمز عبور - پشتیبانی از هر دو نوع hash شده و متن ساده
 */
function verify_password($input_password, $stored_password) {
    // اگر رمز عبور hash شده باشد (bcrypt)
    if (strlen($stored_password) == 60 && substr($stored_password, 0, 4) == '$2y$') {
        return password_verify($input_password, $stored_password);
    }
    // اگر رمز عبور متن ساده باشد (برای سازگاری با کاربران قدیمی)
    else {
        return $input_password === $stored_password;
    }
}

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $username = $_POST['username'];
    $password = $_POST['password'];

    // جستجوی کاربر بر اساس نام کاربری
    $stmt = $pdo->prepare("SELECT * FROM users WHERE username = ?");
    $stmt->execute([$username]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);

    // احراز هویت کاربر
    if ($user) {
        // بررسی فعال بودن کاربر
        if (!$user['is_active']) {
            $_SESSION['toast_message'] = "حساب کاربری شما غیرفعال است. لطفاً با مدیر سیستم تماس بگیرید.";
            $_SESSION['toast_type'] = "danger";
        }
        // بررسی رمز عبور (پشتیبانی از هر دو نوع: hash شده و متن ساده)
        elseif (verify_password($password, $user['password'])) {
            // دریافت اطلاعات نقش کاربر
            $role_stmt = $pdo->prepare("SELECT name, display_name FROM roles WHERE id = ?");
            $role_stmt->execute([$user['role_id']]);
            $role_info = $role_stmt->fetch(PDO::FETCH_ASSOC);

            // ذخیره تمام اطلاعات کاربر در session
            $_SESSION['user'] = [
                'id' => $user['id'],
                'name' => $user['name'],
                'username' => $user['username'],
                'role' => $user['role'] ?? 'user', // نگه داشتن برای سازگاری
                'role_id' => $user['role_id'],
                'role_name' => $role_info['name'] ?? 'user',
                'role_display_name' => $role_info['display_name'] ?? 'کاربر عادی',
                'is_active' => $user['is_active']
            ];

            // هدایت به صفحه اصلی
            header('Location: ../pages/dashboard.php');
            exit;
        } else {
            $_SESSION['toast_message'] = "نام کاربری یا رمز عبور اشتباه است.";
            $_SESSION['toast_type'] = "danger";
        }
    } else {
        $_SESSION['toast_message'] = "نام کاربری یا رمز عبور اشتباه است.";
        $_SESSION['toast_type'] = "danger";
    }
}

// دریافت پیام از session
$toast_message = null;
$toast_type = null;

if (isset($_SESSION['toast_message'])) {
    $toast_message = $_SESSION['toast_message'];
    $toast_type = $_SESSION['toast_type'] ?? 'info';
    // حذف پیام از session تا فقط یک بار نمایش داده شود
    unset($_SESSION['toast_message']);
    unset($_SESSION['toast_type']);
}
?>

<!DOCTYPE html>
<html lang="fa" dir="rtl">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title>ورود | نرم افزار نت دمباز</title>
    <link rel="stylesheet" href="../assets/css/style.css" />
    <?php if ($toast_message): ?>
        <meta name="toast-message" content="<?= htmlspecialchars($toast_message) ?>">
        <meta name="toast-type" content="<?= htmlspecialchars($toast_type) ?>">
    <?php endif; ?>
    <style>
        /* Toast Message Styles - Global */
        .toast-container {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 9999;
        }
        .toast {
            background: white;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            padding: 1rem 1.5rem;
            margin-bottom: 10px;
            border-right: 4px solid;
            min-width: 300px;
            transform: translateX(100%);
            transition: transform 0.3s ease;
            opacity: 0;
        }
        .toast.show {
            transform: translateX(0);
            opacity: 1;
        }
        .toast-success {
            border-color: #28a745;
            color: #155724;
        }
        .toast-warning {
            border-color: #ffc107;
            color: #856404;
        }
        .toast-danger {
            border-color: #dc3545;
            color: #721c24;
        }
        .toast-info {
            border-color: #17a2b8;
            color: #0c5460;
        }
        .toast-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.5rem;
        }
        .toast-title {
            font-weight: 600;
            font-size: var(--fs-sm);
        }
        .toast-close {
            background: none;
            border: none;
            font-size: var(--fs-xl);
            cursor: pointer;
            color: #666;
            padding: 0;
            line-height: 1;
        }
        .toast-message {
            font-size: var(--fs-sm);
            line-height: 1.4;
        }
    </style>
</head>
<body class="login-page">
    <div id="toast-container" class="toast-container"></div>
    <div class="background-overlay"></div>
    <div class="login-wrapper">
        <div class="login-box">
            <div class="logo">
                <!-- می‌تونی اینجا لوگو یا آیکون بذاری -->
                <svg width="64" height="64" viewBox="0 0 64 64" fill="#4a90e2" xmlns="http://www.w3.org/2000/svg">
                    <circle cx="32" cy="32" r="30" stroke="#357abd" stroke-width="3" fill="none" />
                    <path d="M20 32 L28 40 L44 24" stroke="#4a90e2" stroke-width="4" fill="none" stroke-linecap="round" stroke-linejoin="round" />
                </svg>
            </div>
            <h2>ورود به نرم افزار نت دمباز</h2>
            <form method="post" class="login-form" novalidate>
                <div class="input-group">
                    <input type="text" name="username" placeholder="نام کاربری" required autocomplete="username" />
                    <span class="input-icon">&#128100;</span>
                </div>
                <div class="input-group">
                    <input type="password" name="password" placeholder="رمز عبور" required autocomplete="current-password" />
                    <span class="input-icon">&#128274;</span>
                </div>
                <button type="submit" class="login-button">ورود</button>
            </form>
        </div>
    </div>
    
    <script>
    // Global Toast Message Functions
    function showToast(message, type = 'info', duration = 4000) {
        const container = document.getElementById('toast-container');
        if (!container) return;
        
        const toast = document.createElement('div');
        toast.className = `toast toast-${type}`;
        
        const title = type === 'success' ? 'موفقیت' : 
                      type === 'warning' ? 'هشدار' : 
                      type === 'danger' ? 'خطا' : 'اطلاعات';
        
        toast.innerHTML = `
            <div class="toast-header">
                <span class="toast-title">${title}</span>
                <button class="toast-close" onclick="this.parentElement.parentElement.remove()">&times;</button>
            </div>
            <div class="toast-message">${message}</div>
        `;
        
        container.appendChild(toast);
        
        // Show animation
        setTimeout(() => {
            toast.classList.add('show');
        }, 100);
        
        // Auto remove after duration
        setTimeout(() => {
            toast.classList.remove('show');
            setTimeout(() => {
                if (toast.parentElement) {
                    toast.remove();
                }
            }, 300);
        }, duration);
    }

    // Show toast on page load if message exists in session
    document.addEventListener('DOMContentLoaded', function() {
        // جلوگیری از نمایش دوباره پیام در footer
        window.skipFooterToast = true;

        // Check if there's a toast message from PHP session
        const toastMessage = document.querySelector('meta[name="toast-message"]');
        const toastType = document.querySelector('meta[name="toast-type"]');

        if (toastMessage && toastMessage.content) {
            showToast(toastMessage.content, toastType ? toastType.content : 'info', 5000);
        }
    });
    </script>
</body>
</html>