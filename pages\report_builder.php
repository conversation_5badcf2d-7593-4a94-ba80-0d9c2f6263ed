<?php
require_once '../includes/auth.php';
require_once '../db_connection.php';
require_once '../includes/report_query_builder_semantic.php'; // Using the semantic builder

// Access control
require_page_access('report_builder', 'view');

$pdo = db_connect();
$builder = new SemanticReportQueryBuilder(); // Instantiate the builder

// Handle AJAX endpoints
if ($_SERVER['REQUEST_METHOD'] === 'GET' && isset($_GET['action'])) {
    header('Content-Type: application/json; charset=utf-8');
    if ($_GET['action'] === 'get_semantic_layer') {
        echo json_encode(['success' => true, 'layer' => $builder->getSemanticLayer()], JSON_UNESCAPED_UNICODE);
        exit;
    }
}

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    header('Content-Type: application/json; charset=utf-8');
    $definition = json_decode($_POST['definition'] ?? '{}', true) ?: [];
    
    if ($_POST['action'] === 'execute') {
        try {
            require_page_access('report_builder', 'execute');
            $result = $builder->execute($pdo, $definition);
            echo json_encode(['success' => true, 'result' => $result], JSON_UNESCAPED_UNICODE);
        } catch (Throwable $e) {
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => $e->getMessage()], JSON_UNESCAPED_UNICODE);
        }
        exit;
    }
    
    if ($_POST['action'] === 'save') {
        require_page_access('report_builder', 'create');
        $name = trim($_POST['name'] ?? '');
        if (empty($name)) {
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'نام گزارش الزامی است.'], JSON_UNESCAPED_UNICODE);
            exit;
        }
        try {
            // Minimal validation by trying to build the query
            $builder->buildSql($definition);
            $stmt = $pdo->prepare('INSERT INTO report_definitions (name, description, owner_id, is_public, definition_json) VALUES (?,?,?,?,?)');
            $stmt->execute([
                $name, 
                trim($_POST['description'] ?? ''), 
                current_user_id(), 
                isset($_POST['is_public']) && $_POST['is_public'] === 'true' ? 1 : 0, 
                json_encode($definition, JSON_UNESCAPED_UNICODE)
            ]);
            echo json_encode(['success' => true, 'message' => 'گزارش با موفقیت ذخیره شد.'], JSON_UNESCAPED_UNICODE);
        } catch (Throwable $e) {
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => $e->getMessage()], JSON_UNESCAPED_UNICODE);
        }
        exit;
    }
}

include '../includes/header.php';
?>
<!DOCTYPE html>
<html lang="fa" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>گزارش ساز بصری</title>
	<script src="../assets/js/chart.min.js"></script>
    <link rel="stylesheet" href="../assets/css/all.min.css">
    <style>
        
        /* --- General Styles --- */
        body { 
            font-family: 'Vazirmatn', sans-serif; 
            background-color: #f8fafc;
            color: #333;
            margin: 0;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 1.5rem;
        }
        .card {
            background-color: white;
            padding: 1.5rem;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.08);
			display: block;

        }
		.card:hover, .card:focus {
    transform: unset;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    outline: none;
}
        /* --- Header --- */
        .page-header {
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
            gap: 1rem;
        }
        .page-header h1 {
            font-size: 1.5rem;
            font-weight: bold;
            color: #1f2937;
            margin: 0;
        }
        .page-header p {
            color: #6b7280;
            margin: 0.25rem 0 0 0;
        }
        .header-actions {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        .btn {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.6rem 1rem;
            border-radius: 8px;
            border: none;
            cursor: pointer;
            font-weight: 600;
            transition: background-color 0.2s;
        }
        .btn-secondary {
            background-color: #e5e7eb;
            color: #1f2937;
        }
        .btn-secondary:hover {
            background-color: #d1d5db;
        }
        .btn-primary {
            background-color: #4f46e5;
            color: white;
            box-shadow: 0 4px 6px rgba(79, 70, 229, 0.2);
        }
        .btn-primary:hover {
            background-color: #4338ca;
        }

        /* --- Layout --- */
        .layout-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: 1.5rem;
        }
        @media (min-width: 1024px) {
            .layout-grid {
                grid-template-columns: 3fr 1fr;
            }
        }
        .main-content {
            display: flex;
            flex-direction: column;
            gap: 1.5rem;
        }
        .builder-zones {
            display: grid;
            grid-template-columns: 1fr;
            gap: 1.5rem;
        }
        @media (min-width: 768px) {
            .builder-zones {
                grid-template-columns: 1fr 1fr;
            }
        }

        /* --- Drop Zones & Builder --- */
        .zone-title {
            font-weight: bold;
            color: #374151;
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        .zone-title .fa-align-justify { color: #6366f1; }
        .zone-title .fa-chart-bar { color: #10b981; }
        .zone-title .fa-filter { color: #f59e0b; }
        .zone-title .fa-chart-pie { color: #ef4444; }

        .drop-zone {
            min-height: 80px;
            background-color: #f9fafb;
            padding: 0.5rem;
            border-radius: 8px;
            border: 2px dashed #e5e7eb;
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
            transition: background-color 0.2s;
        }
        .drop-zone.drag-over {
            background-color: #e0e7ff;
        }
        .drop-zone-placeholder {
            text-align: center;
            color: #9ca3af;
            padding: 1rem;
            margin: auto;
        }
        .dropped-item {
            background-color: white;
            padding: 0.5rem;
            border-radius: 6px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.05);
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }
        .dropped-item-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .dropped-item-header button {
            color: #ef4444;
            background: none;
            border: none;
            cursor: pointer;
        }
        .filter-input, .filter-select {
            width: 100%;
            margin-top: 0.5rem;
            padding: 0.5rem;
            border: 1px solid #d1d5db;
            border-radius: 6px;
        }
        .custom-date-range {
            display: flex;
            gap: 0.5rem;
        }

        /* --- Sidebar Fields --- */
        .sidebar-title {
            font-size: 1.1rem;
            font-weight: bold;
            margin-bottom: 1rem;
        }
        .fields-group {
            margin-bottom: 1.5rem;
        }
        .fields-group-title {
            font-weight: 600;
            color: #4b5563;
            border-bottom: 1px solid #e5e7eb;
            padding-bottom: 0.5rem;
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        .fields-group-title .fa-cube { color: #6366f1; }
        .fields-group-title .fa-calculator { color: #10b981; }

        .field-item {
            background-color: #f3f4f6;
            padding: 0.5rem;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            cursor: grab;
            margin-bottom: 0.5rem;
        }
        .field-item:active {
            cursor: grabbing;
        }
        .field-item.dragging {
            opacity: 0.5;
        }
        .field-item i {
            color: #9ca3af;
        }

        /* --- Results Area --- */
        #results-area {
            position: relative;
            min-height: 300px;
        }
        #results-placeholder {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-align: center;
            color: #9ca3af;
            height: 100%;
            position: absolute;
            width: 100%;
        }
        #results-placeholder i {
            font-size: 3rem;
            margin-bottom: 0.75rem;
        }
        #chart-wrapper.hidden, #table-wrapper.hidden {
            display: none;
        }
        #table-wrapper {
            margin-top: 1.5rem;
            overflow-x: auto;
        }
        #results-table {
            width: 100%;
            font-size: 0.9rem;
            text-align: right;
            border-collapse: collapse;
        }
        #results-table th, #results-table td {
            padding: 0.75rem;
            border-top: 1px solid #e5e7eb;
        }
        #results-table thead {
            background-color: #f9fafb;
        }

        /* --- Save Modal --- */
        #save-modal {
            position: fixed;
            inset: 0;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 50;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 1rem;
        }
        #save-modal.hidden {
            display: none;
        }
        .modal-content {
            background-color: white;
            border-radius: 8px;
            padding: 1.5rem;
            width: 100%;
            max-width: 28rem;
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }
        .modal-content h3 {
            font-size: 1.1rem;
            font-weight: bold;
        }
        .modal-input, .modal-textarea {
            width: 100%;
            border: 1px solid #d1d5db;
            border-radius: 8px;
            padding: 0.6rem;
        }
        .modal-footer {
            display: flex;
            justify-content: flex-end;
            gap: 0.5rem;
        }
    </style>
</head>
<body>

    <div class="container">
        <!-- Header -->
        <div class="page-header">
            <div>
                <h1>گزارش ساز بصری</h1>
                <p>گزارش‌های خود را با کشیدن و رها کردن بسازید.</p>
            </div>
           
        </div>

        <div class="layout-grid">
            <!-- Main Canvas: Report Builder -->
            <main class="main-content">
                <!-- Builder Drop Zones -->
                <div class="builder-zones">
                    <!-- Rows & Columns -->
                    <div class="card">
                        <div id="rows-container">
                            <h3 class="zone-title"><i class="fas fa-align-justify"></i> سطرها (ابعاد)</h3>
                            <div id="rows-drop-zone" class="drop-zone">
                                <p class="drop-zone-placeholder">ابعاد را اینجا بکشید</p>
                            </div>
                        </div>
                        <hr style="margin: 1.5rem 0; border: none; border-top: 1px solid #f3f4f6;">
                        <div id="columns-container">
                            <h3 class="zone-title"><i class="fas fa-chart-bar"></i> ستون‌ها (سنجه‌ها)</h3>
                            <div id="columns-drop-zone" class="drop-zone">
                                <p class="drop-zone-placeholder">سنجه‌ها را اینجا بکشید</p>
                            </div>
                        </div>
                    </div>
                    <!-- Filters & Chart Type -->
                    <div class="card">
                        <div id="filters-container">
                            <h3 class="zone-title"><i class="fas fa-filter"></i> فیلترها</h3>
                            <div id="filters-drop-zone" class="drop-zone">
                               <p class="drop-zone-placeholder">فیلدها را برای فیلتر کردن بکشید</p>
                            </div>
                        </div>
                        <hr style="margin: 1.5rem 0; border: none; border-top: 1px solid #f3f4f6;">
                        <div>
                            <h3 class="zone-title"><i class="fas fa-chart-pie"></i> نوع نمودار</h3>
                            <select id="chart-type-select" class="filter-select">
                                <option value="bar">ستونی</option>
                                <option value="line">خطی</option>
                                <option value="pie">دایره‌ای</option>
                                <option value="doughnut">حلقه‌ای</option>
                            </select>
                        </div>
                    </div>
                </div>
 <div class="header-actions">
                <button id="save-report-btn" class="btn btn-secondary">
                    <i class="fas fa-save"></i>
                    <span>ذخیره گزارش</span>
                </button>
                <button id="run-report-btn" class="btn btn-primary">
                    <i class="fas fa-play"></i>
                    <span>اجرای گزارش</span>
                </button>
            </div>
                <!-- Results Area -->
                <div class="card">
                    <h2 style="font-size: 1.25rem; font-weight: bold; margin-bottom: 1rem;">نتایج</h2>
                    <div id="results-area">
                        <div id="results-placeholder">
                            <i class="fas fa-search"></i>
                            <p>گزارش را برای دیدن نتایج اجرا کنید</p>
                        </div>
                        <div id="chart-wrapper" class="hidden">
                            <canvas id="results-chart"></canvas>
                        </div>
                        <div id="table-wrapper" class="hidden">
                            <table id="results-table"></table>
                        </div>
                    </div>
                </div>
            </main>

            <!-- Sidebar: Fields -->
            <aside>
                <div class="card">
                    <h2 class="sidebar-title">فیلدها</h2>
                    <div class="fields-group">
                        <h3 class="fields-group-title"><i class="fas fa-cube"></i> ابعاد</h3>
                        <div id="dimensions-list"></div>
                    </div>
                    <div class="fields-group">
                        <h3 class="fields-group-title"><i class="fas fa-calculator"></i> سنجه‌ها</h3>
                        <div id="measures-list"></div>
                    </div>
                </div>
            </aside>
        </div>
    </div>
    
    <!-- Save Modal -->
    <div id="save-modal" class="hidden">
        <div class="modal-content">
            <h3>ذخیره گزارش</h3>
            <input id="save-name" type="text" placeholder="نام گزارش" class="modal-input">
            <textarea id="save-desc" placeholder="توضیحات (اختیاری)" class="modal-textarea" rows="3"></textarea>
            <label style="display: flex; align-items: center; gap: 0.5rem;"><input id="save-public" type="checkbox"> اشتراک عمومی</label>
            <div class="modal-footer">
                <button id="save-modal-cancel" class="btn btn-secondary">لغو</button>
                <button id="save-modal-confirm" class="btn btn-primary">ذخیره</button>
            </div>
        </div>
    </div>

<script>
// The Javascript logic remains the same as it does not depend on Tailwind.
// ... (All previous Javascript code is here)
document.addEventListener('DOMContentLoaded', () => {
    const state = {
        semanticLayer: null,
        reportDef: {
            rows: [],
            columns: [],
            filters: {},
            chartType: 'bar'
        },
        chartInstance: null
    };

    const DOM = {
        dimensionsList: document.getElementById('dimensions-list'),
        measuresList: document.getElementById('measures-list'),
        rowsDropZone: document.getElementById('rows-drop-zone'),
        columnsDropZone: document.getElementById('columns-drop-zone'),
        filtersDropZone: document.getElementById('filters-drop-zone'),
        runReportBtn: document.getElementById('run-report-btn'),
        saveReportBtn: document.getElementById('save-report-btn'),
        chartTypeSelect: document.getElementById('chart-type-select'),
        resultsPlaceholder: document.getElementById('results-placeholder'),
        chartWrapper: document.getElementById('chart-wrapper'),
        tableWrapper: document.getElementById('table-wrapper'),
        resultsChart: document.getElementById('results-chart'),
        resultsTable: document.getElementById('results-table'),
        saveModal: {
            el: document.getElementById('save-modal'),
            name: document.getElementById('save-name'),
            desc: document.getElementById('save-desc'),
            public: document.getElementById('save-public'),
            cancel: document.getElementById('save-modal-cancel'),
            confirm: document.getElementById('save-modal-confirm'),
        }
    };
    
    const apiFetch = async (action, body = {}) => {
        try {
            const formData = new FormData();
            formData.append('action', action);
            for (const key in body) formData.append(key, body[key]);
            const response = await fetch('report_builder.php', { method: 'POST', body: formData });
            const data = await response.json();
            if (!response.ok) throw new Error(data.message || 'خطا در سرور');
            return data;
        } catch (error) {
            alert(`خطا: ${error.message}`);
            return null;
        }
    };

    const loadSemanticLayer = async () => {
        const response = await fetch('report_builder.php?action=get_semantic_layer');
        const data = await response.json();
        if (data.success) {
            state.semanticLayer = data.layer;
            renderFieldLists();
        }
    };

    const renderFieldLists = () => {
        DOM.dimensionsList.innerHTML = '';
        DOM.measuresList.innerHTML = '';
        Object.entries(state.semanticLayer.dimensions).forEach(([key, val]) => {
            DOM.dimensionsList.appendChild(createFieldItem(key, val.label, 'dimension', val.type));
        });
        Object.entries(state.semanticLayer.measures).forEach(([key, val]) => {
            DOM.measuresList.appendChild(createFieldItem(key, val.label, 'measure'));
        });
    };

    const createFieldItem = (key, label, type, fieldType = 'text') => {
        const item = document.createElement('div');
        item.className = 'field-item';
        item.draggable = true;
        item.dataset.key = key;
        item.dataset.label = label;
        item.dataset.type = type;
        item.dataset.fieldType = fieldType;
        item.innerHTML = `<span>${label}</span><i class="fas fa-grip-vertical"></i>`;
        item.addEventListener('dragstart', handleDragStart);
        return item;
    };

    const renderDropZones = () => {
        renderZone(DOM.rowsDropZone, state.reportDef.rows, 'ابعاد را اینجا بکشید');
        renderZone(DOM.columnsDropZone, state.reportDef.columns, 'سنجه‌ها را اینجا بکشید');
        renderFilters();
    };

    const renderZone = (zone, items, placeholderText) => {
        zone.innerHTML = items.length === 0 ? `<p class="drop-zone-placeholder">${placeholderText}</p>` : '';
        items.forEach(key => {
            const field = state.semanticLayer.dimensions[key] || state.semanticLayer.measures[key];
            zone.appendChild(createDroppedItem(key, field.label));
        });
    };
    
    const renderFilters = () => {
        DOM.filtersDropZone.innerHTML = Object.keys(state.reportDef.filters).length === 0 ? `<p class="drop-zone-placeholder">فیلدها را برای فیلتر کردن بکشید</p>` : '';
        Object.entries(state.reportDef.filters).forEach(([key, filterValue]) => {
            const fieldInfo = state.semanticLayer.dimensions[key];
            if (fieldInfo.type === 'date') {
                DOM.filtersDropZone.appendChild(createDateFilterItem(key, fieldInfo.label, filterValue));
            } else {
                DOM.filtersDropZone.appendChild(createTextFilterItem(key, fieldInfo.label, filterValue));
            }
        });
    }

    const createDroppedItem = (key, label) => {
        const item = document.createElement('div');
        item.className = 'dropped-item';
        item.dataset.key = key;
        
        const header = document.createElement('div');
        header.className = 'dropped-item-header';
        header.innerHTML = `<span>${label}</span><button><i class="fas fa-times-circle"></i></button>`;
        header.querySelector('button').onclick = () => handleRemoveItem(key);
        
        item.appendChild(header);
        return item;
    };
    
    const createTextFilterItem = (key, label, value) => {
        const item = createDroppedItem(key, label);
        const input = document.createElement('input');
        input.type = 'text';
        input.placeholder = `مقدار برای ${label}...`;
        input.className = 'filter-input';
        input.value = value || '';
        input.oninput = (e) => { state.reportDef.filters[key] = e.target.value; };
        item.appendChild(input);
        return item;
    };

    const createDateFilterItem = (key, label, value) => {
        const item = createDroppedItem(key, label);
        const container = document.createElement('div');
        container.style.marginTop = '0.5rem';

        const presets = {
            'today': 'امروز', 'yesterday': 'دیروز', 'this_week': 'این هفته', 'last_week': 'هفته قبل',
            'this_month': 'این ماه', 'last_month': 'ماه قبل', 'this_year': 'امسال', 'last_year': 'سال قبل',
            'last_90_days': '۹۰ روز اخیر', 'custom': 'بازه سفارشی'
        };
        
        const select = document.createElement('select');
        select.className = 'filter-select';
        for(const [val, text] of Object.entries(presets)) {
            select.add(new Option(text, val));
        }
        select.value = value.period || 'this_month';

        const customRangeDiv = document.createElement('div');
        customRangeDiv.className = 'custom-date-range';
        customRangeDiv.style.display = (select.value === 'custom' ? 'flex' : 'none');
        customRangeDiv.innerHTML = `
            <input type="text" placeholder="شروع (YYYY-MM-DD)" class="filter-input" data-role="start">
            <input type="text" placeholder="پایان (YYYY-MM-DD)" class="filter-input" data-role="end">
        `;
        
        select.onchange = () => {
            const period = select.value;
            customRangeDiv.style.display = (period === 'custom' ? 'flex' : 'none');
            state.reportDef.filters[key] = { period: period, start: '', end: '' };
        };
        
        const startInput = customRangeDiv.querySelector('[data-role="start"]');
        const endInput = customRangeDiv.querySelector('[data-role="end"]');
        startInput.oninput = (e) => { state.reportDef.filters[key].start = e.target.value; };
        endInput.oninput = (e) => { state.reportDef.filters[key].end = e.target.value; };

        if (value.period === 'custom') {
            startInput.value = value.start || '';
            endInput.value = value.end || '';
        }

        container.append(select, customRangeDiv);
        item.appendChild(container);
        return item;
    };

const renderResults = (data) => {
        DOM.resultsPlaceholder.style.display = 'none';
        DOM.chartWrapper.classList.remove('hidden');
        DOM.tableWrapper.classList.remove('hidden');

        if (state.chartInstance) state.chartInstance.destroy();
        
        const { columns, rows } = data;
        if (!rows || rows.length === 0) {
             DOM.resultsPlaceholder.style.display = 'flex';
             DOM.resultsPlaceholder.innerHTML = '<p>داده‌ای برای نمایش یافت نشد.</p>';
             DOM.chartWrapper.classList.add('hidden');
             DOM.tableWrapper.classList.add('hidden');
             return;
        }

        // --- Table Rendering (remains the same) ---
        DOM.resultsTable.innerHTML = '';
        const thead = document.createElement('thead');
        let trHead = '<tr>';
        columns.forEach(c => trHead += `<th>${c.label}</th>`);
        thead.innerHTML = `${trHead}</tr>`;
        DOM.resultsTable.appendChild(thead);
        
        const tbody = document.createElement('tbody');
        let tableBodyHtml = '';
        rows.forEach(row => {
            let trBody = '<tr>';
            columns.forEach(c => trBody += `<td>${row[c.key] ?? '0'}</td>`);
            trBody += '</tr>';
            tableBodyHtml += trBody;
        });
        tbody.innerHTML = tableBodyHtml;
        DOM.resultsTable.appendChild(tbody);

        // --- Chart Rendering (Revised Logic) ---
        const chartCtx = DOM.resultsChart.getContext('2d');
        const labels = rows.map(row => row[columns[0].key]);
        const measures = state.reportDef.columns;
        
        // A predefined, pleasant color palette
        const baseColors = [
            'rgba(79, 70, 229, 0.7)', 'rgba(52, 211, 153, 0.7)', 'rgba(245, 158, 11, 0.7)', 
            'rgba(239, 68, 68, 0.7)', 'rgba(99, 102, 241, 0.7)', 'rgba(168, 85, 247, 0.7)',
            'rgba(236, 72, 153, 0.7)', 'rgba(16, 185, 129, 0.7)'
        ];

        const datasets = [];
        const chartType = state.reportDef.chartType;

        if (measures.length > 1 && (chartType === 'bar' || chartType === 'line')) {
            // Case: Multiple measures (e.g., MTBF and MTTR by month) -> Grouped Bar/Line Chart
            for (let i = 0; i < measures.length; i++) {
                const measureKey = measures[i];
                const measureLabel = columns.find(c => c.key === measureKey)?.label || measureKey;
                datasets.push({
                    label: measureLabel,
                    data: rows.map(row => row[measureKey]),
                    backgroundColor: baseColors[i % baseColors.length], // One color per dataset
                    borderColor: baseColors[i % baseColors.length].replace('0.7', '1'),
                    borderWidth: 1
                });
            }
        } else {
            // Case: Single measure or Pie/Doughnut chart
            const measureKey = measures[0];
            const measureLabel = columns.find(c => c.key === measureKey)?.label || measureKey;
            
            // For these charts, we want different colors for each dimension item (each bar/slice)
            const backgroundColors = labels.map((_, i) => baseColors[i % baseColors.length]);
            
            datasets.push({
                label: measureLabel,
                data: rows.map(row => row[measureKey]),
                backgroundColor: backgroundColors, // Array of colors
                borderColor: backgroundColors.map(c => c.replace('0.7', '1')),
                borderWidth: 1
            });
        }
        
        state.chartInstance = new Chart(chartCtx, {
            type: chartType,
            data: { labels, datasets },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        // Show legend only if it's meaningful (multiple datasets or a pie chart)
                        display: datasets.length > 1 || chartType === 'pie' || chartType === 'doughnut'
                    }
                }
            }
        });
    };

    let draggedItem = null;
    const handleDragStart = (e) => {
        draggedItem = e.target;
        e.dataTransfer.effectAllowed = 'move';
        e.dataTransfer.setData('text/plain', JSON.stringify({
            key: e.target.dataset.key,
            label: e.target.dataset.label,
            type: e.target.dataset.type,
            fieldType: e.target.dataset.fieldType
        }));
        setTimeout(() => e.target.classList.add('dragging'), 0);
    };

    const handleDrop = (e) => {
        e.preventDefault();
        e.currentTarget.classList.remove('drag-over');
        const data = JSON.parse(e.dataTransfer.getData('text/plain'));
        const dropZoneId = e.currentTarget.id;

        if (dropZoneId === 'rows-drop-zone' && data.type === 'dimension') {
            if (!state.reportDef.rows.includes(data.key)) state.reportDef.rows.push(data.key);
        } else if (dropZoneId === 'columns-drop-zone' && data.type === 'measure') {
            if (!state.reportDef.columns.includes(data.key)) state.reportDef.columns.push(data.key);
        } else if (dropZoneId === 'filters-drop-zone' && data.type === 'dimension') {
            if (!state.reportDef.filters[data.key]) {
                state.reportDef.filters[data.key] = data.fieldType === 'date' ? { period: 'this_month' } : '';
            }
        } else { return; }
        renderDropZones();
    };
    
    const handleRemoveItem = (key) => {
        state.reportDef.rows = state.reportDef.rows.filter(k => k !== key);
        state.reportDef.columns = state.reportDef.columns.filter(k => k !== key);
        delete state.reportDef.filters[key];
        renderDropZones();
    };

    const handleRunReport = async () => {
        if (state.reportDef.columns.length === 0) {
            alert('لطفا حداقل یک ستون (سنجه) را برای گزارش مشخص کنید.');
            return;
        }
        const data = await apiFetch('execute', { definition: JSON.stringify(state.reportDef) });
        if (data?.success) renderResults(data.result);
    };
    
    const handleSaveReport = async () => {
        const name = DOM.saveModal.name.value.trim();
        if (!name) { alert('نام گزارش الزامی است.'); return; }
        const body = {
            name: name,
            description: DOM.saveModal.desc.value.trim(),
            is_public: DOM.saveModal.public.checked.toString(),
            definition: JSON.stringify(state.reportDef)
        };
        const data = await apiFetch('save', body);
        if(data?.success) {
            alert(data.message);
            DOM.saveModal.el.classList.add('hidden');
        }
    };

    [DOM.rowsDropZone, DOM.columnsDropZone, DOM.filtersDropZone].forEach(zone => {
        zone.addEventListener('dragover', (e) => { e.preventDefault(); e.currentTarget.classList.add('drag-over'); });
        zone.addEventListener('dragleave', (e) => e.currentTarget.classList.remove('drag-over'));
        zone.addEventListener('drop', handleDrop);
        document.body.addEventListener('dragend', () => {
            draggedItem?.classList.remove('dragging');
            zone.classList.remove('drag-over');
        });
    });

    DOM.runReportBtn.addEventListener('click', handleRunReport);
    DOM.chartTypeSelect.addEventListener('change', (e) => {
        state.reportDef.chartType = e.target.value;
        if(state.chartInstance) handleRunReport();
    });
    
    DOM.saveReportBtn.addEventListener('click', () => DOM.saveModal.el.classList.remove('hidden'));
    DOM.saveModal.cancel.addEventListener('click', () => DOM.saveModal.el.classList.add('hidden'));
    DOM.saveModal.confirm.addEventListener('click', handleSaveReport);

    loadSemanticLayer();
	 // =================================================================
    // START: کد جدید برای اسکرول خودکار صفحه
    // =================================================================
    const SCROLL_THRESHOLD = 200; // فاصله از لبه صفحه برای شروع اسکرول (به پیکسل) - می‌توانید این عدد را تغییر دهید
    const SCROLL_SPEED = 15; // سرعت اسکرول - می‌توانید این عدد را تغییر دهید

    document.addEventListener('dragover', (e) => {
        // این منطق فقط زمانی اجرا می‌شود که یک آیتم در حال کشیده شدن باشد
        if (draggedItem) { 
            const viewportHeight = window.innerHeight;
            const mouseY = e.clientY;

            // اسکرول به بالا
            if (mouseY < SCROLL_THRESHOLD) {
                window.scrollBy(0, -SCROLL_SPEED);
            }
            // اسکرول به پایین
            else if (mouseY > viewportHeight - SCROLL_THRESHOLD) {
                window.scrollBy(0, SCROLL_SPEED);
            }
        }
    });
    // =================================================================
    // END: کد جدید برای اسکرول خودکار صفحه
    // =================================================================


    DOM.runReportBtn.addEventListener('click', handleRunReport);
    DOM.chartTypeSelect.addEventListener('change', (e) => {
        state.reportDef.chartType = e.target.value;
        if(state.chartInstance) handleRunReport();
    });
    
    DOM.saveReportBtn.addEventListener('click', () => DOM.saveModal.el.classList.remove('hidden'));
    DOM.saveModal.cancel.addEventListener('click', () => DOM.saveModal.el.classList.add('hidden'));
    DOM.saveModal.confirm.addEventListener('click', handleSaveReport);

    loadSemanticLayer();
});
</script>
</body>
</html>
