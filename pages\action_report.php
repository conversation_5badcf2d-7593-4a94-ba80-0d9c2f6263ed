<?php
require_once '../includes/auth.php';
require_once '../db_connection.php';
require_once '../includes/date_helper.php';

date_default_timezone_set('Asia/Tehran');
$pdo = db_connect();
$current_user_id = current_user_id();

if (!isset($_GET['work_order_id']) || !is_numeric($_GET['work_order_id'])) {
    // Redirect to an error page or show a friendly message instead of die()
    header('Location: /error.php?message=invalid_id');
    exit;
}
$work_order_id = (int)$_GET['work_order_id'];

// Check if the current user is assigned to this work order
$access_stmt = $pdo->prepare("SELECT COUNT(*) FROM work_order_assignees WHERE work_order_id = ? AND user_id = ?");
$access_stmt->execute([$work_order_id, $current_user_id]);
if ($access_stmt->fetchColumn() == 0) {
    // Redirect to an error page or show a friendly message instead of die()
    header('Location: /error.php?message=access_denied');
    exit;
}


if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // CSRF Token validation
    if (!isset($_POST['csrf_token']) || !hash_equals($_SESSION['csrf_token'], $_POST['csrf_token'])) {
        die('CSRF token validation failed.');
    }

    $verifier_id = $_POST['verifier_id'] ?? null;

    if (empty($verifier_id)) {
        $_SESSION['toast_message'] = 'لطفا فرد تایید کننده را انتخاب کنید.';
        $_SESSION['toast_type'] = 'warning';
        header('Location: action_report.php?work_order_id=' . $work_order_id);
        exit;
    } else {
        $pdo->beginTransaction();
        try {
            // Process main report datetimes
            $start_datetime_str = (!empty($_POST['start_date']) && !empty($_POST['start_time'])) ? to_miladi($_POST['start_date']) . ' ' . $_POST['start_time'] : null;
            $end_datetime_str = (!empty($_POST['end_date']) && !empty($_POST['end_time'])) ? to_miladi($_POST['end_date']) . ' ' . $_POST['end_time'] : date('Y-m-d H:i:s');
            
            // Validate that end date is not before start date
            if ($start_datetime_str && $end_datetime_str) {
                $start = new DateTime($start_datetime_str);
                $end = new DateTime($end_datetime_str);
                if ($end < $start) {
                    throw new Exception('تاریخ یا ساعت پایان نمی‌تواند قبل از شروع باشد.');
                }
            }

            // Process main report fields
            $notes = $_POST['completion_notes'] ?? '';
            $delay_reason = $_POST['delay_reason'] ?? null;

            // Process outsource fields
            $exit_date_str = !empty($_POST['exit_date']) ? to_miladi($_POST['exit_date']) : null;
            $exit_where = $_POST['exit_where'] ?? null;
            $exit_desc = $_POST['exit_desc'] ?? null;
            $back_date_str = !empty($_POST['back_date']) ? to_miladi($_POST['back_date']) : null;
            $back_desc = $_POST['back_desc'] ?? null;
            // Remove commas before converting to int
            $back_pay = !empty($_POST['back_pay']) ? (int)str_replace(',', '', $_POST['back_pay']) : null;

            // Calculate actual labor hours
            $actual_labor_hours = null;
            if ($start_datetime_str && $end_datetime_str) {
                $diff_seconds = (new DateTime($end_datetime_str))->getTimestamp() - (new DateTime($start_datetime_str))->getTimestamp();
                $actual_labor_hours = round($diff_seconds / 3600, 2);
            }

            // Insert or update execution details (now includes outsource fields)
            $sql = "INSERT INTO work_order_execution (
                        work_order_id, completion_notes, delay_reason, actual_labor_hours, 
                        actual_start_datetime, actual_end_datetime, completed_by_id,
                        exit_date, exit_where, exit_desc, back_date, back_desc, back_pay
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) 
                    ON DUPLICATE KEY UPDATE 
                        completion_notes = VALUES(completion_notes), 
                        delay_reason = VALUES(delay_reason), 
                        actual_labor_hours = VALUES(actual_labor_hours), 
                        actual_start_datetime = VALUES(actual_start_datetime), 
                        actual_end_datetime = VALUES(actual_end_datetime), 
                        completed_by_id = VALUES(completed_by_id),
                        exit_date = VALUES(exit_date),
                        exit_where = VALUES(exit_where),
                        exit_desc = VALUES(exit_desc),
                        back_date = VALUES(back_date),
                        back_desc = VALUES(back_desc),
                        back_pay = VALUES(back_pay)";

            $stmt = $pdo->prepare($sql);
            $stmt->execute([
                $work_order_id, $notes, $delay_reason, $actual_labor_hours, 
                $start_datetime_str, $end_datetime_str, $current_user_id,
                $exit_date_str, $exit_where, $exit_desc, 
                $back_date_str, $back_desc, $back_pay
            ]);

            // Process labor
            $pdo->prepare("DELETE FROM work_order_labor WHERE work_order_id = ?")->execute([$work_order_id]);
            if (isset($_POST['personnel_id']) && is_array($_POST['personnel_id'])) {
                $personnel_ids = $_POST['personnel_id'];
                $num_laborers = count(array_filter($personnel_ids, fn($id) => $id !== 'outsource')); // Count only actual users
                $hours_per_laborer = ($num_laborers > 0 && $actual_labor_hours > 0) ? round($actual_labor_hours / $num_laborers, 2) : 0;
                $labor_stmt = $pdo->prepare("INSERT INTO work_order_labor (work_order_id, user_id, hours_worked) VALUES (?, ?, ?)");
                foreach ($personnel_ids as $person_id) {
                    // Only insert actual user IDs, not the 'outsource' placeholder
                    if (!empty($person_id) && $person_id !== 'outsource') {
                        $labor_stmt->execute([$work_order_id, $person_id, $hours_per_laborer]);
                    }
                }
            }

            // Process parts
            $pdo->prepare("DELETE FROM work_order_parts WHERE work_order_id = ?")->execute([$work_order_id]);
            if (isset($_POST['part_name'])) {
                $part_stmt = $pdo->prepare("INSERT INTO work_order_parts (work_order_id, part_name, quantity_used, unit) VALUES (?, ?, ?, ?)");
                foreach ($_POST['part_name'] as $index => $partName) {
                    if (!empty(trim($partName))) {
                        $quantity = !empty($_POST['part_quantity'][$index]) ? (int)$_POST['part_quantity'][$index] : 1;
                        $unit = $_POST['part_unit'][$index] ?? 'عدد';
                        $part_stmt->execute([$work_order_id, $partName, $quantity, $unit]);
                    }
                }
            }

            // Update work order status
            $status_stmt = $pdo->prepare("UPDATE work_orders SET status = 'منتظر تایید', verifier_id = ? WHERE id = ?");
            $status_stmt->execute([$verifier_id, $work_order_id]);

            // Update source breakdown report status if exists
            $source_report_stmt = $pdo->prepare("UPDATE breakdown_reports SET status = 'منتظر تایید' WHERE converted_to_wo_id = ?");
            $source_report_stmt->execute([$work_order_id]);

            $pdo->commit();
            $_SESSION['toast_message'] = 'گزارش با موفقیت ثبت و برای تایید ارسال شد.';
            $_SESSION['toast_type'] = 'success';
            header('Location: my_tasks.php');
            exit;
        } catch (Exception $e) {
            if ($pdo->inTransaction()) $pdo->rollBack();
            $_SESSION['toast_message'] = 'خطا در ثبت گزارش: ' . $e->getMessage();
            $_SESSION['toast_type'] = 'danger';
            header('Location: action_report.php?work_order_id=' . $work_order_id);
            exit;
        }
    }
}

// Generate CSRF token
if (empty($_SESSION['csrf_token'])) {
    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
}
$csrf_token = $_SESSION['csrf_token'];

// Fetch existing data for the form
$wo_stmt = $pdo->prepare("SELECT title, status FROM work_orders WHERE id = ?");
$wo_stmt->execute([$work_order_id]);
$work_order = $wo_stmt->fetch(PDO::FETCH_ASSOC);

$exec_stmt = $pdo->prepare("SELECT * FROM work_order_execution WHERE work_order_id = ?");
$exec_stmt->execute([$work_order_id]);
$execution_data = $exec_stmt->fetch(PDO::FETCH_ASSOC) ?: [];

// Check if the task is outsourced based on 'exit_where' field
$is_outsourced = !empty($execution_data['exit_where']);

$labor_stmt = $pdo->prepare("SELECT user_id FROM work_order_labor WHERE work_order_id = ?");
$labor_stmt->execute([$work_order_id]);
$labor_data = $labor_stmt->fetchAll(PDO::FETCH_COLUMN);

$parts_stmt = $pdo->prepare("SELECT part_name, quantity_used, unit FROM work_order_parts WHERE work_order_id = ?");
$parts_stmt->execute([$work_order_id]);
$parts_data = $parts_stmt->fetchAll(PDO::FETCH_ASSOC);

require_once '../includes/user_helper.php';
$users = getUsersForSelect($pdo);

// دریافت پیام از session
$toast_message = $_SESSION['toast_message'] ?? null;
$toast_type = $_SESSION['toast_type'] ?? 'info';
unset($_SESSION['toast_message'], $_SESSION['toast_type']);


include '../includes/header.php';
?>
<!DOCTYPE html>
<html lang="fa" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ثبت گزارش برای: <?= htmlspecialchars($work_order['title']) ?></title>
    <?php if ($toast_message): ?>
        <meta name="toast-message" content="<?= htmlspecialchars($toast_message) ?>">
        <meta name="toast-type" content="<?= htmlspecialchars($toast_type) ?>">
    <?php endif; ?>
    <style>
       input[type=number] {
            padding: 12px 0px;
            text-align: center;
        }
        select.form-control {
            padding: 12px 0px;
            width: auto;
        }
        span.select2.select2-container.select2-container--default {
            width: 100% !important;
        }
        :root {
            --primary-color: #007bff;
            --secondary-color: #6c757d;
            --danger-color: #dc3545;
            --success-color: #28a745;
            --light-bg: #f8f9fa;
            --border-color: #dee2e6;
            --text-color: #212529;
            --text-muted: #6c757d;
            --fs-2xl: 1.5rem; /* For h2 */
            --fs-md: 1rem; /* For h4 */
            --fs-sm: 0.9rem; /* For form-section-title and form-group label */
        }
        body { 
            font-family: 'Vazirmatn', sans-serif; 
            background-color: #f4f7f6; 
            line-height: 1.7;
            color: var(--text-color);
            margin: 0;
        }
        .container { 
            max-width: 960px; 
            margin: 1rem auto; 
            padding: 1rem;
        }
        .form-container {
            background-color: #fff; 
            border-radius: 8px; 
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            padding: 1.5rem;
        }
        .page-header { 
            margin-bottom: 2rem; 
            padding-bottom: 1rem; 
            border-bottom: 1px solid var(--border-color); 
        }
        .page-header h2 {
            margin: 0;
            font-size: var(--fs-2xl);
        }
        .page-header h4 {
            margin: 0.5rem 0 0;
            font-size: var(--fs-md);
            color: var(--text-muted);
            font-weight: normal;
            text-align: center;
        }
        .form-section {
            margin-bottom: 2rem;
        }
        .form-section-title {
            font-size: var(--fs-sm);
            font-weight: 600;
            margin-bottom: 1.5rem;
            padding-bottom: 0.5rem;
            border-bottom: 1px solid var(--border-color);
        }
        .form-grid { 
            display: grid; 
            grid-template-columns: 1fr; 
            gap: 1.5rem; 
        }
        .form-group { 
            margin-bottom: 1.5rem; 
        }
        .form-group label { 
            display: block; 
            margin-bottom: 0.5rem; 
            font-weight: 600; 
            font-size: var(--fs-sm);
        }
        .form-control { 
            width: 100%; 
            padding: 0.75rem; 
            border: 1px solid #ccc; 
            border-radius: 6px; 
            box-sizing: border-box; 
            transition: border-color 0.2s, box-shadow 0.2s;
        }
        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
            outline: none;
        }
        .dynamic-row {
            display: flex;
            gap: 10px;
            align-items: center;
            margin-bottom: 10px;
            position: relative;
            padding: 10px;
            padding-left: 40px; /* Space on the left for the button */
            background-color: var(--light-bg);
            border-radius: 6px;
        }
        .dynamic-row select, .dynamic-row input { flex: 1; }
        .dynamic-row input[name="part_name[]"] { flex: 2; }
        .remove-row-btn {
            position: absolute;
            top: -12px;
            right: -10px;
            width: 10px;
            height: auto;
            font-size: 1rem;
            background-color: var(--danger-color);
            color: white;
            border: none;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            line-height: 1;
            transition: background-color 0.2s;
        }
        .remove-row-btn:hover {
            background-color: #c82333;
        }
        .time-input-group { 
            display: flex; 
            flex-direction: column; 
            gap: 10px; 
        }
        .time-input-container { 
            display: flex; 
            align-items: center; 
            gap: 5px; 
            direction: ltr; 
        }
        .time-input-container input.form-control { 
            width: 70px; 
            text-align: center; 
        }
        .time-colon { 
            font-weight: bold; 
        }
        .btn { 
            padding: 0.75rem 1.5rem; 
            border-radius: 6px; 
            text-decoration: none; 
            font-weight: 600; 
            border: none; 
            cursor: pointer; 
            transition: opacity 0.2s;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }
        .btn:hover {
            opacity: 0.9;
        }
        .btn-primary { background-color: var(--primary-color); color: white; }
        .btn-secondary { background-color: var(--secondary-color); color: white; }
        .btn-sm { padding: 0.5rem 1rem; font-size: var(--fs-sm); }
        .form-actions {
            display: flex;
            justify-content: flex-end;
            gap: 1rem;
            margin-top: 2rem;
            padding-top: 1.5rem;
            border-top: 1px solid var(--border-color);
        }
        .message { 
            padding: 1rem; 
            margin-bottom: 1.5rem; 
            border-radius: 6px; 
            text-align: center;
        }
        .message.success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .message.error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        
        @media (min-width: 768px) {
            .form-container { padding: 2.5rem; }
            .container { margin: 2rem auto; padding: 2rem; }
            .form-grid { grid-template-columns: 1fr 1fr; }
            .time-input-group { flex-direction: row; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="form-container">
            <div class="page-header">
                <h2>ثبت گزارش تعمیر</h2>
                <h4>برای دستور کار: <?= htmlspecialchars($work_order['title']) ?></h4>
            </div>

            <form id="completion-report-form" method="POST" action="action_report.php?work_order_id=<?= $work_order_id ?>">
                <!-- CSRF Token -->
                <input type="hidden" name="csrf_token" value="<?= $csrf_token ?>">

                <!-- Outsource Section (if applicable) -->
                <?php if ($is_outsourced): ?>
                <div class="form-section" style="background:#f8f9fa; border:1px solid #eee; border-radius:8px; padding:1.5rem; margin-bottom:2rem;">
                    <h5 class="form-section-title">اطلاعات برون سپاری</h5>
                    <div style="display:flex; flex-wrap:wrap; gap:1rem;">
                        <div style="flex:1; min-width:160px;">
                            <label>تاریخ خروج از سازمان:</label>
                            <input type="text" name="exit_date" class="form-control persian-datepicker" value="<?= !empty($execution_data['exit_date']) ? to_shamsi($execution_data['exit_date'], 'Y/m/d') : '' ?>">
                        </div>
                        <div style="flex:1; min-width:160px;">
                            <label>مقصد خروج:</label>
                            <input type="text" name="exit_where" class="form-control" value="<?= htmlspecialchars($execution_data['exit_where'] ?? '') ?>">
                        </div>
                        <div style="flex:2; min-width:200px;">
                            <label>توضیحات خروج:</label>
                            <input type="text" name="exit_desc" class="form-control" value="<?= htmlspecialchars($execution_data['exit_desc'] ?? '') ?>">
                        </div>
                    </div>
                    <div style="display:flex; flex-wrap:wrap; gap:1rem; margin-top:1rem;">
                        <div style="flex:1; min-width:160px;">
                            <label>تاریخ ورود به سازمان:</label>
                            <input type="text" name="back_date" class="form-control persian-datepicker" value="<?= !empty($execution_data['back_date']) ? to_shamsi($execution_data['back_date'], 'Y/m/d') : '' ?>">
                        </div>
                        <div style="flex:2; min-width:200px;">
                            <label>توضیحات ورود:</label>
                            <input type="text" name="back_desc" class="form-control" value="<?= htmlspecialchars($execution_data['back_desc'] ?? '') ?>">
                        </div>
                        <div style="flex:1; min-width:120px;">
                            <label>مبلغ فاکتور (تومان):</label>
                            <input type="text" name="back_pay" class="form-control" value="<?= !empty($execution_data['back_pay']) ? number_format($execution_data['back_pay']) : '' ?>">
                        </div>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Main Report Section -->
                <div class="form-section">
                    <h5 class="form-section-title">زمانبندی انجام کار</h5>
                    <div class="form-grid">
                        <div class="form-group">
                            <label>تاریخ و ساعت شروع کار</label>
                            <div class="time-input-group">
                                <input type="text" name="start_date" id="start_date" class="form-control persian-datepicker" placeholder="تاریخ شروع" value="<?= !empty($execution_data['actual_start_datetime']) ? to_shamsi($execution_data['actual_start_datetime'], 'Y/m/d') : '' ?>">
                                <div class="time-input-container">
                                    <input type="text" id="start_hour" placeholder="ساعت" maxlength="2" class="form-control" value="<?= !empty($execution_data['actual_start_datetime']) ? (new DateTime($execution_data['actual_start_datetime']))->format('H') : '' ?>">
                                    <span class="time-colon">:</span>
                                    <input type="text" id="start_minute" placeholder="دقیقه" maxlength="2" class="form-control" value="<?= !empty($execution_data['actual_start_datetime']) ? (new DateTime($execution_data['actual_start_datetime']))->format('i') : '' ?>">
                                    <input type="hidden" name="start_time" id="start_time">
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label>تاریخ و ساعت پایان کار</label>
                            <div class="time-input-group">
                                <input type="text" name="end_date" id="end_date" class="form-control persian-datepicker" placeholder="تاریخ پایان" value="<?= !empty($execution_data['actual_end_datetime']) ? to_shamsi($execution_data['actual_end_datetime'], 'Y/m/d') : '' ?>">
                                <div class="time-input-container">
                                    <input type="text" id="end_hour" placeholder="ساعت" maxlength="2" class="form-control" value="<?= !empty($execution_data['actual_end_datetime']) ? (new DateTime($execution_data['actual_end_datetime']))->format('H') : '' ?>">
                                    <span class="time-colon">:</span>
                                    <input type="text" id="end_minute" placeholder="دقیقه" maxlength="2" class="form-control" value="<?= !empty($execution_data['actual_end_datetime']) ? (new DateTime($execution_data['actual_end_datetime']))->format('i') : '' ?>">
                                    <input type="hidden" name="end_time" id="end_time">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="form-section">
                    <h5 class="form-section-title">جزئیات گزارش</h5>
                    <div class="form-group">
                        <label for="completion_notes">شرح اقدامات انجام شده</label>
                        <textarea name="completion_notes" id="completion_notes" rows="5" class="form-control" required><?= htmlspecialchars($execution_data['completion_notes'] ?? '') ?></textarea>
                    </div>
                    
                    <div class="form-group">
                        <label for="delay_reason">دلیل تاخیر (در صورت وجود)</label>
                        <textarea name="delay_reason" id="delay_reason" rows="3" class="form-control"><?= htmlspecialchars($execution_data['delay_reason'] ?? '') ?></textarea>
                    </div>
                </div>

                <div class="form-section">
                    <h5 class="form-section-title">نفرات انجام دهنده</h5>
                    <div class="form-group">
                        <div id="labor-container"></div>
                        <button type="button" id="add-labor-btn" class="btn btn-secondary btn-sm mt-2"><i class="fas fa-plus"></i> افزودن نفر</button>
                    </div>
                </div>

                <div class="form-section">
                    <h5 class="form-section-title">قطعات مصرفی</h5>
                    <div class="form-group">
                        <div id="parts-container"></div>
                        <button type="button" id="add-part-btn" class="btn btn-secondary btn-sm mt-2"><i class="fas fa-plus"></i> افزودن قطعه</button>
                    </div>
                </div>
                
                <div class="form-section">
                    <h5 class="form-section-title">تایید نهایی</h5>
                    <div class="form-group">
                        <label for="verifier_id">تحویل به (جهت تایید)</label>
                        <select name="verifier_id" id="verifier_id" class="form-control" required>
                            <option value="" disabled selected>یک نفر را انتخاب کنید...</option>
                            <?php foreach ($users as $user): ?>
                                <option value="<?= $user['id'] ?>"><?= htmlspecialchars($user['name']) ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>

                <div class="form-actions">
                    <a href="my_tasks.php" class="btn btn-secondary">انصراف</a>
                    <button type="submit" class="btn btn-primary"><i class="fas fa-check"></i> ثبت و ارسال برای تایید</button>
                </div>
            </form>
        </div>
    </div>

    <?php include '../includes/footer.php'; ?>
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        const usersForSelect = <?= json_encode($users) ?>;
        const laborData = <?= json_encode($labor_data) ?>;
        const partsData = <?= json_encode($parts_data) ?>;
        const laborContainer = document.getElementById('labor-container');
        const partsContainer = document.getElementById('parts-container');
        const isOutsourced = <?= json_encode($is_outsourced) ?>;
        
        // Function to add a fixed "Outsourced" row
        function addOutsourcedLaborRow() {
            const newRow = document.createElement('div');
            newRow.className = 'dynamic-row';
            // Add a hidden input to send 'outsource' value to the server for logic, but it won't be inserted
            // Use a disabled text input for display purposes only
            newRow.innerHTML = `
                <input type="hidden" name="personnel_id[]" value="outsource">
                <input type="text" class="form-control" value="برون سپاری" disabled>
            `; // No remove button for this row
            laborContainer.appendChild(newRow);
        }

        // Function to create a user selection dropdown
        function createLaborSelect(selectedUserId = '') {
            const selectEl = document.createElement('select');
            selectEl.name = 'personnel_id[]';
            selectEl.className = 'form-control';
            let optionsHtml = '<option value="" disabled ' + (selectedUserId ? '' : 'selected') + '>انتخاب کنید...</option>';
            optionsHtml += usersForSelect.map(user => `<option value="${user.id}" ${user.id == selectedUserId ? 'selected' : ''}>${user.name}</option>`).join('');
            selectEl.innerHTML = optionsHtml;
            return selectEl;
        }

        // Function to add a new selectable labor row
        function addLaborRow(selectedUserId = '') {
            const newRow = document.createElement('div');
            newRow.className = 'dynamic-row';
            
            const selectEl = createLaborSelect(selectedUserId);
            newRow.appendChild(selectEl);

            const removeBtn = document.createElement('button');
            removeBtn.type = 'button';
            removeBtn.className = 'remove-row-btn';
            removeBtn.innerHTML = '&times;';
            newRow.appendChild(removeBtn);
            
            laborContainer.appendChild(newRow);
        }

        // Function to add a new part row
        function addPartRow(name = '', quantity = 1, unit = 'عدد') {
            const newRow = document.createElement('div');
            newRow.className = 'dynamic-row';
            newRow.innerHTML = `
                <input type="text" name="part_name[]" class="form-control" placeholder="نام قطعه" value="${name}" required>
                <input type="number" name="part_quantity[]" class="form-control" placeholder="تعداد" value="${quantity}" min="1" style="flex: 0.5;">
                <input type="text" name="part_unit[]" class="form-control" placeholder="واحد" value="${unit}" style="flex: 0.5;">
                <button type="button" class="remove-row-btn">&times;</button>
            `;
            partsContainer.appendChild(newRow);
        }

        // --- Initial Population ---

        // Handle Labor section
        if (isOutsourced) {
            addOutsourcedLaborRow();
        }
        if (laborData.length > 0) {
            laborData.forEach(userId => addLaborRow(userId));
        } else if (!isOutsourced) {
            addLaborRow(); // If not outsourced and no data, add one empty row
        }
        
        // Handle Parts section
        if (partsData.length > 0) {
            partsData.forEach(part => addPartRow(part.part_name, part.quantity_used, part.unit));
        } else {
            addPartRow(); // Add one empty row by default
        }

        // --- Event Listeners ---

        // Add/Remove Parts
        document.getElementById('add-part-btn').addEventListener('click', () => addPartRow());
        
        // Add Labor
        document.getElementById('add-labor-btn').addEventListener('click', () => addLaborRow());

        // Remove Rows (delegated for both labor and parts)
        document.getElementById('completion-report-form').addEventListener('click', e => { 
            if(e.target.classList.contains('remove-row-btn')) {
                e.target.parentElement.remove();
            }
        });

        // Time input combination logic
        function combineTime(hourEl, minuteEl, targetHiddenEl) {
            const hour = hourEl.value.padStart(2, '0');
            const minute = minuteEl.value.padStart(2, '0');
            if (hour && minute) {
                targetHiddenEl.value = `${hour}:${minute}:00`;
            }
        }
        
        const startHour = document.getElementById('start_hour');
        const startMinute = document.getElementById('start_minute');
        const startTime = document.getElementById('start_time');
        const endHour = document.getElementById('end_hour');
        const endMinute = document.getElementById('end_minute');
        const endTime = document.getElementById('end_time');

        [startHour, startMinute, endHour, endMinute].forEach(el => {
            el.addEventListener('input', () => {
                combineTime(startHour, startMinute, startTime);
                combineTime(endHour, endMinute, endTime);
            });
        });
        
        // Initial combination on load
        combineTime(startHour, startMinute, startTime);
        combineTime(endHour, endMinute, endTime);

        // Format invoice amount with thousand separators
        const backPayInput = document.querySelector('input[name="back_pay"]');
        if (backPayInput) {
            backPayInput.addEventListener('input', function(e) {
                let value = e.target.value.replace(/,/g, '');
                if (!isNaN(value) && value.length > 0) {
                    e.target.value = parseInt(value, 10).toLocaleString('en-US');
                } else {
                    e.target.value = '';
                }
            });
        }

        // Initialize date pickers with project standard
        initializeDatepicker('.persian-datepicker');
    });
    </script>
</body>
</html>
