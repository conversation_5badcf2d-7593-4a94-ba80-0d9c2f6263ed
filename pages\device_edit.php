<?php
require_once '../includes/auth.php';
require_once '../db_connection.php';
require_once '../includes/date_helper.php';

// Check access permission
require_page_access('devices', 'edit');

$pdo = db_connect();
$device_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if (!$device_id) {
    header("Location: devices.php");
    exit;
}

// Handle form submission for update
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'edit_device') {
    try {
        $pdo->beginTransaction();

        // Handle image updates
        $stmt = $pdo->prepare("SELECT images FROM devices WHERE id = ?");
        $stmt->execute([$device_id]);
        $current_images = json_decode($stmt->fetchColumn() ?: '[]', true);
        if (!is_array($current_images)) {
            $current_images = [];
        }

        $existing_files = $_POST['existing_files'] ?? [];
        
        $images_to_delete = array_diff($current_images, $existing_files);
        foreach ($images_to_delete as $img) {
            if (file_exists("../uploads/devices/" . $img)) {
                unlink("../uploads/devices/" . $img);
            }
        }

        $new_uploaded_files = [];
        if (isset($_FILES['images']) && !empty($_FILES['images']['name'][0])) {
            $file_count = count($_FILES['images']['name']);
            $targetDir = "../uploads/devices/";
            for ($i = 0; $i < $file_count; $i++) {
                if ($_FILES['images']['error'][$i] === UPLOAD_ERR_OK) {
                    $filename = uniqid() . "_" . basename($_FILES['images']['name'][$i]);
                    if (move_uploaded_file($_FILES['images']['tmp_name'][$i], $targetDir . $filename)) {
                        $new_uploaded_files[] = $filename;
                    }
                }
            }
        }
        
        $final_images = array_merge($existing_files, $new_uploaded_files);
        $images_json = json_encode($final_images);

        // Convert dates from Shamsi to Miladi
        $purchase_date = !empty($_POST['purchase_date']) ? to_miladi($_POST['purchase_date']) : null;
        $installation_date = !empty($_POST['installation_date']) ? to_miladi($_POST['installation_date']) : null;
        $operation_date = !empty($_POST['operation_date']) ? to_miladi($_POST['operation_date']) : null;
        $manufacture_year = !empty($_POST['manufacture_year']) ? (int)$_POST['manufacture_year'] : null;

        // Update device main info
        $sql = "UPDATE devices SET 
                    name = ?, serial_number = ?, location = ?, status = ?, 
                    purchase_date = ?, vendor_name = ?, vendor_phone = ?, 
                    manufacture_year = ?, installation_date = ?, operation_date = ?, 
                    description = ?, images = ?, updated_at = NOW() 
                WHERE id = ?";
        $stmt = $pdo->prepare($sql);
        $stmt->execute([
            $_POST['name'], $_POST['serial_number'], $_POST['location'], $_POST['status'],
            $purchase_date, $_POST['vendor_name'], $_POST['vendor_phone'],
            $manufacture_year, $installation_date, $operation_date,
            $_POST['description'], $images_json, $device_id
        ]);

        // Update parts (delete and re-insert)
        $stmt = $pdo->prepare("DELETE FROM device_parts WHERE device_id = ?");
        $stmt->execute([$device_id]);
        if (!empty($_POST['consumable_name']) && is_array($_POST['consumable_name'])) {
            $stmtPart = $pdo->prepare("INSERT INTO device_parts (device_id, part_name, quantity, unit, description, created_at) VALUES (?, ?, ?, ?, ?, NOW())");
            foreach ($_POST['consumable_name'] as $index => $partName) {
                if (trim($partName) !== '') {
                    $quantity = isset($_POST['consumable_quantity'][$index]) ? (int)$_POST['consumable_quantity'][$index] : 1;
                    $unit = trim($_POST['consumable_unit'][$index] ?? 'عدد');
                    $desc = $_POST['consumable_description'][$index] ?? null;
                    $stmtPart->execute([$device_id, $partName, $quantity, $unit, $desc]);
                }
            }
        }

        // Update motors (delete and re-insert)
        $stmt = $pdo->prepare("DELETE FROM device_motors WHERE device_id = ?");
        $stmt->execute([$device_id]);
        if (!empty($_POST['motor_name']) && is_array($_POST['motor_name'])) {
            $stmtMotor = $pdo->prepare("INSERT INTO device_motors (device_id, motor_name, voltage, power, current, rpm, description, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, NOW())");
            foreach ($_POST['motor_name'] as $index => $motorName) {
                if (trim($motorName) !== '') {
                    $voltage = $_POST['motor_voltage'][$index] ?? null;
                    $power = $_POST['motor_power'][$index] ?? null;
                    $current = $_POST['motor_current'][$index] ?? null;
                    $rpm = $_POST['motor_rpm'][$index] ?? null;
                    $desc = $_POST['motor_description'][$index] ?? null;
                    $stmtMotor->execute([$device_id, $motorName, $voltage, $power, $current, $rpm, $desc]);
                }
            }
        }

        $pdo->commit();
        $_SESSION['toast_message'] = 'دستگاه با موفقیت به‌روزرسانی شد.';
        $_SESSION['toast_type'] = 'success';
        header('Location: devices.php');
        exit;

    } catch (PDOException $e) {
        $pdo->rollBack();
        $_SESSION['toast_message'] = 'خطا در پایگاه داده: ' . $e->getMessage();
        $_SESSION['toast_type'] = 'danger';
        header("Location: device_edit.php?id=" . $device_id);
        exit;
    }
}

// Fetch existing device data
$stmt = $pdo->prepare("SELECT * FROM devices WHERE id = ?");
$stmt->execute([$device_id]);
$device = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$device) {
    echo "دستگاه یافت نشد.";
    exit;
}

// Fetch related data
$locations = $pdo->query("SELECT id, location_name FROM locations ORDER BY location_name")->fetchAll(PDO::FETCH_ASSOC);
$stmtParts = $pdo->prepare("SELECT * FROM device_parts WHERE device_id = ?");
$stmtParts->execute([$device_id]);
$parts = $stmtParts->fetchAll(PDO::FETCH_ASSOC);

$stmtMotors = $pdo->prepare("SELECT * FROM device_motors WHERE device_id = ?");
$stmtMotors->execute([$device_id]);
$motors = $stmtMotors->fetchAll(PDO::FETCH_ASSOC);

$device_images = json_decode($device['images'] ?? '[]', true);
if (!is_array($device_images)) {
    $device_images = [];
}

include '../includes/header.php';
?>
<head>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="../assets/css/persian-datepicker.min.css">
    <script src="../assets/js/jquery.min.js"></script>
    <script src="../assets/js/persian-date.min.js"></script>
    <script src="../assets/js/persian-datepicker.min.js"></script>
    <style>
        * { box-sizing: border-box; }
        body { overflow-x: hidden; }
        .add-device-container { margin: 0 auto; padding: 20px; }
        .form-row { margin-bottom: 15px; }
        .form-row:last-child { margin-bottom: 0; }
        .form-row label { display: block; margin-bottom: 5px; font-weight: bold; }
        .form-row input, .form-row select, .form-row textarea { width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px; font-size: 14px; }
        .form-row textarea { min-height: 100px; resize: vertical; }
        .form-row-multi { display: flex; gap: 15px; margin-bottom: 15px; }
        .form-row-multi .form-field { flex: 1; }
        .form-row-multi label { display: block; margin-bottom: 5px; font-weight: bold; }
        .form-row-multi input, .form-row-multi select { width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px; font-size: 14px; }
        #consumableFieldset, #motorFieldset { border: 1px solid #ddd; padding: 15px; border-radius: 5px; background-color: #f9f9f9; }
        .consumable-row, .motor-row { display: flex; flex-wrap: wrap; gap: 10px; margin-bottom: 15px; align-items: flex-end; padding: 15px; border: 1px solid #e0e0e0; border-radius: 8px; background-color: #fafafa; }
        .consumable-field, .motor-field { flex: 1; min-width: 120px; }
        .consumable-field label, .motor-field label { display: block; margin-bottom: 5px; font-weight: bold; font-size: 12px; color: #555; }
        .consumable-field input, .motor-field input { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; font-size: 14px; }
        .consumable-row button, .motor-row button { background-color: #dc3545; color: white; border: none; border-radius: 50%; width: 30px; height: 30px; cursor: pointer; font-weight: bold; display: flex; align-items: center; justify-content: center; flex-shrink: 0; }
        .add-consumable-btn, .add-motor-btn { background-color: #28a745; color: white; border: none; padding: 8px 16px; border-radius: 5px; cursor: pointer; font-size: 14px; }
        .submit-btn { background-color: #007bff; color: white; border: none; padding: 12px 24px; border-radius: 5px; cursor: pointer; font-size: 16px; font-weight: bold; width: 100%; margin-top: 20px; }
        .back-btn { background-color: #6c757d; color: white; border: none; padding: 8px 16px; border-radius: 5px; cursor: pointer; text-decoration: none; display: inline-block; margin-bottom: 20px; }
        .page-title { text-align: center; margin-bottom: 30px; color: #333; }
        @media (max-width: 768px) { .form-row-multi, .consumable-row, .motor-row { flex-direction: column; gap: 10px; } }
    </style>
</head>
<body>
<div class="add-device-container">
    <a href="devices.php" class="back-btn"><i class="fas fa-arrow-right"></i> بازگشت به لیست</a>
    <h2 class="page-title">ویرایش دستگاه</h2>
    <form method="post" enctype="multipart/form-data" id="editDeviceForm">
        <input type="hidden" name="action" value="edit_device">
        <div class="form-row-multi">
            <div class="form-field">
                <label for="name">نام دستگاه:</label>
                <input type="text" id="name" name="name" required value="<?= htmlspecialchars($device['name']) ?>">
            </div>
            <div class="form-field">
                <label for="serial_number">شماره سریال:</label>
                <input type="text" id="serial_number" name="serial_number" required value="<?= htmlspecialchars($device['serial_number']) ?>">
            </div>
        </div>
        <div class="form-row-multi">
            <div class="form-field">
                <label for="location">محل استفاده:</label>
                <select name="location" id="location" required>
                    <option value="">انتخاب محل</option>
                    <?php foreach ($locations as $loc): ?>
                        <option value="<?= $loc['id'] ?>" <?= ($device['location'] == $loc['id']) ? 'selected' : '' ?>><?= htmlspecialchars($loc['location_name']) ?></option>
                    <?php endforeach; ?>
                </select>
            </div>
            <div class="form-field">
                <label for="status">وضعیت:</label>
                <select name="status" id="status" required>
                    <option value="فعال" <?= ($device['status'] == 'فعال') ? 'selected' : '' ?>>فعال</option>
                    <option value="غیرفعال" <?= ($device['status'] == 'غیرفعال') ? 'selected' : '' ?>>غیرفعال</option>
                    <option value="در حال تعمیر" <?= ($device['status'] == 'در حال تعمیر') ? 'selected' : '' ?>>در حال تعمیر</option>
                </select>
            </div>
        </div>
        <div class="form-row-multi">
            <div class="form-field">
                <label for="purchase_date">تاریخ خرید:</label>
                <input type="text" class="persian-datepicker" name="purchase_date" value="<?= !empty($device['purchase_date']) ? to_shamsi($device['purchase_date']) : '' ?>">
            </div>
            <div class="form-field">
                <label for="vendor_name">فروشنده:</label>
                <input type="text" name="vendor_name" value="<?= htmlspecialchars($device['vendor_name']) ?>">
            </div>
            <div class="form-field">
                <label for="vendor_phone">شماره تماس فروشنده:</label>
                <input type="tel" name="vendor_phone" value="<?= htmlspecialchars($device['vendor_phone']) ?>">
            </div>
        </div>
        <div class="form-row-multi">
            <div class="form-field">
                <label for="manufacture_year">سال ساخت:</label>
                <input type="number" name="manufacture_year" value="<?= htmlspecialchars($device['manufacture_year']) ?>">
            </div>
            <div class="form-field">
                <label for="installation_date">تاریخ نصب:</label>
                <input type="text" class="persian-datepicker" name="installation_date" value="<?= !empty($device['installation_date']) ? to_shamsi($device['installation_date']) : '' ?>">
            </div>
            <div class="form-field">
                <label for="operation_date">تاریخ بهره‌برداری:</label>
                <input type="text" class="persian-datepicker" name="operation_date" value="<?= !empty($device['operation_date']) ? to_shamsi($device['operation_date']) : '' ?>">
            </div>
        </div>
        <div class="form-row">
            <label>قطعات مصرفی:</label>
            <fieldset id="consumableFieldset">
                <div id="consumablesContainer">
                    <?php foreach ($parts as $part): ?>
                    <div class="consumable-row">
                        <div class="consumable-field"><label>نام قطعه:</label><input type="text" name="consumable_name[]" placeholder="نام قطعه" required value="<?= htmlspecialchars($part['part_name']) ?>"></div>
                        <div class="consumable-field"><label>تعداد:</label><input type="number" name="consumable_quantity[]" placeholder="تعداد" min="1" required value="<?= htmlspecialchars($part['quantity']) ?>"></div>
                        <div class="consumable-field"><label>واحد:</label><input type="text" name="consumable_unit[]" placeholder="واحد" required value="<?= htmlspecialchars($part['unit']) ?>"></div>
                        <div class="consumable-field"><label>توضیحات:</label><input type="text" name="consumable_description[]" value="<?= htmlspecialchars($part['description']) ?>"></div>
                        <button type="button" onclick="removeConsumableRow(this)">×</button>
                    </div>
                    <?php endforeach; ?>
                </div>
                <button type="button" onclick="addConsumableRow()" class="add-consumable-btn"><i class="fas fa-plus"></i> افزودن قطعه</button>
            </fieldset>
        </div>
        <div class="form-row">
            <label>مشخصات الکتروموتورها:</label>
            <fieldset id="motorFieldset">
                <div id="motorsContainer">
                    <?php foreach ($motors as $motor): ?>
                    <div class="motor-row">
                        <div class="motor-field"><label>نام:</label><input type="text" name="motor_name[]" placeholder="نام" required value="<?= htmlspecialchars($motor['motor_name']) ?>"></div>
                        <div class="motor-field"><label>ولتاژ:</label><input type="text" name="motor_voltage[]" placeholder="ولتاژ" value="<?= htmlspecialchars($motor['voltage']) ?>"></div>
                        <div class="motor-field"><label>توان:</label><input type="text" name="motor_power[]" placeholder="توان" value="<?= htmlspecialchars($motor['power']) ?>"></div>
                        <div class="motor-field"><label>جریان:</label><input type="text" name="motor_current[]" placeholder="جریان" value="<?= htmlspecialchars($motor['current']) ?>"></div>
                        <div class="motor-field"><label>RPM:</label><input type="text" name="motor_rpm[]" placeholder="RPM" value="<?= htmlspecialchars($motor['rpm']) ?>"></div>
                        <div class="motor-field"><label>توضیحات:</label><input type="text" name="motor_description[]" placeholder="توضیحات" value="<?= htmlspecialchars($motor['description']) ?>"></div>
                        <button type="button" onclick="removeMotorRow(this)">×</button>
                    </div>
                    <?php endforeach; ?>
                </div>
                <button type="button" onclick="addMotorRow()" class="add-motor-btn"><i class="fas fa-plus"></i> افزودن مشخصات</button>
            </fieldset>
        </div>
        <div class="form-row">
            <?php
            require_once '../includes/file_uploader.php';
            $existing_files_for_uploader = [];
            foreach ($device_images as $img) {
                $existing_files_for_uploader[] = ['name' => $img, 'path' => '../uploads/devices/' . $img, 'size' => file_exists('../uploads/devices/' . $img) ? filesize('../uploads/devices/' . $img) : 0];
            }
            echo render_file_uploader([
                'id' => 'deviceImages', 'name' => 'images[]', 'label' => 'تصاویر دستگاه',
                'existing_files' => $existing_files_for_uploader, 'show_existing' => true
            ]);
            ?>
        </div>
        <div class="form-row">
            <label for="description">توضیحات:</label>
            <textarea name="description" id="description"><?= htmlspecialchars($device['description']) ?></textarea>
        </div>
        <button type="submit" class="submit-btn">ذخیره تغییرات</button>
    </form>
</div>
<?php include '../includes/footer.php'; ?>
<script>
function addConsumableRow() {
    const container = document.getElementById('consumablesContainer');
    const div = document.createElement('div');
    div.className = 'consumable-row';
    div.innerHTML = '<div class="consumable-field"><label>نام قطعه:</label><input type="text" name="consumable_name[]" placeholder="نام قطعه" required></div><div class="consumable-field"><label>تعداد:</label><input type="number" name="consumable_quantity[]" placeholder="تعداد" min="1" value="1" required></div><div class="consumable-field"><label>واحد:</label><input type="text" name="consumable_unit[]" placeholder="واحد شمارش" value="عدد" required></div><div class="consumable-field"><label>توضیحات:</label><input type="text" name="consumable_description[]" ></div><button type="button" onclick="removeConsumableRow(this)">×</button>';
    container.appendChild(div);
}
function removeConsumableRow(button) { button.parentElement.remove(); }
function addMotorRow() {
    const container = document.getElementById('motorsContainer');
    const div = document.createElement('div');
    div.className = 'motor-row';
    div.innerHTML = '<div class="motor-field"><label>نام:</label><input type="text" name="motor_name[]" placeholder="نام" required></div><div class="motor-field"><label>ولتاژ:</label><input type="text" name="motor_voltage[]" placeholder="ولتاژ"></div><div class="motor-field"><label>توان:</label><input type="text" name="motor_power[]" placeholder="توان"></div><div class="motor-field"><label>جریان:</label><input type="text" name="motor_current[]" placeholder="جریان"></div><div class="motor-field"><label>RPM:</label><input type="text" name="motor_rpm[]" placeholder="RPM"></div><div class="motor-field"><label>توضیحات:</label><input type="text" name="motor_description[]" placeholder="توضیحات"></div><button type="button" onclick="removeMotorRow(this)">×</button>';
    container.appendChild(div);
}
function removeMotorRow(button) { button.parentElement.remove(); }
$(document).ready(function() {
    initializeDatepicker('.persian-datepicker');
});
</script>
</body>
</html>
