<?php
/**
 * Helper functions for user management
 * این فایل شامل توابع کمکی برای مدیریت کاربران است
 */

/**
 * دریافت لیست کاربران بدون کاربر "برون سپاری"
 * این تابع کاربر برون سپاری را از لیست حذف می‌کند مگر اینکه صراحتاً درخواست شود
 * 
 * @param PDO $pdo اتصال به دیتابیس
 * @param bool $include_outsource آیا کاربر برون سپاری شامل شود؟ (پیش‌فرض: false)
 * @param string $order_by ترتیب مرتب‌سازی (پیش‌فرض: name)
 * @return array لیست کاربران
 */
function getUsersForSelect($pdo, $include_outsource = false, $order_by = 'name') {
    $sql = "SELECT id, name, username, role FROM users";
    
    if (!$include_outsource) {
        $sql .= " WHERE id != 99999 AND username != 'outsource' AND name != 'برون سپاری'";
    }
    
    $sql .= " ORDER BY " . $order_by;
    
    return $pdo->query($sql)->fetchAll(PDO::FETCH_ASSOC);
}

/**
 * دریافت لیست کاربران برای نمایش در فیلدهای select
 * این تابع کاربر برون سپاری را حذف می‌کند مگر اینکه در حال حاضر انتخاب شده باشد
 * 
 * @param PDO $pdo اتصال به دیتابیس
 * @param int|array $selected_user_ids شناسه کاربران انتخاب شده
 * @param string $order_by ترتیب مرتب‌سازی
 * @return array لیست کاربران
 */
function getUsersForSelectWithSelected($pdo, $selected_user_ids = null, $order_by = 'name') {
    // تبدیل به آرایه اگر یک عدد باشد
    if ($selected_user_ids !== null && !is_array($selected_user_ids)) {
        $selected_user_ids = [$selected_user_ids];
    }
    
    // بررسی اینکه آیا کاربر برون سپاری در لیست انتخاب شده‌ها هست
    $include_outsource = false;
    if ($selected_user_ids !== null) {
        $include_outsource = in_array(99999, $selected_user_ids) || 
                           in_array('99999', $selected_user_ids);
    }
    
    return getUsersForSelect($pdo, $include_outsource, $order_by);
}

/**
 * بررسی اینکه آیا کاربر فعلی مجاز به دیدن کاربر برون سپاری است
 * فقط مدیران سیستم مجاز به دیدن و مدیریت کاربر برون سپاری هستند
 * 
 * @return bool
 */
function canSeeOutsourceUser() {
    return function_exists('is_admin') && is_admin();
}

/**
 * دریافت اطلاعات کاربر برون سپاری
 * 
 * @param PDO $pdo اتصال به دیتابیس
 * @return array|null اطلاعات کاربر برون سپاری یا null اگر یافت نشود
 */
function getOutsourceUser($pdo) {
    $stmt = $pdo->prepare("SELECT id, name, username, role FROM users WHERE id = 99999 OR username = 'outsource' LIMIT 1");
    $stmt->execute();
    return $stmt->fetch(PDO::FETCH_ASSOC);
}

/**
 * بررسی اینکه آیا یک کاربر، کاربر برون سپاری است
 * 
 * @param int|string $user_id شناسه کاربر
 * @return bool
 */
function isOutsourceUser($user_id) {
    return $user_id == 99999 || $user_id === '99999';
}

/**
 * دریافت نام کاربر با در نظر گیری کاربر برون سپاری
 * 
 * @param PDO $pdo اتصال به دیتابیس
 * @param int $user_id شناسه کاربر
 * @return string نام کاربر
 */
function getUserName($pdo, $user_id) {
    if (isOutsourceUser($user_id)) {
        return 'برون سپاری';
    }
    
    $stmt = $pdo->prepare("SELECT name FROM users WHERE id = ?");
    $stmt->execute([$user_id]);
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    
    return $result ? $result['name'] : 'کاربر نامشخص';
}

// تابع دریافت اطلاعات کاربر
function get_user_info($user_id) {
    global $pdo;

    if (!$user_id) {
        return null;
    }

    $stmt = $pdo->prepare("SELECT * FROM users WHERE id = ?");
    $stmt->execute([$user_id]);
    return $stmt->fetch(PDO::FETCH_ASSOC);
}
?>
