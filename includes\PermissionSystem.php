<?php

/**
 * سیستم مدیریت مجوزها و کنترل دسترسی (RBAC)
 * 
 * این کلاس مسئول مدیریت مجوزهای کاربران و کنترل دسترسی به صفحات و عملیات مختلف است
 */
class PermissionSystem {
    private $pdo;
    private $user_permissions = null;
    private $user_pages = null;
    
    public function __construct($pdo) {
        $this->pdo = $pdo;
    }
    
    /**
     * بررسی اینکه آیا کاربر جاری مجوز خاصی دارد یا نه
     * 
     * @param string $page_name نام صفحه
     * @param string $permission_name نام مجوز (view, create, edit, delete, etc.)
     * @return bool
     */
    public function has_permission($page_name, $permission_name) {
        if (!isset($_SESSION['user']) || !isset($_SESSION['user']['id'])) {
            return false;
        }
        
        // اگر کاربر غیرفعال است، هیچ مجوزی ندارد
        if (!$this->is_user_active($_SESSION['user']['id'])) {
            return false;
        }
        
        // بارگذاری مجوزهای کاربر اگر هنوز بارگذاری نشده
        if ($this->user_permissions === null) {
            $this->load_user_permissions($_SESSION['user']['id']);
        }
        
        // بررسی وجود مجوز
        return isset($this->user_permissions[$page_name][$permission_name]);
    }
    
    /**
     * بررسی دسترسی به صفحه و در صورت عدم دسترسی، خروج از اسکریپت
     * 
     * @param string $page_name نام صفحه
     * @param string $permission_name نام مجوز (پیش‌فرض: view)
     * @param string $redirect_url آدرس هدایت در صورت عدم دسترسی
     */
    public function require_page_access($page_name, $permission_name = 'view', $redirect_url = '../pages/dashboard.php') {
        if (!$this->has_permission($page_name, $permission_name)) {
            $_SESSION['toast_message'] = 'شما مجوز دسترسی به این بخش را ندارید.';
            $_SESSION['toast_type'] = 'danger';
            header("Location: $redirect_url");
            exit;
        }
    }
    
    /**
     * دریافت لیست صفحاتی که کاربر به آنها دسترسی دارد
     * 
     * @param int|null $user_id شناسه کاربر (اگر null باشد، کاربر جاری استفاده می‌شود)
     * @return array
     */
    public function get_accessible_pages($user_id = null) {
        if ($user_id === null) {
            $user_id = $_SESSION['user']['id'] ?? null;
        }
        
        if (!$user_id || !$this->is_user_active($user_id)) {
            return [];
        }
        
        // بارگذاری صفحات قابل دسترس کاربر اگر هنوز بارگذاری نشده
        if ($this->user_pages === null) {
            $this->load_user_pages($user_id);
        }
        
        return $this->user_pages;
    }
    
    /**
     * بررسی فعال بودن کاربر
     * 
     * @param int $user_id
     * @return bool
     */
    public function is_user_active($user_id) {
        $stmt = $this->pdo->prepare("SELECT is_active FROM users WHERE id = ?");
        $stmt->execute([$user_id]);
        $result = $stmt->fetchColumn();
        
        return $result === true || $result === 1 || $result === '1';
    }
    
    /**
     * بارگذاری مجوزهای کاربر از دیتابیس
     * 
     * @param int $user_id
     */
    private function load_user_permissions($user_id) {
        $this->user_permissions = [];
        
        $stmt = $this->pdo->prepare("
            SELECT sp.page_name as page_name, p.permission_name as permission_name
            FROM users u
            JOIN roles r ON u.role_id = r.id
            JOIN role_permissions rp ON r.id = rp.role_id
            JOIN permissions p ON rp.permission_id = p.id
            JOIN system_pages sp ON p.page_id = sp.id
            WHERE u.id = ? AND u.is_active = 1 AND r.is_active = 1 AND sp.is_active = 1
        ");
        
        $stmt->execute([$user_id]);
        $permissions = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        foreach ($permissions as $perm) {
            $this->user_permissions[$perm['page_name']][$perm['permission_name']] = true;
        }
    }
    
    /**
     * بارگذاری صفحات قابل دسترس کاربر از دیتابیس
     * 
     * @param int $user_id
     */
    private function load_user_pages($user_id) {
        $this->user_pages = [];
        
        $stmt = $this->pdo->prepare("
            SELECT DISTINCT sp.page_name as page_name, sp.display_name, sp.file_path, sp.icon
            FROM users u
            JOIN roles r ON u.role_id = r.id
            JOIN role_permissions rp ON r.id = rp.role_id
            JOIN permissions p ON rp.permission_id = p.id
            JOIN system_pages sp ON p.page_id = sp.id
            WHERE u.id = ? AND u.is_active = 1 AND r.is_active = 1 AND sp.is_active = 1
            AND p.permission_name = 'view'
            ORDER BY sp.display_name
        ");
        
        $stmt->execute([$user_id]);
        $this->user_pages = $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    /**
     * دریافت تمام نقش‌های فعال
     * 
     * @return array
     */
    public function get_active_roles() {
        $stmt = $this->pdo->prepare("SELECT * FROM roles WHERE is_active = 1 ORDER BY display_name");
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    /**
     * دریافت تمام صفحات فعال سیستم
     * 
     * @return array
     */
    public function get_active_pages() {
        $stmt = $this->pdo->prepare("SELECT * FROM system_pages WHERE is_active = 1 ORDER BY display_name");
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    /**
     * دریافت مجوزهای یک صفحه
     * 
     * @param int $page_id
     * @return array
     */
    public function get_page_permissions($page_id) {
        $stmt = $this->pdo->prepare("SELECT * FROM permissions WHERE page_id = ? ORDER BY display_name");
        $stmt->execute([$page_id]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    /**
     * دریافت مجوزهای یک نقش
     * 
     * @param int $role_id
     * @return array
     */
    public function get_role_permissions($role_id) {
        $stmt = $this->pdo->prepare("
            SELECT p.id, p.page_id, p.permission_name as permission_name, p.display_name, sp.page_name as page_name, sp.display_name as page_display_name
            FROM role_permissions rp
            JOIN permissions p ON rp.permission_id = p.id
            JOIN system_pages sp ON p.page_id = sp.id
            WHERE rp.role_id = ?
            ORDER BY sp.display_name, p.display_name
        ");
        $stmt->execute([$role_id]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    /**
     * تخصیص مجوز به نقش
     * 
     * @param int $role_id
     * @param int $permission_id
     * @return bool
     */
    public function grant_permission($role_id, $permission_id) {
        try {
            $stmt = $this->pdo->prepare("INSERT INTO role_permissions (role_id, permission_id) VALUES (?, ?) ON DUPLICATE KEY UPDATE granted_at = CURRENT_TIMESTAMP");
            return $stmt->execute([$role_id, $permission_id]);
        } catch (Exception $e) {
            return false;
        }
    }
    
    /**
     * حذف مجوز از نقش
     * 
     * @param int $role_id
     * @param int $permission_id
     * @return bool
     */
    public function revoke_permission($role_id, $permission_id) {
        try {
            $stmt = $this->pdo->prepare("DELETE FROM role_permissions WHERE role_id = ? AND permission_id = ?");
            return $stmt->execute([$role_id, $permission_id]);
        } catch (Exception $e) {
            return false;
        }
    }
    
    /**
     * پاک کردن کش مجوزهای کاربر
     */
    public function clear_user_cache() {
        $this->user_permissions = null;
        $this->user_pages = null;
    }
}
