<?php
require_once '../includes/auth.php';
require_once '../db_connection.php';
require_once '../includes/date_helper.php';
header('Content-Type: application/json; charset=utf-8');

$current_user_id = current_user_id();
$pdo = db_connect();

$stmt = $pdo->prepare('
    SELECT brp.*, br.problem_description, br.report_datetime, sender.name AS sender_name
    FROM breakdown_report_pokes brp
    JOIN breakdown_reports br ON brp.report_id = br.id
    JOIN users sender ON brp.sender_id = sender.id
    WHERE brp.recipient_id = ?
    ORDER BY brp.created_at DESC
');
$stmt->execute([$current_user_id]);
$reminders = $stmt->fetchAll(PDO::FETCH_ASSOC);

foreach ($reminders as &$reminder) {
    $reminder['created_at_shamsi'] = format_shamsi_datetime($reminder['created_at']);
}

echo json_encode([
    'success' => true,
    'reminders' => $reminders
], JSON_UNESCAPED_UNICODE);
