<?php
/**
 * Database Setup for Reporting Module
 * - Creates required tables for report definitions and dashboard widgets.
 * - Registers necessary pages and permissions for the system.
 *
 * NOTE: Run this script once to set up the database schema.
 */

// Corrected path: Go up one directory from 'includes' to find the root.
require_once __DIR__ . '/../db_connection.php';

function setup_reporting_schema(PDO $pdo): void
{
    echo "Starting database setup for reporting module...\n";

    try {
        // Create report_definitions table
        $pdo->exec("CREATE TABLE IF NOT EXISTS report_definitions (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(150) NOT NULL,
            description TEXT NULL,
            owner_id INT NOT NULL,
            is_public BOOLEAN DEFAULT 0,
            definition_json JSON NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            <PERSON><PERSON><PERSON><PERSON><PERSON> (owner_id) REFERENCES users(id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_persian_ci;");
        echo "Table 'report_definitions' created or already exists.\n";

        // Create dashboard_report_widgets table
        $pdo->exec("CREATE TABLE IF NOT EXISTS dashboard_report_widgets (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            report_id INT NOT NULL,
            position INT NOT NULL DEFAULT 0,
            display_options JSON NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
            FOREIGN KEY (report_id) REFERENCES report_definitions(id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_persian_ci;");
        echo "Table 'dashboard_report_widgets' created or already exists.\n";

        // Register pages if missing
        $pages = [
            ['report_builder', 'سازنده گزارش', 'ساخت و ذخیره گزارش‌های سفارشی', 'report_builder.php', 'fas fa-chart-bar', 9],
            ['dashboard_reports', 'سفارشی‌سازی داشبورد', 'انتخاب گزارش‌ها جهت نمایش در داشبورد', 'dashboard_reports.php', 'fas fa-th-large', 10],
        ];
        foreach ($pages as $p) {
            $stmt = $pdo->prepare('SELECT COUNT(*) FROM system_pages WHERE page_name = ?');
            $stmt->execute([$p[0]]);
            if ((int)$stmt->fetchColumn() === 0) {
                $ins = $pdo->prepare('INSERT INTO system_pages (page_name, display_name, description, file_path, icon, sort_order, is_active) VALUES (?,?,?,?,?, ?, 1)');
                $ins->execute([$p[0], $p[1], $p[2], $p[3], $p[4], $p[5]]);
                echo "Page '{$p[0]}' registered.\n";
            }
        }

        // Register permissions if missing
        $permissionsByPage = [
            'report_builder' => ['view', 'create', 'edit', 'delete', 'execute'],
            'dashboard_reports' => ['view', 'manage'],
        ];
        foreach ($permissionsByPage as $pageName => $perms) {
            $pageIdStmt = $pdo->prepare('SELECT id FROM system_pages WHERE page_name = ?');
            $pageIdStmt->execute([$pageName]);
            $pageId = $pageIdStmt->fetchColumn();
            if (!$pageId) continue;
            foreach ($perms as $permName) {
                $exists = $pdo->prepare('SELECT COUNT(*) FROM permissions WHERE page_id = ? AND permission_name = ?');
                $exists->execute([$pageId, $permName]);
                if ((int)$exists->fetchColumn() === 0) {
                    $ins = $pdo->prepare('INSERT INTO permissions (page_id, permission_name, display_name, description) VALUES (?,?,?,?)');
                    $ins->execute([$pageId, $permName, $permName, null]);
                    echo "Permission '{$permName}' for page '{$pageName}' registered.\n";
                }
            }
        }

        // Grant all permissions to admin role
        $adminRoleIdStmt = $pdo->query("SELECT id FROM roles WHERE name = 'admin' LIMIT 1");
        if ($adminRoleIdStmt) {
            $adminRoleId = $adminRoleIdStmt->fetchColumn();
            if ($adminRoleId) {
                $permCount = $pdo->exec("INSERT INTO role_permissions (role_id, permission_id)
                            SELECT {$adminRoleId}, p.id
                            FROM permissions p
                            LEFT JOIN role_permissions rp ON rp.role_id = {$adminRoleId} AND rp.permission_id = p.id
                            WHERE rp.id IS NULL");
                echo "Granted {$permCount} new permissions to the admin role.\n";
            }
        }

        echo "Database setup completed successfully.\n";

    } catch (Exception $e) {
        die("An error occurred during database setup: " . $e->getMessage());
    }
}

// Execute the setup
// Ensure db_connect() function is available from the included file
if (function_exists('db_connect')) {
    $pdo = db_connect();
    setup_reporting_schema($pdo);
} else {
    die("Error: The 'db_connect' function is not defined. Please check the 'db_connection.php' file.");
}

?>
