<?php
require_once '../includes/auth.php';
require_once '../db_connection.php';
require_once '../includes/date_helper.php';
require_once '../includes/header.php';

$current_user_id = current_user_id();
$pdo = db_connect();

// دریافت یادآوری‌های دریافتی
$stmt = $pdo->prepare('
    SELECT brp.*, br.problem_description, br.report_datetime, sender.name AS sender_name
    FROM breakdown_report_pokes brp
    JOIN breakdown_reports br ON brp.report_id = br.id
    JOIN users sender ON brp.sender_id = sender.id
    WHERE brp.recipient_id = ?
    ORDER BY brp.created_at DESC
');
$stmt->execute([$current_user_id]);
$reminders = $stmt->fetchAll(PDO::FETCH_ASSOC);
?>
<!DOCTYPE html>
<html lang="fa" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>یادآوری‌های دریافتی من</title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="../assets/fontawesome/css/all.min.css">
    <style>
        .reminders-container {
            max-width: 600px;
            margin: 30px auto;
            padding: 0 10px 40px;
        }
        .reminder-card {
            background: #fff;
            border-radius: 14px;
            box-shadow: 0 4px 16px rgba(255, 102, 0, 0.10);
            margin-bottom: 18px;
            padding: 18px 16px;
            display: flex;
            flex-direction: column;
            gap: 8px;
        }
        .reminder-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            gap: 8px;
        }
        .reminder-title {
            font-size: 1.1rem;
            font-weight: 700;
            color: #d94307;
        }
        .reminder-date {
            font-size: 0.95rem;
            color: #888;
        }
        .reminder-message {
            font-size: 1rem;
            color: #333;
            margin: 6px 0;
            white-space: pre-line;
        }
        .reminder-sender {
            font-size: 0.95rem;
            color: #a16207;
            display: flex;
            align-items: center;
            gap: 4px;
        }
        .reminder-report-link {
            font-size: 0.95rem;
            color: #2563eb;
            text-decoration: underline;
            margin-top: 4px;
        }
        @media (max-width: 600px) {
            .reminders-container {
                max-width: 98vw;
                padding: 0 2vw 30px;
            }
            .reminder-card {
                padding: 12px 7px;
            }
            .reminder-title {
                font-size: 1rem;
            }
        }
    </style>
</head>
<body>
    <div class="reminders-container">
        <h2 style="text-align:center;color:#d94307;margin-bottom:22px;font-weight:700;font-size:1.3rem;">یادآوری‌های دریافتی من</h2>
        <?php if (empty($reminders)): ?>
            <div class="reminder-card" style="text-align:center;color:#888;">هیچ یادآوری‌ای برای شما ثبت نشده است.</div>
        <?php else: ?>
            <?php foreach ($reminders as $reminder): ?>
                <div class="reminder-card">
                    <div class="reminder-header">
                        <span class="reminder-title"><i class="fas fa-bell"></i> یادآوری جدید</span>
                        <span class="reminder-date"><i class="fas fa-clock"></i> <?= format_shamsi_datetime($reminder['created_at']) ?></span>
                    </div>
                    <div class="reminder-message">
                        <?= htmlspecialchars($reminder['message']) ?>
                    </div>
                    <div class="reminder-sender">
                        <i class="fas fa-user"></i> ارسال‌کننده: <?= htmlspecialchars($reminder['sender_name']) ?>
                    </div>
                    <div class="reminder-report-link">
                        <a href="reports_list.php?highlight=<?= $reminder['report_id'] ?>" target="_blank">مشاهده گزارش مرتبط</a>
                    </div>
                </div>
            <?php endforeach; ?>
        <?php endif; ?>
    </div>
    <?php include '../includes/footer.php'; ?>
</body>
</html>
