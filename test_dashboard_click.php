<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>تست کلیک داشبورد</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .report-list-container {
            max-height: 400px;
            overflow-y: auto;
            border-radius: 8px;
            border: 1px solid #e5e7eb;
        }
        
        .report-list-item {
            display: flex;
            align-items: center;
            padding: 1rem;
            border-bottom: 1px solid #f3f4f6;
            cursor: pointer;
            transition: all 0.2s ease;
            background-color: white;
        }
        
        .report-list-item:hover {
            background-color: #f8fafc;
        }
        
        .needs-my-approval {
            background-color: #fef3c7 !important;
            border-right: 4px solid #f59e0b !important;
        }
        
        .item-status-indicator {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 0.9rem;
            margin-left: 1rem;
            flex-shrink: 0;
        }
        
        .item-content {
            flex-grow: 1;
            min-width: 0;
        }
        
        .item-title {
            font-weight: 600;
            color: #1f2937;
            font-size: 0.95rem;
            margin-bottom: 0.25rem;
            line-height: 1.4;
        }
        
        .item-details {
            font-size: 0.8rem;
            color: #6b7280;
            line-height: 1.3;
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
        }
        
        .my-approval-indicator {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 0.5rem;
            background-color: #f59e0b;
            color: white;
            border-radius: 6px;
            font-size: 0.75rem;
            text-align: center;
            margin-right: 1rem;
            min-width: 80px;
            flex-shrink: 0;
        }
    </style>
</head>
<body>
    <h2>تست کلیک داشبورد</h2>
    
    <div class="report-list-container">
        <div class="report-list-item needs-my-approval" onclick="openApprovalPage('WO-001')">
            <div class="item-status-indicator" style="background-color: #f59e0b;">
                <i class="fas fa-exclamation-circle"></i>
            </div>
            <div class="item-content">
                <div class="item-title">تعمیر پمپ آب اصلی</div>
                <div class="item-details">
                    <span>شماره: WO-001</span> • 
                    <span>دستگاه: پمپ آب</span> • 
                    <span>تاریخ: 1403/09/15</span>
                </div>
            </div>
            <div class="my-approval-indicator">
                <i class="fas fa-user-check"></i>
                <span>نیاز به تایید شما</span>
            </div>
        </div>
        
        <div class="report-list-item" onclick="openApprovalPage('WO-002')">
            <div class="item-status-indicator" style="background-color: #6b7280;">
                <i class="fas fa-clock"></i>
            </div>
            <div class="item-content">
                <div class="item-title">بررسی موتور کمپرسور</div>
                <div class="item-details">
                    <span>شماره: WO-002</span> • 
                    <span>دستگاه: کمپرسور</span> • 
                    <span>تاریخ: 1403/09/14</span>
                </div>
            </div>
        </div>
    </div>

    <script>
        function openApprovalPage(workorderId) {
            console.log('Opening approval page for:', workorderId);
            const url = `pages/repair_approvals.php?workorder_id=${workorderId}`;
            console.log('URL:', url);
            alert('باز کردن صفحه تایید برای: ' + workorderId + '\nURL: ' + url);
            window.open(url, '_blank');
        }
    </script>
</body>
</html>
