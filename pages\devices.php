<?php
require_once '../includes/auth.php';
require_once '../db_connection.php';
require_once '../includes/date_helper.php'; // اضافه کردن فایل تبدیل تاریخ

// بررسی دسترسی به صفحه دستگاه‌ها
require_page_access('devices', 'view');

$pdo = db_connect();
// START DEVICE REPAIR HISTORY HELPERS
if (!function_exists('to_persian_numerals')) {
    function to_persian_numerals($string) {
        $persian_digits = ['۰','۱','۲','۳','۴','۵','۶','۷','۸','۹'];
        $english_digits = ['0','1','2','3','4','5','6','7','8','9'];
        return str_replace($english_digits, $persian_digits, $string);
    }
}
if (!function_exists('format_shamsi_datetime')) {
    function format_shamsi_datetime($datetime_string) {
        if (empty($datetime_string) || str_starts_with($datetime_string,'0000-00-00')) {
            return '-';
        }
        try {
            $date_obj = new DateTime($datetime_string);
            $shamsi_date = to_shamsi($datetime_string,'Y/m/d');
            if (strpos($datetime_string,' ') !== false && $date_obj->format('H:i:s') !== '00:00:00') {
                $time = $date_obj->format('H:i');
                return $shamsi_date.'، ساعت '.to_persian_numerals($time);
            }
            return $shamsi_date;
        } catch (Exception $e) {
            return to_shamsi($datetime_string,'Y/m/d');
        }
    }
}
if (!function_exists('json_response')) {
    function json_response($data, $statusCode = 200) {
        if (ob_get_level() > 0) { ob_clean(); }
        http_response_code($statusCode);
        header('Content-Type: application/json; charset=utf-8');
        echo json_encode($data, JSON_UNESCAPED_UNICODE);
        exit;
    }
}
// Handle AJAX request for device history
if (isset($_GET['action']) && $_GET['action'] === 'get_device_history') {
    $device_id = $_GET['device_id'] ?? 0;
    if (empty($device_id)) {
        json_response(['success'=>false,'message'=>'شناسه دستگاه نامعتبر است.'],400);
    }
    $stmt = $pdo->prepare("
        SELECT
            wo.id, wo.title, wo.status,
            exec.actual_start_datetime,
            exec.actual_end_datetime,
            exec.completion_notes,
            exec.delay_reason,
            exec.exit_date,
            exec.exit_where,
            exec.exit_desc,
            exec.back_date,
            exec.back_desc,
            exec.back_pay,
            u_verifier.name as verifier_name,
            (SELECT GROUP_CONCAT(u.name) FROM work_order_labor wol JOIN users u ON wol.user_id=u.id WHERE wol.work_order_id = wo.id) as labor_users,
            (SELECT GROUP_CONCAT(wop.part_name, ' (', wop.quantity_used, ' ', wop.unit, ')') FROM work_order_parts wop WHERE wop.work_order_id = wo.id) as parts_used
        FROM work_orders wo
        LEFT JOIN work_order_execution exec ON wo.id = exec.work_order_id
        LEFT JOIN users u_verifier ON wo.verifier_id = u_verifier.id
        WHERE wo.device_id = ?
        ORDER BY exec.actual_end_datetime DESC LIMIT 100
    ");
    $stmt->execute([$device_id]);
    $history = $stmt->fetchAll(PDO::FETCH_ASSOC);
    foreach ($history as &$task) {
        $task['actual_start_datetime_shamsi'] = format_shamsi_datetime($task['actual_start_datetime']);
        $task['actual_end_datetime_shamsi'] = format_shamsi_datetime($task['actual_end_datetime']);
        $task['exit_date_shamsi'] = $task['exit_date'] ? to_shamsi($task['exit_date']) : null;
        $task['back_date_shamsi'] = $task['back_date'] ? to_shamsi($task['back_date']) : null;
    }
    json_response(['success'=>true,'history'=>$history]);
}
// END DEVICE REPAIR HISTORY HELPERS

// تابع get_locations به صفحه device_add.php منتقل شده است

// کد مربوط به افزودن دستگاه به صفحه device_add.php منتقل شده است
$devices = $pdo->query("
    SELECT d.*, l.location_name 
    FROM devices d
    LEFT JOIN locations l ON d.location = l.id
    ORDER BY updated_at DESC, created_at DESC
")->fetchAll();

// دریافت پیام از session
$toast_message = null;
$toast_type = null;

if (isset($_SESSION['toast_message'])) {
    $toast_message = $_SESSION['toast_message'];
    $toast_type = $_SESSION['toast_type'] ?? 'info';
    // حذف پیام از session تا فقط یک بار نمایش داده شود
    unset($_SESSION['toast_message']);
    unset($_SESSION['toast_type']);
}

include '../includes/header.php';
?>
<head>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="../assets/css/persian-datepicker.min.css">
    <script src="../assets/js/jquery.min.js"></script>
    <script src="../assets/js/persian-date.min.js"></script>
    <script src="../assets/js/persian-datepicker.min.js"></script>
    <?php if ($toast_message): ?>
        <meta name="toast-message" content="<?= htmlspecialchars($toast_message) ?>">
        <meta name="toast-type" content="<?= htmlspecialchars($toast_type) ?>">
    <?php endif; ?>

    <style>
        * { box-sizing: border-box; }
        body { overflow-x: hidden; }

        
        .device-list { flex: 2;max-width: 80%;
    margin: auto; }
        .table-scroll-container { width: 100%; overflow-x: auto; }
        
        
        
        .add-device-btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            background-color: #28a745;
            color: white;
            text-decoration: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-weight: bold;
            font-size: 16px;
            transition: background-color 0.3s ease;
        }
        
        .add-device-btn:hover {
            background-color: #218838;
            color: white;
            text-decoration: none;
        }

        /* استایل‌های جدید برای مودال شناسنامه چاپی */
        #deviceDetailsModal {
            position: fixed;
            z-index: 10000;
            inset: 0;
            background: rgba(0,0,0,0.6);
            display: none;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        #deviceDetailsModalBody {
            background: #fff;
            border-radius: 5px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            padding: 0;
            width: 100%;
            max-width: 800px; /* عرض مناسب برای برگه A4 */
            max-height: 95vh;
            overflow: hidden; /* حذف اسکرول اصلی */
            position: relative;
            font-family: 'Vazirmatn', sans-serif;
            border: 1px solid #333;
        }

        .printable-certificate {
            height: 100%;
            overflow-y: auto; /* اسکرول داخلی در صورت نیاز */
            padding: 43px;
            background: #fff;
            color: #000;
        }

        .printable-certificate .cert-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 2px solid #000;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }

        .printable-certificate .cert-header h4 {
            margin: 0;
            font-size: 1.6rem;
            font-weight: bold;
        }

        .printable-certificate .cert-header .device-id {
            font-size: 1rem;
            font-weight: bold;
        }

        .printable-certificate .cert-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }

        .printable-certificate .cert-table th,
        .printable-certificate .cert-table td {
            border: 1px solid #000;
            padding: 8px;
            text-align: right;
            font-size: 0.9rem;
        }

        .printable-certificate .cert-table th {
            background-color: #e9ecef;
            font-weight: bold;
        }

        .printable-certificate .cert-section-title {
            font-size: 1.2rem;
            font-weight: bold;
            margin-top: 20px;
            margin-bottom: 10px;
            border-bottom: 1px solid #ccc;
            padding-bottom: 5px;
        }

        .printable-certificate .cert-gallery {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
            gap: 10px;
        }

        .printable-certificate .cert-gallery img {
            width: 100%;
            height: 150px;
            object-fit: cover;
            border: 1px solid #ccc;
            border-radius: 4px;
        }

        .printable-certificate .cert-footer {
            margin-top: 30px;
            text-align: center;
            font-size: 0.8rem;
            color: #555;
        }

        .close-btn {
            position: absolute;
            top: 10px;
            right: 10px;
            background: #eee;
            color: #000;
            border: 1px solid #ccc;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            font-size: 16px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        @media print {
            body, #deviceDetailsModal {
                background: #fff !important;
            }
            #deviceDetailsModalBody {
                box-shadow: none !important;
                border: none !important;
                max-height: none !important;
            }
            .close-btn, .add-device-section, .device-list h3, .device-search-container, .table-scroll-container th:last-child, .table-scroll-container td:last-child {
                display: none !important;
            }
            .printable-certificate {
                padding: 0;
                overflow: visible;
            }
        }

        .close-btn:hover {
            background: rgba(255,255,255,0.3) !important;
            transform: scale(1.1) !important;
        }

        /* Override any conflicting styles from main CSS */
        #deviceDetailsModal * {
            box-sizing: border-box !important;
        }

        #deviceDetailsModal .modal-body {
            background: none !important;
            border: none !important;
            padding: 0 !important;
            margin: 0 !important;
            max-width: none !important;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
        }

        #deviceDetailsModal h4 {
            margin: 0 !important;
            padding: 0 !important;
            border: none !important;
            color: white !important;
            font-size: 1.4rem !important;
            font-weight: 600 !important;
           
        }

        #deviceDetailsModal p {
            margin: 10px 0 !important;
            line-height: 1.6 !important;
            font-size: 1rem !important;
            color: #495057 !important;
        }

        #deviceDetailsModal img {
            max-width: 100% !important;
            max-height: none !important;
            border-radius: 12px !important;
            object-fit: cover !important;
            border: 3px solid #e9ecef !important;
        }

        #deviceDetailsModal .close-btn {
            background: rgba(255,255,255,0.2) !important;
            color: white !important;
            border: none !important;
            font-size: 18px !important;
            cursor: pointer !important;
        }

        /* بخش اطلاعات پایه */
        .certificate-section {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            border: 1px solid rgba(0,0,0,0.05);
        }

        .section-title {
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 1.2rem;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #e9ecef;
        }

        .section-title i {
            color: #667eea;
            font-size: 1.3rem;
        }

        .fields-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 15px;
        }

        .field-item {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }

        .field-item strong {
            color: #495057;
            font-size: 0.9rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .field-item span {
            color: #2c3e50;
            font-size: 1.1rem;
            font-weight: 500;
            padding: 8px 12px;
            background: #f8f9fa;
            border-radius: 8px;
            border-right: 4px solid #667eea;
        }

        /* گالری ت��اویر */
        /* گالری به صورت ردیفی و اسکرول‌پذیر افقی */
        #deviceDetailsModal .modal-gallery {
            display: contents !important;
            flex-direction: row;
            gap: 15px;
            margin-top: 15px;

            padding-bottom: 12px;
            scrollbar-color: #aaa #e9ecef;
            scrollbar-width: thin;
            width: 100%;
        }
        #deviceDetailsModal .modal-gallery img {
            flex: 0 0 auto;
            width: 140px;
            height: 110px;
            object-fit: cover;
            border-radius: 12px;
            border: 3px solid #e9ecef;
            transition: all 0.3s;
            cursor: pointer;
            background: #fff;
        }
        #deviceDetailsModal .modal-gallery img:hover {
            transform: scale(1.08);
            border-color: #0a7fda;
            z-index:2;
        }
        @media (max-width: 700px) {
            #deviceDetailsModal .modal-gallery img {
                width: 92px;
                height: 80px;
            }
        }
        /* دکمه بستن واضح */
        #deviceDetailsModal .close-btn {
            background: #222 !important;
            color: #fff !important;
            border: 2px solid #fff !important;
            width: 36px;
            height: 36px;
            border-radius: 50%;
            font-size: 20px !important;
            opacity: 0.93;
            box-shadow: 0 0 0 4px #fff3;
            z-index: 20;
        }
        #deviceDetailsModal .close-btn:hover {
            background: #000 !important;
            color: #fff !important;
            border: 2px solid #63e !important;
            opacity: 1;
        }
        /* فعال‌سازی اسکرول کل مودال */
        #deviceDetailsModalBody {
            overflow-y: auto !important;
        }
        .printable-certificate {
            height: auto !important;
            max-height: unset !important;
        }

        .modal-gallery img {
            width: 100%;
            height: 120px;
            object-fit: cover;
            border-radius: 12px;
            border: 3px solid #e9ecef;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .modal-gallery img:hover {
            transform: scale(1.05);
            border-color: #667eea;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        /* جداول */
        .detail-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        }

        .detail-table thead {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }

        .detail-table th {
            padding: 15px 12px;
            text-align: right;
            font-weight: 600;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .detail-table td {
            padding: 12px;
            text-align: right;
            border-bottom: 1px solid #e9ecef;
            color: #495057;
            font-weight: 500;
        }

        .detail-table tbody tr:hover {
            background: #f8f9fa;
        }

        .detail-table tbody tr:last-child td {
            border-bottom: none;
        }

        /* بخش توضیحات */
        .description-section {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            border-radius: 12px;
            padding: 20px;
            border-right: 5px solid #667eea;
        }

        .description-section strong {
            color: #2c3e50;
            font-size: 1rem;
            font-weight: 600;
            display: block;
            margin-bottom: 10px;
        }

        .description-content {
            color: #495057;
            line-height: 1.6;
            font-size: 1rem;
        }

        /* فوتر */
        .certificate-footer {
            text-align: center;
            margin-top: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            border-radius: 12px;
            color: #6c757d;
            font-size: 0.9rem;
            font-weight: 500;
        }

        /* پیام خالی بودن */
        .empty-message {
            text-align: center;
            color: #6c757d;
            font-style: italic;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
            border: 2px dashed #dee2e6;
        }

        /* ریسپانسیو */
        @media (max-width: 768px) {
            #deviceDetailsModalBody {
                width: 98%;
                max-width: none;
                margin: 10px;
            }
            
            .device-certificate-new {
                padding: 20px;
            }
            
            .certificate-header {
                margin: -20px -20px 20px -20px;
                padding: 20px;
            }
            
            .fields-row {
                grid-template-columns: 1fr;
                gap: 15px;
            }
            
            .modal-gallery {
                grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
                gap: 10px;
            }
            
            .detail-table {
                font-size: 0.85rem;
            }
            
            .detail-table th,
            .detail-table td {
                padding: 8px 6px;
            }
        }

        // استایل‌های مربوط به image preview به صفحه device_add.php منتقل شده است

        @media (max-width: 768px) {
            .device-container { flex-direction: column; padding: 10px; }
            .device-list { width: 100%; flex: none; }
            .add-device-btn {
                width: 100%;
                justify-content: center;
            }
            .device-list table { border: none; }
            .device-list table thead { display: none; }
            .device-list table tr { 
                display: block; 
                margin-bottom: 15px; 
                border: 1px solid #ddd; 
                border-radius: 8px; 
                padding: 15px; 
                background-color: transparent; /* **اصلاح**: حذف پس‌زمینه سفید */
                box-shadow: 0 2px 4px rgba(0,0,0,0.05); 
            }
            .device-list table td { display: flex; justify-content: space-between; align-items: center; padding: 10px 5px; text-align: right; border-bottom: 1px dotted #eee; }
            .device-list table td:last-child { border-bottom: none; }
            .device-list table td::before { content: attr(data-label); font-weight: bold; margin-left: 10px; color: #333; }
            
            /* **اصلاح**: چیدمان جدید برای دکمه‌های عملیات */
            .device-list table td[data-label="عملیات"] { 
                display: flex;
                flex-direction: row;
                justify-content: left;
                gap: 8px; /* فاصله بین دکمه‌ها */
                padding: 10px 0 0 0; /* فاصله از بالای سلول */
            }
            .device-list table td[data-label="عملیات"]::before { display: none; }
            .device-list table td[data-label="عملیات"] button, 
            .device-list table td[data-label="عملیات"] a {
                display: block;
                text-align: center;
            }
            .device-list table td[data-label="عملیات"] a button {
                width: 100%; /* دکمه داخل لینک عرض کامل بگیرد */
            }
        }/* ------ Device repair history modal styles ------ */
#device-history-modal { display:none; position:fixed; inset:0; background-color:rgba(0,0,0,0.6); z-index:1000; justify-content:center; align-items:center; }
#device-history-modal .modal-header { padding: 1rem 1.5rem; border-bottom: 1px solid #e9ecef; display: flex; flex-direction: column; align-items: stretch; gap: 1rem; flex-shrink: 0; }
#device-history-modal .modal-header-top { display: flex; justify-content: space-between; align-items: center; }
#device-history-modal .modal-body { padding: 0.5rem 1.5rem 1.5rem 1.5rem; }
#device-history-modal .history-filters { display: flex; flex-direction: column; gap: 1rem; }
#device-history-modal .filter-actions { display: flex; justify-content: space-between; align-items: center; gap: 1rem; }
#device-history-modal .advanced-filters { display: none; flex-direction: column; gap: 1rem; padding: 1rem; border-radius: 6px; background-color: #f8f9fa; border: 1px solid #e9ecef; margin-top: 0.5rem; }
#device-history-modal .filter-row { display: flex; flex-direction: column; gap: 0.5rem; }
#device-history-modal .filter-row input, #device-history-modal .filter-row select { width: 100%; padding: 0.5rem; border: 1px solid #ccc; border-radius: 4px; }
        #device-history-modal #toggle-filters-btn { background: none; border: 1px dashed #aaa; color: #555; padding: 0.5rem 1rem; border-radius: 6px; flex-grow: 1; text-align: right; cursor: pointer; }
        #device-history-modal #toggle-filters-btn .icon { float: left; transition: transform 0.3s; }
        #device-history-modal #toggle-filters-btn.open .icon { transform: rotate(180deg); }
        #device-history-modal #clear-filters-btn { background: none; border: none; color: #dc3545; font-size: var(--fs-2xl); cursor: pointer; padding: 0 0.5rem; }
        #device-history-modal .history-cards-container { max-height: 55vh; overflow-y: auto; padding: 5px; }
        #device-history-modal .history-card { background: #fff; border-radius: 8px; border-left: 5px solid #6c757d; margin-bottom: 1rem; box-shadow: 0 2px 8px rgba(0,0,0,0.08); padding: 0; }
        #device-history-modal .history-card-main { cursor: pointer; padding: 1rem; position: relative; }
        #device-history-modal .history-card-summary { flex-grow: 1; padding-left: 2rem; }
        #device-history-modal .history-card-summary p { margin: 0.25rem 0; color: #555; }
        #device-history-modal .history-card-title-line { display: flex; justify-content: space-between; align-items: center; gap: 1rem; margin-bottom: 0.5rem; }
        #device-history-modal .history-card-title-line strong { color: #333; flex-grow: 1; }
        #device-history-modal .history-card-toggle { position: absolute; bottom: 0.5rem; left: 0.5rem; background: none; border: none; font-size: var(--fs-sm); color: #aaaaaa; cursor: pointer; padding: 0.2rem; transition: transform 0.3s ease; }
        #device-history-modal .history-card.active .history-card-toggle { transform: rotate(180deg); }
        #device-history-modal .history-card-collapsible-content { max-height: 0; overflow: hidden; transition: max-height 0.4s ease-out; }
        #device-history-modal .history-card.active .history-card-collapsible-content { max-height: max-content; transition: max-height 0.5s ease-in; }
        #device-history-modal .history-card-details { padding: 0 1rem 1rem 1rem; }
        #device-history-modal .history-card-details p { font-size: var(--fs-sm); color: #555; }
        #device-history-modal .history-card-details p strong { color: #333; }
        #device-history-modal .status-badge, .priority-badge { padding: 0.2em 0.6em; border-radius: 0.25rem; font-size: var(--fs-xs); font-weight: bold; color: white; flex-shrink: 0; }
        #device-history-modal .status-انجام-شده { background-color: #28a745; }
        #device-history-modal .status-منتظر-تایید { background-color: #ffc107; color: #333; }
        #device-history-modal .status-تایید-شده { background-color: #17a2b8; }
        #device-history-modal .status-بسته-شده { background-color: #6c757d; }
        #device-history-modal .status-done-confirmed { background-color: #28a745 !important; color: #fff !important; }
        #device-history-modal .status-rejected { background-color: #dc3545 !important; color: #fff !important; }
        #device-history-modal .history-card-divider { border: 0; border-top: 1px solid #eee; margin: 0 1rem; }
        #device-history-modal .modal-content { width: 80%; padding: 38px 20px !important; }

        /* استایل‌های بخش برون‌سپاری */
        #device-history-modal .outsourcing-section {
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 20px;
        }

        #device-history-modal .outsourcing-section h5 {
            margin: 0 0 10px 0;
            font-size: 1rem;
            font-weight: 600;
        }

        #device-history-modal .outsourcing-section p {
            margin: 5px 0;
            font-size: 0.9rem;
        }

        #device-history-modal .outsourcing-section p strong {
            color: #495057;
        }

        /* استایل کانتینر وضعیت و آیکون برون‌سپاری */
        #device-history-modal .status-container {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        #device-history-modal .outsourcing-icon {
            color: #007bff;
            font-size: 14px;
            cursor: help;
        }
/* ----------------------------------------------- */
    </style>
</head>
<body>
<div class="device-container">
    <div class="device-list">
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1rem;">
            <h3>لیست دستگاه‌ها</h3>
            <?php if (has_permission('devices', 'create')): ?>
            <a href="device_add.php" class="add-device-btn" style="margin: 0;">
                <i class="fas fa-plus"></i> افزودن دستگاه جدید
            </a>
            <?php endif; ?>
        </div>
        <div class="device-search-container"><input type="text" id="deviceSearch" placeholder="جستجو در لیست..." class="device-search-input"></div>
        <div class="table-scroll-container">
            <table border="1" cellpadding="8" cellspacing="0" style="border-collapse: collapse; width:100%;">
                <thead><tr><th>نام دستگاه</th><th>شماره سریال</th><th>محل استفاده</th><th>وضعیت</th><th>آخرین ویرایش</th><th>عملیات</th></tr></thead>
                <tbody>
                    <?php foreach ($devices as $device): ?>
                        <tr data-name="<?= htmlspecialchars($device['name']) ?>" data-serial="<?= htmlspecialchars($device['serial_number']) ?>" data-status="<?= htmlspecialchars($device['status']) ?>" data-vendor="<?= htmlspecialchars($device['vendor_name']) ?>" data-parts="<?php $stmtParts = $pdo->prepare('SELECT part_name FROM device_parts WHERE device_id = ?'); $stmtParts->execute([$device['id']]); echo htmlspecialchars(implode(', ', $stmtParts->fetchAll(PDO::FETCH_COLUMN))); ?>">
                            <td data-label="نام دستگاه"><?= htmlspecialchars($device['name']) ?></td>
                            <td data-label="شماره سریال"><?= htmlspecialchars($device['serial_number']) ?: '-' ?></td>
                            <td data-label="محل استفاده"><?= htmlspecialchars($device['location_name'] ?? '-') ?></td>
                            <td data-label="وضعیت"><?= htmlspecialchars($device['status']) ?></td>
                            <td data-label="آخرین ویرایش"><?php $date = $device['updated_at'] ?: $device['created_at']; echo to_shamsi($date, 'Y/m/d H:i'); ?></td>
                            <td data-label="عملیات">
                                <button class="btn btn-sm btn-info" onclick="showDeviceDetails(<?= $device['id'] ?>, '<?= htmlspecialchars(addslashes($device['images'] ?? '[]')) ?>')">شناسنامه دستگاه</button>
                                <?php if (has_permission('devices', 'view_history')): ?>
                                <button class="btn btn-sm btn-info" onclick="openDeviceHistoryModal(<?= $device['id'] ?>)">سوابق تعمیرات</button>
                                <?php endif; ?>
                                <?php if (has_permission('devices', 'edit')): ?>
                                <button class="btn btn-sm btn-warning" onclick="window.location.href='device_edit.php?id=<?= $device['id'] ?>'">ویرایش</button>
                                <?php endif; ?>
                                <?php if (has_permission('devices', 'delete')): ?>
                                <button class="btn btn-sm btn-danger" onclick="if(confirm('آیا از حذف دستگاه <?= htmlspecialchars(addslashes($device['name'])) ?> مطمئن هستید؟')) { deleteDevice(<?= $device['id'] ?>); }">حذف</button>
                                <?php endif; ?>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<div id="deviceDetailsModal" class="device-certificate-modal">
    <div id="deviceDetailsModalBody">
        <button onclick="closeDeviceModal()" class="close-btn" aria-label="بستن مودال">&times;</button>
        <!-- Modal content is added here by JS -->
    </div>
</div>
<!-- Device repair history modal -->
<div id="device-history-modal">
    <div class="modal-content" style="background:#fff; margin:5% auto; border-radius:8px; max-width:750px; padding:1.5rem; position:relative;">
        <span class="close-btn" onclick="closeDeviceHistoryModal()" style="position:absolute;left:15px;top:10px;font-size:var(--fs-3xl);cursor:pointer;">&times;</span>
        <h4 style="margin-top:0;">سوابق تعمیرات دستگاه</h4>
        <div id="device-history-cards-container" class="history-cards-container"></div>
    </div>
</div>
<?php include '../includes/footer.php'; ?>
<script>


// ---- مودال نمایش تصویر تکی ----
function openImageModal(imgSrc) {
    let modal = document.getElementById('singleImageModal');
    if (!modal) {
        modal = document.createElement('div');
        modal.id = 'singleImageModal';
        modal.style.position = 'fixed';
        modal.style.zIndex = 15000;
        modal.style.inset = 0;
        modal.style.background = 'rgba(0,0,0,0.89)';
        modal.style.display = 'flex';
        modal.style.alignItems = 'center';
        modal.style.justifyContent = 'center';
        modal.innerHTML = `<img src="${imgSrc}" style="max-width:95vw;max-height:95vh;border-radius:18px;box-shadow:0 12px 40px #0008" onclick="event.stopPropagation()"><button style="position:absolute;top:18px;right:28px;font-size:2rem;background:none;border:none;color:#fff;cursor:pointer;z-index:1" onclick="closeImageModal()">&times;</button>`;
        document.body.appendChild(modal);
    } else {
        modal.querySelector('img').src = imgSrc;
        modal.style.display = 'flex';
    }
    modal.onclick = function(){ closeImageModal(); }
}
function closeImageModal() {
    let modal = document.getElementById('singleImageModal');
    if(modal) modal.style.display = 'none';
}
// ---- پایان مودال تصویر ----

// کدهای مربوط به accordion و فرم افزودن دستگاه به صفحه device_add.php منتقل شده است

function showDeviceDetails(id, imagesJson) {
    fetch(`device_details.php?id=${id}&format=json`)
        .then(res => res.json())
        .then(data => {
            if (data.success) {
                const d = data.device;
                // تصاویر دستگاه (آرایه یا خالی)
                let images = [];
                try {
                    images = d.images ? JSON.parse(d.images) : [];
                } catch (e) {
                    images = [];
                }
                // گالری تصاویر
                let imageGallery = '';
                if (images.length > 0) {
                    imageGallery = `<div class="modal-gallery">
                        ${images.map(img => `<img src="../uploads/devices/${img}" alt="تصویر دستگاه" onclick="openImageModal('../uploads/devices/${img}')">`).join('')}
                    </div>`;
                } else {
                    imageGallery = '<div class="empty-message"><i class="fas fa-image"></i> تصویری برای این دستگاه ثبت نشده</div>';
                }
                // قطعات مصرفی جدول
                let partsTable = '';
                if (Array.isArray(d.parts) && d.parts.length > 0) {
                    partsTable = `<table class="cert-table"><thead><tr><th>نام قطعه</th><th>تعداد</th><th>واحد</th><th>توضیحات</th></tr></thead><tbody>
                        ${d.parts.map(p => `<tr><td>${p.part_name}</td><td>${p.quantity}</td><td>${p.unit || '-'}</td><td>${p.description || '-'}</td></tr>`).join('')}
                    </tbody></table>`;
                } else {
                    partsTable = '<div class="empty-message">قطعاتی ثبت نشده است.</div>';
                }
                // موتورها جدول
                let motorsTable = '';
                if (Array.isArray(d.motors) && d.motors.length > 0) {
                    motorsTable = `<table class="cert-table"><thead><tr><th>نام الکتروموتور</th><th>ولتاژ</th><th>توان</th><th>جریان</th><th>RPM</th><th>توضیحات</th></tr></thead><tbody>
                        ${d.motors.map(m => `<tr><td>${m.motor_name}</td><td>${m.voltage || '-'}</td><td>${m.power || '-'}</td><td>${m.current || '-'}</td><td>${m.rpm || '-'}</td><td>${m.description || '-'}</td></tr>`).join('')}
                    </tbody></table>`;
                } else {
                    motorsTable = '<div class="empty-message">مشخصاتی برای موتورها ثبت نشده است.</div>';
                }

                let htmlContent = `
                    <div class="printable-certificate" dir="rtl">
                        <div class="cert-header">
                            <h4 style="color:#000 !important">شناسنامه فنی دستگاه</h4>
                        </div>

                        <table class="cert-table">
                            <tr>
                                <th>نام دستگاه</th>
                                <td>${d.name}</td>
                                <th>شماره سریال</th>
                                <td>${d.serial_number || '-'}</td>
                            </tr>
                            <tr>
                                <th>محل استفاده</th>
                                <td>${d.location_name || '-'}</td>
                                <th>وضعیت</th>
                                <td>${d.status || '-'}</td>
                            </tr>
                            <tr>
                                <th>فروشنده</th>
                                <td>${d.vendor_name || '-'}</td>
                                <th>شماره تماس فروشنده</th>
                                <td>${d.vendor_phone || '-'}</td>
                            </tr>
                            <tr>
                                <th>سال ساخت</th>
                                <td>${d.manufacture_year || '-'}</td>
                                <th>تاریخ خرید</th>
                                <td>${d.purchase_date || '-'}</td>
                            </tr>
                            <tr>
                                <th>تاریخ نصب</th>
                                <td>${d.installation_date || '-'}</td>
                                <th>تاریخ بهره‌برداری</th>
                                <td>${d.operation_date || '-'}</td>
                            </tr>
                            <tr>
                                <th colspan="4">توضیحات</th>
                            </tr>
                            <tr>
                                <td colspan="4">${d.description ? d.description.replace(/\n/g,'<br>') : '-'}</td>
                            </tr>
                        </table>

                        <div class="cert-section-title">قطعات مصرفی</div>
                        ${partsTable}

                        <div class="cert-section-title">مشخصات الکتروموتورها</div>
                        ${motorsTable}

                        <div class="cert-section-title">تصاویر دستگاه</div>
                        <div class="cert-gallery">${imageGallery}</div>

                        <div class="cert-footer">
                            آخرین بروزرسانی: ${d.updated_at}
                        </div>
                    </div>
                `;

                // اضافه کردن دکمه بستن (باید خارج از htmlContent باشد)
                const modalBody = document.getElementById('deviceDetailsModalBody');
                const closeButton = modalBody.querySelector('.close-btn');
                modalBody.innerHTML = '';
                modalBody.appendChild(closeButton);
                $(modalBody).append(htmlContent);

                // اسکرول را قفل کن
                const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
                document.body.style.top = `-${scrollTop}px`;
                document.body.classList.add('modal-open');

                document.getElementById('deviceDetailsModal').style.display = 'flex';
            } else {
                alert('خطا در دریافت اطلاعات دست��اه.');
            }
        }).catch(() => alert('خطا در ارتباط با سرور'));
}

function closeDeviceModal() {
    const modal = document.getElementById('deviceDetailsModal');
    modal.style.display = 'none';

    // بازگرداندن اسکرول صفحه
    document.body.classList.remove('modal-open');
    const scrollTop = parseInt(document.body.style.top || '0') * -1;
    document.body.style.top = '';
    window.scrollTo(0, scrollTop);

    // Optional: Clear content on close
    const modalBody = document.getElementById('deviceDetailsModalBody');
    const closeButton = modalBody.querySelector('.close-btn');
    modalBody.innerHTML = '';
    modalBody.appendChild(closeButton);
}

document.getElementById('deviceDetailsModal').addEventListener('click', function(e) {
    if (e.target.id === 'deviceDetailsModal') {
        closeDeviceModal();
    }
});


function deleteDevice(id) {
    if (confirm('آیا از حذف این دستگاه اطمینان دارید؟')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = 'device_delete.php';
        
        const input = document.createElement('input');
        input.type = 'hidden';
        input.name = 'id';
        input.value = id;
        
        form.appendChild(input);
        document.body.appendChild(form);
        form.submit();
    }
}

document.getElementById('deviceSearch').addEventListener('input', function () {
    const query = this.value.toLowerCase();
    document.querySelectorAll('.device-list table tbody tr').forEach(row => {
        row.style.display = row.textContent.toLowerCase().includes(query) ? '' : 'none';
    });
});

// کد مربوط به submit فرم به صفحه device_add.php منتقل شده است

$(document).ready(function() {
    // کدهای مربوط به accordion به صفحه device_add.php منتقل شده است
});
// NEW JS FOR DEVICE HISTORY MODAL
const deviceHistoryModal = document.getElementById('device-history-modal');
const deviceHistoryContainer = document.getElementById('device-history-cards-container');
function openDeviceHistoryModal(deviceId) {
    // جلوگیری از اسکرول صفحه پشت
    const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
    document.body.style.top = `-${scrollTop}px`;
    document.body.classList.add('modal-open');

    deviceHistoryModal.style.display = 'flex';
    fetchAndRenderDeviceHistory(deviceId);
}
function closeDeviceHistoryModal() {
    deviceHistoryModal.style.display = 'none';

    // بازگرداندن اسکرول صفحه
    document.body.classList.remove('modal-open');
    const scrollTop = parseInt(document.body.style.top || '0') * -1;
    document.body.style.top = '';
    window.scrollTo(0, scrollTop);

    deviceHistoryContainer.innerHTML = '';
}
deviceHistoryModal.addEventListener('click', function(e) {
    if (e.target.id === 'device-history-modal') {
        closeDeviceHistoryModal();
    }
});
function fetchAndRenderDeviceHistory(deviceId) {
    deviceHistoryContainer.innerHTML = '<p style="text-align:center;">در حال بارگذاری...</p>';
    fetch(`devices.php?action=get_device_history&device_id=${deviceId}`)
        .then(res => res.json())
        .then(data => {
            if (!data.success) {
                deviceHistoryContainer.innerHTML = '<p class="no-tasks">خطا در بارگذاری اطلاعات.</p>';
                return;
            }
            if (data.history.length === 0) {
                deviceHistoryContainer.innerHTML = '<p class="no-tasks">سابقه‌ای یافت نشد.</p>';
                return;
            }
            let cardsHtml = '';
            data.history.forEach(task => {
                let statusClass = '';
                switch (task.status) {
                    case 'انجام شده': statusClass = 'status-انجام-شده'; break;
                    case 'منتظر تایید': statusClass = 'status-منتظر-تایید'; break;
                    case 'تایید شده': statusClass = 'status-تایید-شده'; break;
                    case 'بسته شده': statusClass = 'status-بسته-شده'; break;
                    case 'انجام و تایید شده': statusClass = 'status-done-confirmed'; break;
                    case 'رد شده': statusClass = 'status-rejected'; break;
                }
                const notes = task.completion_notes ? task.completion_notes.replace(/\n/g, '<br>') : 'ثبت نشده';
                const labor = task.labor_users || 'ثبت نشده';
                const parts = task.parts_used ? task.parts_used.replace(/,/g, '<br>') : 'ثبت نشده';
                const delayReason = task.delay_reason ? `<p><strong>دلیل تاخیر:</strong><br>${task.delay_reason.replace(/\n/g, '<br>')}</p>` : '';
                const verifier = task.verifier_name || 'ثبت نشده';
                const startDate = task.actual_start_datetime_shamsi || 'ثبت نشده';
                const endDate = task.actual_end_datetime_shamsi || 'ثبت نشده';

                // بررسی وجود اطلاعات برون‌سپاری
                const hasOutsourcing = task.exit_date || task.back_date;
                let outsourcingSection = '';

                if (hasOutsourcing) {
                    outsourcingSection = '<hr class="history-card-divider"><div class="outsourcing-section">';
                    outsourcingSection += '<h5 style="color: #007bff; margin-bottom: 10px;"><i class="fas fa-external-link-alt"></i> اطلاعات برون‌سپاری</h5>';

                    if (task.exit_date) {
                        outsourcingSection += `<p><strong>تاریخ خروج:</strong> ${task.exit_date_shamsi || task.exit_date}</p>`;
                        if (task.exit_where) {
                            outsourcingSection += `<p><strong>مقصد:</strong> ${task.exit_where}</p>`;
                        }
                        if (task.exit_desc) {
                            outsourcingSection += `<p><strong>توضیحات خروج:</strong><br>${task.exit_desc.replace(/\n/g, '<br>')}</p>`;
                        }
                    }

                    if (task.back_date) {
                        outsourcingSection += `<p><strong>تاریخ بازگشت:</strong> ${task.back_date_shamsi || task.back_date}</p>`;
                        if (task.back_desc) {
                            outsourcingSection += `<p><strong>توضیحات بازگشت:</strong><br>${task.back_desc.replace(/\n/g, '<br>')}</p>`;
                        }
                        if (task.back_pay) {
                            // تبدیل اعداد انگلیسی به فارسی
                            const persianAmount = task.back_pay.toString().replace(/[0-9]/g, function(w) {
                                return ['۰','۱','۲','۳','۴','۵','۶','۷','۸','۹'][w];
                            });
                            outsourcingSection += `<p><strong>مبلغ فاکتور:</strong> ${persianAmount} تومان</p>`;
                        }
                    } else if (task.exit_date) {
                        outsourcingSection += '<p><strong>وضعیت:</strong> <span style="color: #ffc107;">هنوز بازگشت داده نشده است</span></p>';
                    }

                    outsourcingSection += '</div>';
                }
                cardsHtml += `
                    <div class="history-card">
                        <div class="history-card-main">
                            <div class="history-card-summary">
                                <div class="history-card-title-line">
                                    <strong>${task.title}</strong>
                                    <div class="status-container">
                                        <span class="status-badge ${statusClass}">${task.status}</span>
                                        ${hasOutsourcing ? '<i class="fas fa-external-link-alt outsourcing-icon" title="شامل برون‌سپاری"></i>' : ''}
                                    </div>
                                </div>
                                <p><small>${startDate} - ${endDate}</small></p>
                            </div>
                            <button class="history-card-toggle"><i class="fas fa-chevron-down"></i></button>
                        </div>
                        <div class="history-card-collapsible-content">
                            <hr class="history-card-divider">
                            <div class="history-card-details">
                                <p><strong>شرح اقدامات:</strong><br>${notes}</p>
                                ${delayReason}
                                <p><strong>نفرات:</strong> ${labor}</p>
                                <p><strong>قطعات مصرفی:</strong><br>${parts}</p>
                                <p><strong>تحویل به:</strong> ${verifier}</p>
                            </div>
                            ${outsourcingSection}
                        </div>
                    </div>
                `;
            });
            deviceHistoryContainer.innerHTML = cardsHtml;
        })
        .catch(() => {
            deviceHistoryContainer.innerHTML = '<p class="no-tasks">خطای ارتباط با سرور.</p>';
        });
}
deviceHistoryContainer.addEventListener('click', function(e) {
    const header = e.target.closest('.history-card-main');
    if (header) {
        header.parentElement.classList.toggle('active');
    }
});
</script>
</body>
</html>
